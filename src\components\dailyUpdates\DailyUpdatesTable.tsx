// import React, { useEffect } from "react";
// import {
//   Table,
//   TableBody,
//   TableCell,
//   TableHead,
//   TableHeader,
//   TableRow,
// } from "@/components/ui/table";
// import { format } from "date-fns";
// import { DateRange } from "react-day-picker";
// import NoDataToShow from "../ui/noDataToShow";
// import { useInView } from "react-intersection-observer";
// import useGetDailyUpdates from "@/services/project/getDailyUpdates";
// import Loader from "../ui/loader";
// import ErrorText from "../ui/errortext";

// interface FileData {
//   fileName: string;
//   storagePath: string;
//   _id: string;
// }

// interface DailyUpdatesTableProps {
//   projectId: string;
//   dateRange?: DateRange | undefined;
// }

// const DailyUpdatesTable: React.FC<DailyUpdatesTableProps> = ({
//   projectId,
//   dateRange,
// }) => {
//   const { ref, inView } = useInView();
//   const {
//     data,
//     isLoading,
//     error,
//     fetchNextPage,
//     hasNextPage,
//     isFetchingNextPage,
//   } = useGetDailyUpdates(projectId, dateRange);

//   const parseFileData = (fileData: string | undefined): FileData[] => {
//     if (!fileData) return [];

//     try {
//       if (Array.isArray(fileData)) {
//         return fileData;
//       }

//       const parsed = JSON.parse(fileData);
//       return Array.isArray(parsed) ? parsed : [];
//     } catch (error) {
//       console.error("Error parsing file data:", error);
//       return [];
//     }
//   };

//   const handleFileDownload = async (fileData: string | undefined) => {
//     const files = parseFileData(fileData);
//     if (files.length === 0) return;

//     const s3BaseUrl = process.env.NEXT_PUBLIC_S3_URL;
//     if (!s3BaseUrl) {
//       console.error("S3 URL not configured");
//       return;
//     }

//     try {
//       files.forEach(async (file) => {
//         const fileUrl = `${s3BaseUrl}${file.storagePath}`;
//         const response = await fetch(fileUrl);
//         const blob = await response.blob();

//         const downloadUrl = window.URL.createObjectURL(blob);
//         const link = document.createElement("a");
//         link.href = downloadUrl;
//         link.download = file.fileName;
//         document.body.appendChild(link);
//         link.click();
//         document.body.removeChild(link);
//         window.URL.revokeObjectURL(downloadUrl);
//       });
//     } catch (error) {
//       console.error("Error downloading files:", error);
//     }
//   };

//   useEffect(() => {
//     if (inView && hasNextPage && !isFetchingNextPage) {
//       fetchNextPage();
//     }
//   }, [inView, fetchNextPage, hasNextPage, isFetchingNextPage]);

//   if (isLoading) return <Loader />;
//   if (error) return <ErrorText entity="daily updates" />;

//   const allUpdates = data?.pages.flatMap((page) => page.data) ?? [];

//   if (allUpdates.length === 0) {
//     return <NoDataToShow />;
//   }

//   return (
//     <div className="space-y-4">
//       <Table>
//         <TableHeader>
//           <TableRow>
//             <TableHead rowSpan={2} className="w-[113px]">
//               Date
//             </TableHead>
//             <TableHead rowSpan={2} className="w-[165px]">
//               Site Engineer
//             </TableHead>
//             <TableHead rowSpan={2} className="w-[166px]">
//               Milestone
//             </TableHead>
//             <TableHead rowSpan={2} className="w-[165px]">
//               Work type
//             </TableHead>
//             <TableHead
//               className="w-[130px] text-center p-0 pt-2 align-bottom"
//               colSpan={2}
//             >
//               Laborers(Nos)
//             </TableHead>
//             <TableHead className="text-center" rowSpan={2}>
//               Qty of work done
//             </TableHead>
//             <TableHead rowSpan={2} className="w-[57px]">
//               Unit
//             </TableHead>
//             <TableHead rowSpan={2} className="w-[73px]">
//               Image
//             </TableHead>
//             <TableHead rowSpan={2} className="w-[105px]">
//               Productivity difference
//             </TableHead>
//           </TableRow>
//           <TableRow>
//             <TableHead className="flex-1 flex-grow text-center bg-[#CBD5E1]">
//               Skilled
//             </TableHead>
//             <TableHead className="flex-1 flex-grow text-center bg-[#D3D5D8]">
//               Helper
//             </TableHead>
//           </TableRow>
//         </TableHeader>
//         <TableBody>
//           {allUpdates.map((row) => (
//             <TableRow key={row._id}>
//               <TableCell>
//                 {format(new Date(row.dailyUpdateDate), "dd/MM/yyyy")}
//               </TableCell>
//               <TableCell>{row.userId.name}</TableCell>
//               <TableCell>
//                 {row.milestoneTemplateId?.name || row.milestoneId.milestoneName}
//               </TableCell>
//               <TableCell>
//                 {row.worktypeTemplateId?.worktype ||
//                   row.worktypeId.worktypeName}
//               </TableCell>
//               <TableCell className="text-center">{row.workerCount}</TableCell>
//               <TableCell className="text-center">{row.helperCount}</TableCell>
//               <TableCell className="text-center">
//                 {row.preferedQuantity}
//               </TableCell>
//               <TableCell className="text-center">
//                 {row.preferedUnit.name}
//               </TableCell>
//               <TableCell>
//                 {parseFileData(row.file).length > 0 ? (
//                   <button
//                     onClick={() => handleFileDownload(row.file)}
//                     className="text-primary hover:text-primary-500 text"
//                   >
//                     View
//                   </button>
//                 ) : (
//                   <span className="text-gray-400">View</span>
//                 )}
//               </TableCell>
//               <TableCell
//                 className="text-center"
//                 style={{
//                   color:
//                     row.productivityDifference !== undefined
//                       ? row.productivityDifference >= 0
//                         ? "#22C55E"
//                         : "#EF4444"
//                       : "inherit",
//                 }}
//               >
//                 {row.productivityDifference !== undefined
//                   ? `${row.productivityDifference.toFixed(1)}%`
//                   : "NA"}
//               </TableCell>
//             </TableRow>
//           ))}
//         </TableBody>
//       </Table>

//       <div ref={ref} className="w-full h-20 flex items-center justify-center">
//         {isFetchingNextPage && <Loader />}
//       </div>
//     </div>
//   );
// };

// export default DailyUpdatesTable;

import React, { useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { format, subDays } from "date-fns";
import { DateRange } from "react-day-picker";
import NoDataToShow from "../ui/noDataToShow";
import { useInView } from "react-intersection-observer";
import useGetDailyUpdates from "@/services/project/getDailyUpdates";
import Loader from "../ui/loader";
import ErrorText from "../ui/errortext";

interface FileData {
  fileName: string;
  storagePath: string;
  _id: string;
}

interface DailyUpdatesTableProps {
  projectId: string;
  dateRange?: DateRange | undefined;
}

const DailyUpdatesTable: React.FC<DailyUpdatesTableProps> = ({
  projectId,
  dateRange,
}) => {
  const { ref, inView } = useInView();
  const {
    data,
    isLoading,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useGetDailyUpdates(projectId, dateRange);

  const parseFileData = (fileData: string | undefined): FileData[] => {
    if (!fileData) return [];
    try {
      if (Array.isArray(fileData)) return fileData;
      const parsed = JSON.parse(fileData);
      return Array.isArray(parsed) ? parsed : [];
    } catch (error) {
      console.error("Error parsing file data:", error);
      return [];
    }
  };

  const handleFileDownload = async (fileData: string | undefined) => {
    const files = parseFileData(fileData);
    if (files.length === 0) return;

    const s3BaseUrl = process.env.NEXT_PUBLIC_S3_URL;
    if (!s3BaseUrl) {
      console.error("S3 URL not configured");
      return;
    }

    try {
      files.forEach(async (file) => {
        const fileUrl = `${s3BaseUrl}${file.storagePath}`;
        const response = await fetch(fileUrl);
        const blob = await response.blob();
        const downloadUrl = window.URL.createObjectURL(blob);
        const link = document.createElement("a");
        link.href = downloadUrl;
        link.download = file.fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(downloadUrl);
      });
    } catch (error) {
      console.error("Error downloading files:", error);
    }
  };

  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, fetchNextPage, hasNextPage, isFetchingNextPage]);

  if (isLoading) return <Loader />;
  if (error) return <ErrorText entity="daily updates" />;

  const allUpdates = data?.pages.flatMap((page) => page.data) ?? [];
  if (allUpdates.length === 0) return <NoDataToShow />;

  const groupedUpdates = allUpdates.reduce(
    (acc, update) => {
      const date = format(new Date(update.dailyUpdateDate), "yyyy-MM-dd");
      if (!acc[date]) acc[date] = [];
      acc[date].push(update);
      return acc;
    },
    {} as Record<string, typeof allUpdates>,
  );
  return (
    <div className="space-y-2">
      {Object.entries(groupedUpdates).map(([date, updates]) => {
        const dateObj = new Date(date);
        const dateLabel =
          format(dateObj, "dd MMM yyyy") === format(new Date(), "dd MMM yyyy")
            ? `Today, ${format(dateObj, "dd MMM yyyy")}`
            : format(dateObj, "dd MMM yyyy") ===
                format(subDays(new Date(), 1), "dd MMM yyyy")
              ? `Yesterday, ${format(dateObj, "dd MMM yyyy")}`
              : format(dateObj, "dd MMM yyyy");

        return (
          <div
            key={date}
            className="h-full p-3 bg-white rounded-xl border border-border-gray1 flex-col justify-start items-start gap-4"
          >
            <div className="self-stretch px-3.5 py-2.5 w-full border-b border-[#e6e6e6] justify-start items-center gap-3 inline-flex">
              <div className="grow shrink basis-0 h-[19px] justify-between items-start flex">
                <div className="grow shrink basis-0 text-neutrals-G900 text-base font-semibold">
                  {dateLabel}
                </div>
              </div>
            </div>

            <div className="w-full">
              <Table>
                <TableHeader className="sticky top-0">
                  <TableRow className="*:h-auto *:pb-3 *:pt-4">
                    <TableHead className="w-1/8">Site Engineer</TableHead>
                    <TableHead className="w-1/8">Milestone</TableHead>
                    <TableHead className="w-1/8">Work type</TableHead>
                    <TableHead className="w-1/8">Laborers</TableHead>
                    <TableHead className="w-1/8">QTY OF Work DONE</TableHead>
                    <TableHead className="w-1/8 text-center">UNIT</TableHead>
                    <TableHead className="w-1/8">IMAGE</TableHead>
                    <TableHead className="w-1/8">Productivity</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {updates.map((row) => (
                    <TableRow key={row._id} className="*:align-top">
                      <TableCell className="w-1/8">{row.userId.name}</TableCell>
                      <TableCell className="w-1/8">
                        {row.milestoneTemplateId?.name ||
                          row.milestoneId.milestoneName}
                      </TableCell>
                      <TableCell className="w-1/8">
                        {row.worktypeTemplateId?.worktype ||
                          row.worktypeId.worktypeName}
                      </TableCell>
                      <TableCell className="w-1/8 ">
                        <div className="space-y-2">
                          <div>
                            <span className="font-semibold">
                              {row.workerCount}
                            </span>{" "}
                            skilled
                          </div>
                          <div>
                            <span className="font-semibold">
                              {row.helperCount}
                            </span>{" "}
                            helper
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="w-1/8">
                        {row.preferedQuantity}
                      </TableCell>
                      <TableCell className="w-1/8 text-center">
                        {row.preferedUnit.name}
                      </TableCell>
                      <TableCell className="w-1/8">
                        {parseFileData(row.file).length > 0 ? (
                          <button
                            onClick={() => handleFileDownload(row.file)}
                            className="text-primary-blue-B900 font-semibold hover:text-primary-blue-B900/80"
                          >
                            View
                          </button>
                        ) : (
                          <span className="text-gray-400">View</span>
                        )}
                      </TableCell>
                      <TableCell
                        className="w-1/8"
                        style={{
                          color: row.productivityDifference
                            ? row.productivityDifference >= 0
                              ? "#4caf50"
                              : "#cc0000"
                            : "inherit",
                        }}
                      >
                        {row.productivityDifference
                          ? `${row.productivityDifference >= 0 ? "+" : ""}${row.productivityDifference.toFixed(1)}%`
                          : "NA"}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        );
      })}

      <div ref={ref} className="w-full h-20 flex items-center justify-center">
        {isFetchingNextPage && <Loader />}
      </div>
    </div>
  );
};

export default DailyUpdatesTable;
