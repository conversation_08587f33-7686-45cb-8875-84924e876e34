import { useMutation, useQueryClient } from "@tanstack/react-query";
import api from "@/lib/api-client";
import { toast } from "sonner";

type UpdateContractorParams = {
  contractorId: string;
  data: {
    name: string;
    organisationName: string;
  };
};

const useUpdateContractor = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ contractorId, data }: UpdateContractorParams) => {
      const response = await api.put(`/contractors/${contractorId}`, data);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["contractors"] });
      toast.success("Contractor updated successfully");
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to update contractor",
      );
    },
  });
};

export default useUpdateContractor;
