// const AnalyticsCard = ({ children }: { children: React.ReactNode }) => {
//   return (
//     <div className="p-3.5 text-name-title rounded-xl border bg-white border-neutrals-G40 flex gap-x-3 shadow-lg">
//       {children}
//     </div>
//   );
// };

// const CardHeader = ({ children }: { children: React.ReactNode }) => {
//   return <div className="space-y-0.5">{children}</div>;
// };

// AnalyticsCard.Header = CardHeader;

// const CardTitle = ({ children }: { children: React.ReactNode }) => {
//   return (
//     <p className="font-semibold text-[28px] text-neutrals-G900">{children}</p>
//   );
// };

// AnalyticsCard.Title = CardTitle;

// const CardContent = ({ children }: { children: React.ReactNode }) => {
//   return <div className="text-[14px] text-neutrals-G600">{children}</div>;
// };

// AnalyticsCard.Content = CardContent;

// export default AnalyticsCard;
