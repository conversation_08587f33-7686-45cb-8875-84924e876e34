"use client";
import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import DeleteDocumentDialog from "./DeleteDocument";
import EditDocumentDialog from "./EditDocument";
import { format } from "date-fns";
import { SiteDocument } from "@/types/SiteDocuments";
import FileIcon from "../icons/File";
import { Plus } from "lucide-react";
import AddFileDialog from "./AddFile";

interface DocumentsTableProps {
  title: string;
  documents: SiteDocument[];
  profileId: string;
  onEdit: () => void;
}

export const DocumentsTable: React.FC<DocumentsTableProps> = ({
  title,
  documents,
  profileId,
  onEdit,
}) => {
  const [isAddFileOpen, setIsAddFileOpen] = useState(false);
  const S3_URL = process.env.NEXT_PUBLIC_S3_URL;

  const handleAddFile = () => {
    setIsAddFileOpen(true);
  };

  return (
    <div className="p-3 bg-white rounded-xl border border-border-gray1 flex flex-col mb-2">
      <div className="px-3.5 py-2.5 border-b border-[#e6e6e6] flex items-center">
        <h3 className="text-neutrals-G900 text-xl font-semibold">{title}</h3>
      </div>

      <Table>
        <TableHeader className="sticky top-0">
          <TableRow className="*:h-auto *:pb-3 *:pt-4">
            <TableHead className="w-[30%]">File Name</TableHead>
            <TableHead className="w-[20%]">Revision Number</TableHead>
            <TableHead className="w-[20%]">Applicable till</TableHead>
            <TableHead className="w-[20%]">Date of Upload</TableHead>
            <TableHead className="text-end w-[10%]"></TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {documents.map((doc) => {
            const fileUrl = `${S3_URL}${doc.storagePath}`;
            return (
              <TableRow key={doc._id}>
                <TableCell>
                  <span className="inline-flex items-center">
                    <FileIcon />
                    <a
                      href={fileUrl}
                      className="text-neutrals-G900 hover:underline cursor-pointer"
                      download={doc.fileName}
                      target="_blank"
                    >
                      {doc.fileName}
                    </a>
                  </span>
                </TableCell>
                <TableCell>
                  {doc.revisionNumber === 0 ? "0" : doc.revisionNumber}
                </TableCell>
                <TableCell>
                  {doc.milestoneId?.isCustom
                    ? doc.milestoneId.milestoneName
                    : doc.milestoneTemplateId?.name || "Not specified"}
                </TableCell>
                <TableCell>
                  {format(new Date(doc.uploadDate), "dd/MM/yyyy")}
                </TableCell>
                <TableCell className="text-center p-4">
                  <div className="flex justify-center items-center gap-3">
                    <EditDocumentDialog
                      document={doc}
                      profileId={profileId}
                      onEdit={onEdit}
                    />
                    <DeleteDocumentDialog
                      documentId={doc._id}
                      profileId={profileId}
                      isOpen={false}
                      onClose={() => console.log("Dialog closed")}
                      fileName={doc.fileName}
                      category={title}
                    />
                  </div>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>

      <div className="px-3.5 py-1.5">
        <button
          onClick={handleAddFile}
          className="flex items-center gap-1.5 text-primary text-sm font-medium hover:opacity-80"
        >
          <Plus className="w-[18px] h-[18px]" />
          Add file
        </button>
      </div>
      <AddFileDialog
        isOpen={isAddFileOpen}
        onClose={() => setIsAddFileOpen(false)}
        profileId={profileId}
        category={title}
        onFileAdded={onEdit}
      />
    </div>
  );
};

export default DocumentsTable;
