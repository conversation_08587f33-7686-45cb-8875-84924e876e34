import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import api from "@/lib/api-client";

const deleteOtherCost = async ({
  stageId,
  costId,
}: {
  stageId: string;
  costId: string;
}) => {
  const response = await api({
    url: `/design-stage/remove-cost/${stageId}/${costId}`,
    method: "DELETE",
  });

  return response.data;
};

const useDeleteOtherCost = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteOtherCost,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["design-stage"] });
      queryClient.invalidateQueries({ queryKey: ["design-stage-budget"] });
      toast.success("Cost removed successfully");
      onSuccess?.();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to delete other cost",
      );
    },
  });
};

export default useDeleteOtherCost;
