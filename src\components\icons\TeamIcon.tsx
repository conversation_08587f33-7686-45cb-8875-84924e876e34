import React from "react";

const TeamIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
  >
    <path
      d="M16.244 13.7581C15.7434 13.2571 15.1584 12.8482 14.516 12.5502C15.422 11.8162 16 10.6962 16 9.44025C16 7.22432 14.152 5.40637 11.936 5.44037C9.754 5.47437 7.99601 7.25232 7.99601 9.44025C7.99601 10.6962 8.57601 11.8162 9.48 12.5502C8.83743 12.848 8.25247 13.257 7.75201 13.7581C6.66002 14.8521 6.04002 16.2961 6.00003 17.836C5.99949 17.8574 6.00324 17.8786 6.01104 17.8985C6.01884 17.9183 6.03054 17.9365 6.04545 17.9517C6.06036 17.967 6.07818 17.9792 6.09786 17.9875C6.11753 17.9957 6.13867 18 6.16002 18H7.28002C7.36602 18 7.43802 17.932 7.44002 17.846C7.47802 16.686 7.94801 15.6001 8.77401 14.7761C9.19669 14.3512 9.69944 14.0143 10.2532 13.785C10.8069 13.5557 11.4006 13.4385 12 13.4401C13.218 13.4401 14.364 13.9141 15.226 14.7761C16.05 15.6001 16.52 16.686 16.56 17.846C16.562 17.932 16.634 18 16.72 18H17.84C17.8613 18 17.8824 17.9957 17.9021 17.9875C17.9218 17.9792 17.9396 17.967 17.9545 17.9517C17.9694 17.9365 17.9811 17.9183 17.9889 17.8985C17.9967 17.8786 18.0005 17.8574 18 17.836C17.96 16.2961 17.34 14.8521 16.244 13.7581ZM12 12.0002C11.316 12.0002 10.672 11.7342 10.19 11.2502C9.94811 11.0102 9.75703 10.724 9.62818 10.4085C9.49932 10.0931 9.43532 9.75496 9.44 9.41425C9.446 8.75827 9.708 8.12429 10.166 7.6543C10.646 7.16232 11.288 6.88833 11.974 6.88033C12.652 6.87433 13.31 7.13832 13.794 7.61231C14.29 8.09829 14.562 8.74827 14.562 9.44025C14.562 10.1242 14.296 10.7662 13.812 11.2502C13.5745 11.4888 13.2921 11.6779 12.9811 11.8066C12.6701 11.9354 12.3366 12.0012 12 12.0002ZM6.99002 9.96824C6.97202 9.79424 6.96202 9.61825 6.96202 9.44025C6.96202 9.12226 6.99202 8.81227 7.04802 8.51028C7.06202 8.43828 7.02402 8.36428 6.95802 8.33428C6.68602 8.21229 6.43602 8.04429 6.22002 7.8323C5.9655 7.58552 5.76522 7.2884 5.63196 6.95989C5.49869 6.63138 5.43537 6.2787 5.44603 5.92435C5.46403 5.28237 5.72203 4.67239 6.17202 4.2124C6.66602 3.70642 7.33002 3.43043 8.03601 3.43843C8.67401 3.44443 9.29 3.69042 9.756 4.12641C9.914 4.2744 10.05 4.4384 10.164 4.61439C10.204 4.67639 10.282 4.70239 10.35 4.67839C10.702 4.55639 11.074 4.4704 11.456 4.4304C11.568 4.4184 11.632 4.2984 11.582 4.1984C10.932 2.91244 9.604 2.02447 8.06801 2.00047C5.85003 1.96647 4.00204 3.78442 4.00204 5.99835C4.00204 7.25432 4.58003 8.37428 5.48603 9.10826C4.85003 9.40225 4.26404 9.80824 3.75604 10.3162C2.66005 11.4102 2.04005 12.8542 2.00005 14.3961C1.99952 14.4175 2.00326 14.4387 2.01106 14.4586C2.01886 14.4784 2.03056 14.4966 2.04547 14.5118C2.06039 14.5271 2.0782 14.5393 2.09788 14.5476C2.11756 14.5558 2.1387 14.5601 2.16005 14.5601H3.28204C3.36804 14.5601 3.44004 14.4921 3.44204 14.4061C3.48004 13.2461 3.95004 12.1602 4.77603 11.3362C5.36403 10.7482 6.08402 10.3402 6.87002 10.1422C6.94802 10.1222 7.00002 10.0482 6.99002 9.96824Z"
      fill="#787878"
    />
  </svg>
);

export default TeamIcon;
