"use client";
import { useParams } from "next/navigation";

import PageHeader from "@/components/layout/pageHeader/PageHeader";
import ProductivityDatabaseList from "@/components/productivityDatabase/ProductivityDatabaseList";
import Loader from "@/components/ui/loader";
import useGetProductivityDatabase from "@/services/productivityDatabase/getProductivityDatabase";
import ErrorText from "@/components/ui/errortext";
import NoDataToShow from "@/components/ui/noDataToShow";

const ProductivityDatabasePage = () => {
  const { id } = useParams() as { id: string };
  const { data, isPending, isError } = useGetProductivityDatabase({
    projectId: id,
  });

  if (isPending) return <Loader />;

  if (isError) return <ErrorText entity="productivity database" />;

  return (
    <div className="flex flex-col gap-y-6">
      <div className="space-y-1.5 text-neutrals-G800">
        <h4 className="font-semibold"> Productivity Database</h4>
        <p className="text-sm">
          Set the amount of work an individual has to do each day
        </p>
      </div>

      <div className="flex-grow overflow-auto scrollbar-hide">
        {data?.length === 0 ? (
          <NoDataToShow />
        ) : (
          <ProductivityDatabaseList data={data} />
        )}
      </div>
    </div>
  );
};

export default ProductivityDatabasePage;
