const SettingsIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
  >
    <path
      d="M6.75006 10.6875C7.24897 10.6874 7.73378 10.8531 8.12824 11.1585C8.52271 11.464 8.80446 11.8919 8.92918 12.375H15.1876C15.3281 12.3747 15.4637 12.4271 15.5676 12.5218C15.6714 12.6165 15.7361 12.7467 15.7488 12.8867C15.7615 13.0267 15.7213 13.1663 15.6362 13.2782C15.5511 13.39 15.4271 13.466 15.2888 13.491L15.1876 13.5L8.92918 13.5011C8.80488 13.9846 8.52331 14.413 8.1288 14.7189C7.7343 15.0248 7.24927 15.1908 6.75006 15.1908C6.25085 15.1908 5.76582 15.0248 5.37132 14.7189C4.97681 14.413 4.69525 13.9846 4.57094 13.5011L2.81256 13.5C2.672 13.5003 2.53643 13.4479 2.43255 13.3532C2.32868 13.2585 2.26403 13.1283 2.25133 12.9883C2.23862 12.8483 2.2788 12.7087 2.36393 12.5968C2.44907 12.485 2.57299 12.409 2.71131 12.384L2.81256 12.375H4.57094C4.69566 11.8919 4.97741 11.464 5.37188 11.1585C5.76634 10.8531 6.25115 10.6874 6.75006 10.6875ZM6.75006 11.8125C6.45169 11.8125 6.16554 11.931 5.95457 12.142C5.74359 12.353 5.62506 12.6391 5.62506 12.9375C5.62506 13.2359 5.74359 13.522 5.95457 13.733C6.16554 13.944 6.45169 14.0625 6.75006 14.0625C7.04843 14.0625 7.33458 13.944 7.54556 13.733C7.75653 13.522 7.87506 13.2359 7.87506 12.9375C7.87506 12.6391 7.75653 12.353 7.54556 12.142C7.33458 11.931 7.04843 11.8125 6.75006 11.8125ZM11.2501 2.8125C11.749 2.81237 12.2338 2.97807 12.6282 3.28354C13.0227 3.58901 13.3045 4.01693 13.4292 4.5H15.1876C15.3281 4.49974 15.4637 4.55212 15.5676 4.64682C15.6714 4.74152 15.7361 4.87169 15.7488 5.01167C15.7615 5.15166 15.7213 5.29134 15.6362 5.40319C15.5511 5.51504 15.4271 5.59096 15.2888 5.616L15.1876 5.625L13.4292 5.62612C13.3049 6.10961 13.0233 6.53802 12.6288 6.84392C12.2343 7.14983 11.7493 7.31584 11.2501 7.31584C10.7509 7.31584 10.2658 7.14983 9.87132 6.84392C9.47681 6.53802 9.19524 6.10961 9.07094 5.62612L2.81256 5.625C2.672 5.62526 2.53643 5.57288 2.43255 5.47818C2.32868 5.38348 2.26403 5.25332 2.25133 5.11333C2.23862 4.97334 2.2788 4.83366 2.36393 4.72181C2.44907 4.60996 2.57299 4.53404 2.71131 4.509L2.81256 4.5H9.07094C9.19566 4.01693 9.47741 3.58901 9.87188 3.28354C10.2663 2.97807 10.7511 2.81237 11.2501 2.8125ZM11.2501 3.9375C10.9517 3.9375 10.6655 4.05603 10.4546 4.267C10.2436 4.47798 10.1251 4.76413 10.1251 5.0625C10.1251 5.36087 10.2436 5.64702 10.4546 5.858C10.6655 6.06897 10.9517 6.1875 11.2501 6.1875C11.5484 6.1875 11.8346 6.06897 12.0456 5.858C12.2565 5.64702 12.3751 5.36087 12.3751 5.0625C12.3751 4.76413 12.2565 4.47798 12.0456 4.267C11.8346 4.05603 11.5484 3.9375 11.2501 3.9375Z"
      fill="currentColor"
    />
  </svg>
);

export default SettingsIcon;
