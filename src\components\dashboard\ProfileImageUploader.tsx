"use client";

import { useRef, useState, ChangeEvent, useEffect } from "react";
import { Upload, X, RefreshCw, Loader2 } from "lucide-react";
import { toast } from "sonner";
import Image from "next/image";

import {
  useGenerateS3Url,
  useUploadToS3,
} from "@/services/project/sitedocs-hooks";
import useUpdateProfile from "@/services/auth/updateUser";
import DeletePro from "../icons/DeletePro";
import ChnagePro from "../icons/ChnagePro";

type Props = {
  projectId: string;
  userData: any;
};

const ProfileImageUploader = ({ projectId, userData }: Props) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [imageUrl, setImageUrl] = useState<string | null>(
    userData?.user?.profileImage || null,
  );
  const [isUploading, setIsUploading] = useState(false);

  const { mutateAsync: generateS3Url } = useGenerateS3Url();
  const { mutateAsync: uploadToS3 } = useUploadToS3();
  const { mutateAsync: addProfileImage } = useUpdateProfile();

  useEffect(() => {
    if (userData?.user?.profileImage) {
      setImageUrl(userData.user.profileImage);
    }
  }, [userData]);

  const handleImageUpload = async (event: ChangeEvent<HTMLInputElement>) => {
    event.preventDefault();
    const file = event.target.files?.[0] || null;
    if (!file) return;

    if (!file.type.startsWith("image/")) {
      return toast.error("Only image files are allowed");
    }

    setIsUploading(true);

    try {
      const signedUrl = await generateS3Url({
        fileName: file.name,
        projectId,
      });

      await uploadToS3({
        signedUrl,
        file,
      });

      const s3Link = signedUrl.split("?")[0];
      setImageUrl(s3Link);

      await addProfileImage({
        profileImage: s3Link,
        name: userData?.user?.name,
        organisationName: userData?.organisation?.organisationName,
      });
    } catch (error: any) {
      console.error("Upload failed:", error);
      toast.error("Upload failed. Please try again.");
    } finally {
      setIsUploading(false);
      event.target.value = "";
    }
  };

  const triggerUpload = (e: React.MouseEvent) => {
    e.preventDefault();
    fileInputRef.current?.click();
  };

  const handleDelete = async (e: React.MouseEvent) => {
    e.preventDefault();
    setImageUrl(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }

    try {
      await addProfileImage({
        profileImage: null,
        name: userData?.user?.name,
        organisationName: userData?.organisation?.organisationName,
      });

      toast.success("Profile image removed");
    } catch (error) {
      console.error("Failed to remove profile image:", error);
      toast.error("Failed to update profile. Please try again.");
    }
  };

  return (
    <div className="flex flex-col items-center gap-3 p-[32px]">
      <div
        className="relative w-[244px] h-[244px] rounded-full border-2 border-dashed border-gray-300 flex items-center justify-center overflow-hidden cursor-pointer bg-gray-100 hover:border-primary transition"
        onClick={triggerUpload}
      >
        {isUploading ? (
          <Loader2 className="animate-spin text-gray-500 w-8 h-8" />
        ) : imageUrl ? (
          <Image
            src={imageUrl}
            alt="Profile"
            width={244}
            height={244}
            className="object-cover w-full h-full"
          />
        ) : (
          <Upload className="text-gray-400 w-8 h-8" />
        )}
      </div>

      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageUpload}
        className="hidden"
      />

      {!isUploading && imageUrl && (
        <div className="flex gap-[4px] mt-[24px]">
          <button
            onClick={(e) => handleDelete(e)}
            className="text-sm  hover:underline flex items-center gap-1 bg-[#E4EEFD] px-[24px] py-[8px] rounded-[4px]"
          >
            <DeletePro />
          </button>
          <button
            onClick={(e) => triggerUpload(e)}
            className="text-sm  hover:underline flex items-center gap-1 bg-[#E4EEFD] px-[24px] py-[8px] rounded-[4px]"
          >
            <ChnagePro />
          </button>
        </div>
      )}
    </div>
  );
};

export default ProfileImageUploader;
