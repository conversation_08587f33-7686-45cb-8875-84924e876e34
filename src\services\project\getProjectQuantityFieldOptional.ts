import { useQuery } from "@tanstack/react-query";
import api from "@/lib/api-client";

type GetProjectQuantityFieldOptional = { projectId: string };

const getProjectFieldOptional = async ({
  projectId,
}: GetProjectQuantityFieldOptional): Promise<{
  isQuantityFieldOptional: boolean;
}> => {
  const response = await api.get(
    `/projects/${projectId}/quantityFieldOptional`,
  );
  return response.data;
};

type UseGetProjectQuantityFieldOptional = GetProjectQuantityFieldOptional;

const useGetProjectQuantityFieldOptional = ({
  projectId,
}: UseGetProjectQuantityFieldOptional) => {
  return useQuery({
    queryKey: ["project-field-optional", projectId],
    queryFn: async () => getProjectFieldOptional({ projectId }),
    enabled: !!projectId,
  });
};

export default useGetProjectQuantityFieldOptional;
