import { EllipsisVertical } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdownMenu";
import DeleteIcon from "../icons/Delete";
import EditTeamMember from "./EditTeamMember";
import Edit2 from "../icons/Edit2";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogAction,
  AlertDialogCancel,
} from "@/components/ui/alertDialog";
import { Button } from "../ui/button";
import { TeamMember } from "@/types/TeamMember";
import useDeleteTeamMember from "@/services/teamMember/deleteTeamMember";

const TeamCardDropdownMenu = ({ data }: { data: TeamMember }) => {
  const [open, setOpen] = useState(false);

  const [isEditTeamMemberOpen, setIsEditTeamMemberOpen] = useState(false);
  const [isDeleteTeamMemberOpen, setIsDeleteTeamMemberOpen] = useState(false);

  const { mutate, isPending } = useDeleteTeamMember(() => {
    toast.success("Team member deleted successfully!");
    setIsDeleteTeamMemberOpen(false);
  });

  const handleEditTeamMemberClick = () => {
    setIsEditTeamMemberOpen(true);
    setOpen(false);
  };

  const handleDeleteClick = () => {
    setIsDeleteTeamMemberOpen(true);
    setOpen(false);
  };

  return (
    <>
      <DropdownMenu open={open} onOpenChange={setOpen}>
        <DropdownMenuTrigger className="right-4 top-4  text-neutrals-G200 absolute">
          <EllipsisVertical className="size-4" />
        </DropdownMenuTrigger>
        <DropdownMenuContent>
          <DropdownMenuItem
            onClick={handleEditTeamMemberClick}
            className="gap-x-1 [&_svg]:size-3.5"
          >
            <Edit2 />
            Edit
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={handleDeleteClick}
            className="gap-x-1 [&_svg]:size-3.5"
          >
            <DeleteIcon />
            Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <EditTeamMember
        data={data}
        open={isEditTeamMemberOpen}
        setOpen={() => setIsEditTeamMemberOpen(false)}
      />
      <AlertDialog
        open={isDeleteTeamMemberOpen}
        onOpenChange={setIsDeleteTeamMemberOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              Do you wish to delete this team member?
            </AlertDialogDescription>
          </AlertDialogHeader>

          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction asChild>
              <Button
                onClick={() => mutate({ userId: data.userId })}
                loading={isPending}
              >
                Delete
              </Button>
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default TeamCardDropdownMenu;
