import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";

import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  useGenerateS3Url,
  useUploadToS3,
  useAddSiteDocument,
} from "@/services/project/sitedocs-hooks";
import useGetMilestones from "@/services/milestone/getMilestones";
import { toast } from "sonner";
import CustomFileInput from "@/components/ui/fileUpload";
import { X } from "lucide-react";

const formSchema = z.object({
  file: z.instanceof(File, { message: "" }),
  milestoneId: z.string().min(1, ""),
});

type FormData = z.infer<typeof formSchema>;

type AddFileDialogProps = {
  isOpen: boolean;
  onClose: () => void;
  profileId: string;
  category: string;
  onFileAdded: () => void;
};

const AddFileDialog: React.FC<AddFileDialogProps> = ({
  isOpen,
  onClose,
  profileId,
  category,
  onFileAdded,
}) => {
  const [isFileReady, setIsFileReady] = useState(false);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
  });

  const { data: milestones } = useGetMilestones({ projectId: profileId });
  const generateS3Url = useGenerateS3Url();
  const uploadToS3 = useUploadToS3();
  const addSiteDocument = useAddSiteDocument(profileId);

  const handleClose = () => {
    form.reset();
    setIsFileReady(false);
    onClose();
  };

  const onSubmit = async (data: FormData) => {
    if (!isFileReady) {
      toast.error("Please upload a file first");
      return;
    }

    try {
      const signedUrl = await generateS3Url.mutateAsync({
        fileName: data.file.name,
        projectId: profileId,
      });

      await uploadToS3.mutateAsync({
        signedUrl,
        file: data.file,
      });

      const url = new URL(signedUrl);
      const storagePath = url.pathname;

      await addSiteDocument.mutateAsync({
        category,
        fileName: data.file.name,
        storagePath,
        milestoneId: data.milestoneId,
      });

      onClose();
      form.reset();
      setIsFileReady(false);
      onFileAdded();
      toast.success("Document uploaded successfully");
    } catch (error: any) {
      console.error("Error uploading document:", error);
      toast.error("Failed to upload document. Please try again.");
    }
  };

  const handleFileChange = (file: File | null) => {
    if (file) {
      form.setValue("file", file);
      setIsFileReady(false);
    }
  };

  const handleFileUpload = () => {
    setIsFileReady(true);
    toast.success("File ready for upload");
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[510px] p-6">
        <DialogTitle className="mb-[30px] flex items-center justify-between">
          Upload site documents in {category}
          <DialogClose asChild>
            <Button type="button" variant="outline" onClick={handleClose}>
              <X className="h-6 w-6" />
            </Button>
          </DialogClose>
        </DialogTitle>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="file"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-Grey-Dark-Grey text-sm">
                    Upload files
                  </FormLabel>
                  <FormControl>
                    <CustomFileInput
                      onFileChange={handleFileChange}
                      onFileUpload={handleFileUpload}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="milestoneId"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-Grey-Dark-Grey text-sm">
                    Applicable to milestones till
                  </FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="shadow-input">
                        <SelectValue placeholder="Select milestone" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {milestones?.map((milestone) => (
                        <SelectItem key={milestone._id} value={milestone._id}>
                          {milestone.isCustom
                            ? milestone.milestoneName
                            : milestone.milestoneTemplateId?.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-4">
              <Button
                type="submit"
                className="px-4 py-3 bg-primary text-white hover:bg-primary/90"
                loading={generateS3Url.isPending || uploadToS3.isPending}
                disabled={
                  !isFileReady ||
                  generateS3Url.isPending ||
                  uploadToS3.isPending
                }
              >
                Save
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AddFileDialog;
