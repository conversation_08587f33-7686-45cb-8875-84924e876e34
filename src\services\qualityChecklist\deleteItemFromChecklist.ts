import { toast } from "sonner";

import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";

type DeleteItemFromChecklist = {
  projectId: string;
  qualityChecklistId: string;
  questionId: string;
};

const deleteItemFromChecklist = async ({
  projectId,
  qualityChecklistId,
  questionId,
}: DeleteItemFromChecklist) => {
  const response = await api({
    url: `/quality-checklist/${projectId}/${qualityChecklistId}/question/${questionId}
`,
    method: "DELETE",
  });
  return response.data;
};

const useDeleteItemFromChecklist = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteItemFromChecklist,
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [
          "quality-checklist-by-id",
          variables.projectId,
          variables.qualityChecklistId,
        ],
      });
      if (onSuccess) onSuccess();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message ||
          "Failed to delete item from checklist",
      );
    },
  });
};

export default useDeleteItemFromChecklist;
