import { toast } from "sonner";

import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";

type UpdateProjectQuantityFieldOptional = {
  projectId: string;
  isQuantityFieldOptional: boolean;
};

const updateProjectQuantityFieldOptional = async ({
  projectId,
  ...body
}: UpdateProjectQuantityFieldOptional): Promise<{
  milestoneTemplateId: string;
}> => {
  const response = await api({
    url: `/projects/${projectId}/quantityFieldOptional`,
    method: "PATCH",
    data: body,
  });
  return response.data;
};

const useUpdateProjectQuantityFieldOptional = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateProjectQuantityFieldOptional,
    onSuccess: (_, { projectId }) => {
      queryClient.invalidateQueries({
        queryKey: ["project-field-optional", projectId],
      });
      if (onSuccess) onSuccess();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to update milestone",
      );
    },
  });
};

export default useUpdateProjectQuantityFieldOptional;
