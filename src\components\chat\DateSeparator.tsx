import { formatChatDate } from "@/lib/utils";

interface DateSeparatorProps {
  timestamp: string;
}

const DateSeparator = ({ timestamp }: DateSeparatorProps) => {
  return (
    <div className="flex justify-center py-4" data-date-separator={timestamp}>
      <div className="h-[35px] flex items-center justify-center rounded-full border border-neutrals-G30 bg-neutrals-G20 px-4 shadow-[0px_8px_20.2px_0px_#002F6C21]">
        <span className="text-sm text-neutrals-G600 font-medium">
          {formatChatDate(timestamp)}
        </span>
      </div>
    </div>
  );
};

export default DateSeparator;
