"use client";

import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import useGetUser from "@/services/auth/getUser";
import useUpdateProfile from "@/services/auth/updateUser";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import { toast } from "sonner";
import Loader from "../ui/loader";
import ErrorText from "../ui/errortext";
import ProfileImageUploader from "./ProfileImageUploader";

const formSchema = z.object({
  name: z.string().min(1, "Name is required"),
  email: z.string().email("Invalid email").min(1, "Email is required"),
  tagline: z.string().optional(),
  phone: z.string().optional(),
  website: z.string().optional(),
  organisationName: z.string().min(1, "Organization name is required"),
  location: z.string().optional(),
  //whatsappNo: z.string().optional(),
});

type FormData = z.infer<typeof formSchema>;

const ProfilePageForm = () => {
  const { data: userData, isLoading, isError } = useGetUser();
  const updateProfileMutation = useUpdateProfile();

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      tagline: userData?.organisation?.tagline || "",
      website: userData?.organisation?.website || "",
      organisationName: userData?.organisation?.name || "",
      location: userData?.organisation?.location || "",
      //whatsappNo: "",
    },
  });

  useEffect(() => {
    if (userData && userData.user) {
      form.reset({
        name: userData.user.name || "",
        email: userData.user.email || "",
        tagline: userData.organisation?.tagline || "",

        phone: userData.user.phone || "",
        website: userData.organisation?.website || "",
        organisationName: userData.organisation?.name || "",
        location: userData.organisation?.location || "",

        //whatsappNo: userData.user.whatsappNo || "",
      });
    }
  }, [userData, form]);

  const onSubmit = async (data: FormData) => {
    try {
      await updateProfileMutation.mutateAsync({
        name: data.name,
        phone: data.phone,
        tagline: data.tagline,
        website: data.website,
        organisationName: data.organisationName,
        profileImage: userData?.user?.profileImage,
        // whatsappNo: data.whatsappNo || "",
        location: data.location,
      });
      toast.success("Profile updated successfully!");
    } catch (error) {
      console.error("Failed to update profile:", error);
      toast.error("Failed to update profile. Please try again.");
    }
  };

  if (isLoading) {
    return <Loader />;
  }

  if (isError) {
    return <ErrorText entity="profile" />;
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="p-3 rounded-xl border border-border-gray1 space-y-4"
      >
        <div className="flex justify-between items-center px-3.5 py-2.5 gap-x-3 border-b border-[#E6E6E6]">
          <h3 className="text-neutrals-G800 font-semibold">Basic details</h3>
          <Button
            type="submit"
            disabled={
              updateProfileMutation.isPending || !form.formState.isDirty
            }
            loading={updateProfileMutation.isPending}
            className="px-4"
          >
            Save Changes
          </Button>
        </div>
        <div className="flex w-full justify-between items-center">
          <ProfileImageUploader
            projectId={userData?.user?._id ?? ""}
            userData={userData}
          />

          <div className="px-3.5 py-2 grid grid-cols-2 gap-6 w-full">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <Label htmlFor="name">Name</Label>
                  <FormControl>
                    <Input id="name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <Label htmlFor="email">Email</Label>
                  <FormControl>
                    <Input id="email" {...field} disabled readOnly />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="tagline"
              render={({ field }) => (
                <FormItem className="col-span-2">
                  <Label htmlFor="tagline">Tagline</Label>
                  <FormControl>
                    <Input
                      id="tagline"
                      {...field}
                      placeholder="Enter your tagline"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="phone"
              render={({ field }) => (
                <FormItem>
                  <Label htmlFor="phone">Phone</Label>
                  <FormControl>
                    <Input id="phone" type="number" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="website"
              render={({ field }) => (
                <FormItem>
                  <Label htmlFor="website">Website</Label>
                  <FormControl>
                    <Input id="website" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="organisationName"
              render={({ field }) => (
                <FormItem>
                  <Label htmlFor="organisationName">Organization name</Label>
                  <FormControl>
                    <Input id="organisationName" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <Label htmlFor="location">Location</Label>
                  <FormControl>
                    <Input id="location" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>
      </form>
    </Form>
  );
};

export default ProfilePageForm;
