import { toast } from "sonner";

import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";

type DeleteWorkType = {
  projectId: string;
  worktypeId: string;
};

const deleteWorkType = async ({ projectId, worktypeId }: DeleteWorkType) => {
  const response = await api({
    url: `/worktypes/${projectId}/${worktypeId}`,
    method: "DELETE",
  });
  return response.data;
};

const useDeleteWorkType = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteWorkType,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["worktypes"] });
      if (onSuccess) onSuccess();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to delete work type",
      );
    },
  });
};

export default useDeleteWorkType;
