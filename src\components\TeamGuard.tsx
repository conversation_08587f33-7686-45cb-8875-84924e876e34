/* eslint-disable react-hooks/exhaustive-deps */
"use client";

import { useAuth } from "@/app/contexts/AuthContext";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import type { ComponentType } from "react";

export default function withTeamGuard<T extends object>(
  Component: ComponentType<T>,
) {
  return function GuardedComponent(props: T) {
    const { isTeamMember, loading, isAdmin } = useAuth();
    const router = useRouter();

    useEffect(() => {
      if (!loading && isTeamMember && !isAdmin) {
        router.replace("/projects");
      }
    }, [loading, isTeamMember, isAdmin]);

    if (loading) return null;
    if (isTeamMember && !isAdmin) return null;

    return <Component {...props} />;
  };
}
