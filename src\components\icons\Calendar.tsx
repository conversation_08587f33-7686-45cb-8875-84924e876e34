import { cn } from "@/lib/utils";

const Calendar: React.FC<React.SVGProps<SVGElement>> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="21"
    height="20"
    fill="none"
    viewBox="0 0 21 20"
    className={cn("text-[#868686]", className)}
  >
    <path
      fill="currentColor"
      d="M13.833 2.5a.834.834 0 0 1 .828.736l.006.097v.834h1.666a1.667 1.667 0 0 1 1.663 1.541l.004.125v10a1.667 1.667 0 0 1-1.542 1.663l-.125.004H4.667a1.667 1.667 0 0 1-1.663-1.542L3 15.833v-10a1.667 1.667 0 0 1 1.542-1.662l.125-.004h1.666v-.834a.833.833 0 0 1 1.661-.097L8 3.333v.834h5v-.834a.833.833 0 0 1 .833-.833m2.5 7.5H4.667v5.833h11.666zm0-4.167H4.667v2.5h11.666z"
    ></path>
  </svg>
);

export default Calendar;
