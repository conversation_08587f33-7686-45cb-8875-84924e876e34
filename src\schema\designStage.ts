import { z } from "zod";
import { checkIsStartDateLowerThanOrEqualsToEndDate } from "@/lib/utils";

export const designStageSchema = z
  .object({
    projectId: z.string().min(1, { message: "" }),
    _id: z.string().optional(),
    stageName: z.string().min(1, { message: "" }),
    budgetAllocated: z.coerce.number({ message: "" }).min(0, { message: "" }),
    startDate: z.date({ required_error: "" }),
    endDate: z.date({ required_error: "" }),
    duration: z.number().min(1, { message: "" }),

    members: z.array(
      z.object({
        memberId: z.string().min(1, { message: "" }),
        name: z.string().min(1, { message: "" }),
        role: z.string().min(1, { message: "" }),
        hours: z.coerce.number({ message: "" }).min(0, { message: "" }),
      }),
    ),
  })
  .refine(
    (data) => {
      if (!data.startDate || !data.endDate) {
        return true;
      }
      return checkIsStartDateLowerThanOrEqualsToEndDate(
        new Date(data.startDate),
        new Date(data.endDate),
      );
    },
    {
      message: "End date cannot be lower than start date",
      path: ["endDate"],
    },
  );

// Dynamic schema factory that includes project timeline validation
export const createDesignStageSchemaWithProjectValidation = (
  projectStartDate?: Date,
  projectEndDate?: Date,
) => {
  return designStageSchema
    .refine(
      (data) => {
        if (!data.startDate || !projectStartDate) {
          return true;
        }
        return data.startDate >= projectStartDate;
      },
      {
        message:
          "Design stage start date cannot be earlier than project start date",
        path: ["startDate"],
      },
    )
    .refine(
      (data) => {
        if (!data.endDate || !projectEndDate) {
          return true;
        }
        return data.endDate <= projectEndDate;
      },
      {
        message: "Design stage end date cannot be later than project end date",
        path: ["endDate"],
      },
    );
};

export const otherCostSchema = z.object({
  stageId: z.string().min(1, { message: "" }),
  purpose: z.string().min(1, { message: "" }),
  amount: z.coerce.number({ message: "" }).min(1, { message: "" }),
});
