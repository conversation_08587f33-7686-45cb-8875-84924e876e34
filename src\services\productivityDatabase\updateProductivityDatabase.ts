import { toast } from "sonner";
import { z } from "zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";

import api from "@/lib/api-client";
import { productivityDatabaseSchema } from "@/schema/productivityDatabase";

type updateProductivityDatabase = z.infer<typeof productivityDatabaseSchema> & {
  projectId: string;
  worktypeId: string;
};

const updateProductivityDatabase = async ({
  projectId,
  worktypeId,
  ...body
}: updateProductivityDatabase): Promise<{ milestoneTemplateId: string }> => {
  const response = await api({
    url: `/productivity-tracker/${projectId}/${worktypeId}`,
    method: "PUT",
    data: body,
  });
  return response.data;
};

const useUpdateProductivityDatabase = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateProductivityDatabase,
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["productivity-database", variables.projectId],
      });
      if (onSuccess) onSuccess();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message ||
          "Failed to update productivity database",
      );
    },
  });
};

export default useUpdateProductivityDatabase;
