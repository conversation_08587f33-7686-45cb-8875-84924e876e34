import { useState, useEffect } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>ger,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormMessage,
  FormItem,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { z } from "zod";
import useUpdateContractor from "@/services/contractor/updateContractor";
import { Contractor } from "@/types/Contractor";
import EditIcon from "../icons/Edit";

const updateContractorSchema = z.object({
  name: z.string().min(1, ""),
  email: z.string().min(1, "").email(""),
  organisationName: z.string().min(1, ""),
});

type UpdateContractorFormData = z.infer<typeof updateContractorSchema>;

interface EditContractorProps {
  contractor: Contractor;
}

export function EditContractor({ contractor }: EditContractorProps) {
  const [open, setOpen] = useState(false);

  const form = useForm<UpdateContractorFormData>({
    defaultValues: {
      name: contractor.contractorName,
      email: contractor.email,
      organisationName: contractor.organisationName,
    },
  });

  useEffect(() => {
    if (open) {
      form.reset({
        name: contractor.contractorName,
        email: contractor.email,
        organisationName: contractor.organisationName,
      });
    }
  }, [open, contractor, form]);

  const { mutate: updateContractor, isPending } = useUpdateContractor();

  const onSubmit = (data: UpdateContractorFormData) => {
    updateContractor(
      {
        contractorId: contractor._id,
        data: {
          name: data.name,
          organisationName: data.organisationName,
        },
      },
      {
        onSuccess: () => {
          setOpen(false);
          form.reset();
        },
      },
    );
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <button onClick={() => setOpen(true)}>
          <EditIcon />
        </button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="text-center text-2xl mb-8">
            Edit Contractor
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input placeholder="Enter name" {...field} />
                  </FormControl>
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input
                      placeholder="Enter email"
                      type="email"
                      {...field}
                      disabled
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="organisationName"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input placeholder="Enter organisation name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end gap-4 pt-6">
              <Button
                type="button"
                variant="outline"
                className="w-1/2"
                onClick={() => {
                  setOpen(false);
                  form.reset();
                }}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                loading={isPending}
                disabled={!form.formState.isDirty}
                className="w-1/2"
              >
                Save changes
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
