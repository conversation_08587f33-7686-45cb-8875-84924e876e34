import React from "react";

const SiteDocumentIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
    className={className}
  >
    <path
      d="M11.8125 9.28582V15.3281C11.8125 16.1824 11.1428 16.875 10.3025 16.875H3.76172C2.91973 16.875 2.25 16.1824 2.25 15.3281V6.04688C2.2524 5.63736 2.41614 5.2453 2.70572 4.95572C2.9953 4.66614 3.38736 4.5024 3.79688 4.5H7.10578C7.22031 4.50017 7.33367 4.52314 7.43923 4.56757C7.5448 4.61199 7.64047 4.67698 7.72066 4.75875L11.558 8.66109C11.7212 8.82805 11.8126 9.05231 11.8125 9.28582Z"
      stroke="currentColor"
      strokeWidth="1.125"
      strokeLinejoin="round"
    />
    <path
      d="M7.03125 4.5V8.29688C7.03435 8.55698 7.13906 8.80556 7.323 8.9895C7.50694 9.17344 7.75552 9.27815 8.01562 9.28125H11.8125"
      stroke="currentColor"
      strokeWidth="1.125"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.1875 4.5V2.67188C6.1899 2.26236 6.35364 1.8703 6.64322 1.58072C6.9328 1.29114 7.32486 1.1274 7.73438 1.125H11.0391C11.1542 1.12516 11.2682 1.14811 11.3745 1.19251C11.4807 1.23692 11.5771 1.30192 11.6582 1.38375L15.4955 5.28609C15.6586 5.45128 15.75 5.6741 15.75 5.90625V11.9531C15.75 12.8074 15.0803 13.5 14.24 13.5H12.0938"
      stroke="currentColor"
      strokeWidth="1.125"
      strokeLinejoin="round"
    />
    <path
      d="M10.9688 1.125V4.92188C10.9719 5.18198 11.0766 5.43056 11.2605 5.6145C11.4444 5.79844 11.693 5.90315 11.9531 5.90625H15.75"
      stroke="currentColor"
      strokeWidth="1.125"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export default SiteDocumentIcon;
