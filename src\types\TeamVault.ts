import { folderSchema } from "@/schema/folder";
import { z } from "zod";

export type FolderModalProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  buttonText: string;
  initialValue?: string;
  onSubmit: (data: FolderFormData) => Promise<void>;
  loading: boolean;
};

export type Folder = {
  id: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
  parentId?: string;
};

export type FolderFormData = z.infer<typeof folderSchema>;

export type TeamVaultItem = {
  _id: string;
  name: string;
  type: "folder" | "file";
  s3: {
    url: string;
    mimeType: string;
    size: number;
  };
  createdAt: Date;
  itemsCount: number;
};
export type TeamVaultFolder = {
  _id: string;
  name: string;
  type: "folder" | "file";
  createdAt: Date;
  itemsCount: number;
};

export type getItemsFromTeamVaultResponse = {
  folders: TeamVaultFolder[];
  files: TeamVaultItem[];
};
