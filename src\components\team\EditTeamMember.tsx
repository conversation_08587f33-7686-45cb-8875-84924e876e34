import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON>etHeader,
  SheetTitle,
} from "@/components/ui/sheet";
import { teamMemberSchema } from "@/schema/teamMember";
import TeamMemberFormFields from "./TeamMemberFormFields";
import useUpdateTeamMember from "@/services/teamMember/updateTeamMeber";
import { TeamMember } from "@/types/TeamMember";

type EditTeamMemberProps = {
  data: TeamMember;
  open: boolean;
  setOpen: (bool: boolean) => void;
};

const EditTeamMember = ({ data, open, setOpen }: EditTeamMemberProps) => {
  const form = useForm<z.infer<typeof teamMemberSchema>>({
    resolver: zodResolver(teamMemberSchema),
    defaultValues: {
      name: data.name,
      email: data.email,
      role: data.role,
      salary: data.salary.toString(),
      phone: data.phone,
      isAdmin: data.isAdmin,
    },
  });

  useEffect(() => {
    if (open) {
      form.reset({
        name: data.name,
        email: data.email,
        role: data.role,
        salary: data.salary.toString(),
        phone: data.phone,
        isAdmin: data.isAdmin,
      });
      form.clearErrors();
    }
  }, [open, form, data]);

  const { mutate, isPending } = useUpdateTeamMember(() => {
    setOpen(false);
    toast.success("Team member updated successfully!");
  });

  const onSubmit = async (values: z.infer<typeof teamMemberSchema>) => {
    const { email: _, ...rest } = values;
    mutate({ ...rest, userId: data.userId });
  };

  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetContent className="max-w-[507px] flex flex-col gap-0">
        <SheetHeader>
          <SheetTitle>Edit Team Member</SheetTitle>
        </SheetHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col justify-between flex-1"
          >
            <div className="space-y-3">
              <TeamMemberFormFields form={form} isEdit={true} />
            </div>
            <Button
              className="ml-auto px-4 py-3 sticky bottom-0"
              type="submit"
              loading={isPending}
            >
              Save
            </Button>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  );
};

export default EditTeamMember;
