import axios from "axios";
import { useQuery, UseQueryOptions } from "@tanstack/react-query";
import { User } from "@/types/User";
import { Organisation } from "@/types/Organisation";

type UserData = {
  user: User;
  organisation: Organisation;
};

const getUserWithToken = async (token: string): Promise<any> => {
  const response = await axios.post<any>(
    `${process.env.NEXT_PUBLIC_API_URL}/auth/client`,
    {}, // no body
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  );
  return response.data;
};

const useGetUserWithToken = (
  token: string,
  options?: Partial<UseQueryOptions<any>>,
) => {
  return useQuery({
    queryKey: ["user", token],
    queryFn: () => getUserWithToken(token),
    staleTime: 0,
    enabled: !!token,
    ...options,
  });
};

export default useGetUserWithToken;
