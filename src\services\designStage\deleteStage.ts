import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";
const deleteDesignStage = async (stageId: string) => {
  const response = await api.delete(`/design-stage/delete/${stageId}`);
  return response.data;
};

export const useDeleteDesignStage = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (stageId: string) => deleteDesignStage(stageId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["design-stage"] });
    },
  });
};
