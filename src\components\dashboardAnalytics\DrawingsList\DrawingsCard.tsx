import { format } from "date-fns";

import DayOfCompletionStatusBadge from "@/components/projectAnalytics/DayOfCompletionStatusBadge";
import { Alert } from "@/types/DelayedDrawings";
import { MapPin } from "lucide-react";

type DrawingsCardProps = {
  alert: Alert;
};

const DrawingsCard = ({ alert }: DrawingsCardProps) => {
  return (
    <div className="flex justify-between items-center gap-x-4 p-3.5">
      <div className="space-y-1 basis-1/2">
        <div className="break-all text-neutrals-G900 font-semibold">
          {alert.milestoneTemplates?.name || alert.milestone?.milestoneName}
        </div>
        <div className="flex gap-x-1 items-start text-xs text-neutrals-G600">
          <div className="px-1 h-[23px] flex items-center rounded border border-neutrals-G50 bg-neutrals-G30">
            #ID {alert.project.projectId}
          </div>
          <div className="px-1 h-[23px]  items-center rounded border border-neutrals-G50 bg-neutrals-G30 flex gap-x-1">
            <MapPin className="size-3.5" /> {alert.project.location}
          </div>
        </div>
      </div>
      <div className="flex gap-x-1.5 items-center basis-1/2">
        <p className="text-xs text-neutrals-G600">
          {" "}
          {format(new Date(alert.milestone?.startDate), "dd MMM")}
        </p>
        <DayOfCompletionStatusBadge
          projectedEndDate={alert.delayedDate}
          expectedEndDate={alert.milestone?.startDate}
          className="shrink-0"
        />
      </div>
      {/* <div className="flex gap-x-16 items-center basis-1/2">
        <div className="space-y-1">
          <div className="text-sidebar-gray text-sm">Document category</div>
          <div className="font-semibold text-name-title">Drawing</div>
        </div>
        <div className="space-y-1">
          <div className="text-sidebar-gray text-sm">Expected upload date</div>
          <div className="font-semibold text-name-title flex gap-x-1">
            {format(new Date(alert.milestone?.startDate), "dd MMM yyyy")}
            <DayOfCompletionStatusBadge
              projectedEndDate={alert.delayedDate}
              expectedEndDate={alert.milestone?.startDate}
              className="shrink-0"
            />
          </div>
        </div>
      </div> */}
    </div>
  );
};

export default DrawingsCard;
