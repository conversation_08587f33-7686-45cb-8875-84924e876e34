"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";
import useGetUser from "@/services/auth/getUser";
import Loader from "./ui/loader";

const ProtectedRoutes: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const router = useRouter();

  const { data: user, isPending } = useGetUser();

  useEffect(() => {
    router.prefetch("/login");
  }, [router]);

  useEffect(() => {
    if (!isPending && !user) {
      console.log("run redirect from protected routes");
      router.push("/login");
    }
  }, [isPending, user, router]);

  if (isPending) {
    return <Loader />;
  }

  return <>{children}</>;
};

export default ProtectedRoutes;
