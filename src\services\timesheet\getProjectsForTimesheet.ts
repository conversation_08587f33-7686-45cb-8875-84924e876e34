import { useInfiniteQuery } from "@tanstack/react-query";

import api from "@/lib/api-client";
import { ProjectData } from "@/types/Project";

type UseGetProjectsForTimesheet = {
  search?: string;
  limit?: number;
};

type GetProjectsForTimesheet = UseGetProjectsForTimesheet & {
  pageParam?: number;
};

const getProjectsForTimesheet = async ({
  search,
  pageParam,
  limit,
}: GetProjectsForTimesheet): Promise<{
  data: {
    _id: string;
    name: string;
  }[];
  totalCount: number;
  limit: number;
}> => {
  return await api.get(
    `/time-sheet/get-projects?page=${pageParam}&limit=${limit}&search=${search}`,
  );
};

const useGetProjectsForTimesheet = ({
  search,
  limit = 10,
}: UseGetProjectsForTimesheet) => {
  return useInfiniteQuery({
    queryKey: ["time-sheet-projects", search, limit],
    queryFn: ({ pageParam = 1 }) =>
      getProjectsForTimesheet({ search, pageParam, limit }),
    getNextPageParam: (lastPage, allPages) => {
      const nextPage = allPages.length + 1;
      return lastPage.totalCount > allPages.length * lastPage.limit
        ? nextPage
        : undefined;
    },
    initialPageParam: 1,
  });
};

export default useGetProjectsForTimesheet;
