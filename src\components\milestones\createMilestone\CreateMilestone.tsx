import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { milestoneSchema } from "@/schema/milestone";
import { toast } from "sonner";
import { Plus, X } from "lucide-react";

import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import MilestoneFormFields from "../formFields/Milestone";
import useCreateMilestoneBulk from "@/services/milestone/createMilestoneBulk";
import {
  Sheet,
  SheetClose,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alertDialog";
import WorkType from "../formFields/WorkType";

type CreateMilestoneProps = {
  projectId: string;
};

type CreateMilestonePayload = {
  projectId: string;
  milestoneTemplateId?: string;
  milestoneName?: string;
  isCustom?: boolean;
  startDate?: string;
  duration?: number;
};

const CreateMilestone = ({ projectId }: CreateMilestoneProps) => {
  const [isCreateMilestoneOpen, setIsCreateMilestoneOpen] = useState(false);

  const form = useForm<z.infer<typeof milestoneSchema>>({
    resolver: zodResolver(milestoneSchema),
    defaultValues: {
      milestoneTemplateId: "",
      milestoneName: "",
      isCustom: false,
      duration: -1, // can't give 0, since we have to show placeholder by default and 0 is a valid value
      worktypes: [],
    },
  });

  const handleSuccess = () => {
    handleDialogClose();
    toast.success("Milestone created successfully");
  };

  const { mutate, isPending } = useCreateMilestoneBulk(handleSuccess);

  const handleDialogOpenChange = (open: boolean) => {
    setIsCreateMilestoneOpen(open);
    if (!open) {
      form.reset();
    }
  };

  const handleDialogClose = () => {
    handleDialogOpenChange(false);
  };

  const onSubmit = (data: z.infer<typeof milestoneSchema>) => {
    const payload: CreateMilestonePayload = {
      projectId,
    };

    if (data.startDate && data.endDate) {
      payload.startDate = data.startDate.toLocaleDateString("en-CA");
      payload.duration = data.duration ?? undefined;
    }

    if (data.isCustom) {
      payload.milestoneName = data.milestoneName;
      payload.isCustom = true;
    } else {
      payload.milestoneTemplateId = data.milestoneTemplateId;
    }

    const worktypes = data.worktypes?.map((worktype) => {
      // eslint-disable-next-line @typescript-eslint/no-unused-vars, no-unused-vars
      const { startDate, endDate, totalQty, optionalQty, ...rest } = worktype;
      const formattedWorktype = {
        ...rest,
        startDate: startDate
          ? startDate.toLocaleDateString("en-CA")
          : undefined,
        totalQuantity: Number(totalQty),
        totalQuantityOptionalUnit: Number(optionalQty.totalQty),
        optionalUnit: optionalQty.unit || undefined,
      };

      return formattedWorktype;
    });

    mutate({ ...payload, worktypes });
  };

  const isMilestoneSelected =
    !!form.watch("milestoneTemplateId") || !!form.watch("milestoneName");

  return (
    <Sheet open={isCreateMilestoneOpen} onOpenChange={setIsCreateMilestoneOpen}>
      <SheetTrigger asChild>
        <Button className="pl-4 py-2 pr-3 gap-x-2">
          Create milestone <Plus className="size-[22px] stroke-[2.5px]" />
        </Button>
      </SheetTrigger>
      <SheetContent
        isCloseIconVisible={false}
        className="max-w-[510px] gap-y-0 p-0 flex flex-col"
      >
        <SheetHeader className="px-6 pt-6 flex items-center justify-between flex-row space-y-0">
          <SheetTitle>Create milestone</SheetTitle>
          <AlertDialog>
            <AlertDialogTrigger>
              <div className="rounded-sm ring-offset-background transition-opacity focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-secondary">
                <X className="h-6 w-6 text-neutrals-G400" />
              </div>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Discard Milestone?</AlertDialogTitle>
                <AlertDialogDescription>
                  Doing this will remove all the data you entered for this
                  milestone
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction asChild>
                  <SheetClose asChild>
                    <Button>Yes, Discard </Button>
                  </SheetClose>
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </SheetHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex-1 flex flex-col"
          >
            <div className="px-6 mb-6 flex-1">
              <div className="flex flex-col gap-y-6">
                <MilestoneFormFields form={form} />
                {isMilestoneSelected && (
                  <div className="flex flex-col gap-y-6">
                    <div className="space-y-1.5 text-sm">
                      <h6 className="text-neutrals-G900 font-semibold">
                        Work types
                      </h6>
                      <p className="text-name-title">
                        Add sub tasks to be completed within a milestone.
                      </p>
                    </div>
                    <WorkType form={form} />
                  </div>
                )}
              </div>
            </div>
            <div className="px-6 py-3 flex justify-end sticky bottom-0 border border-neutrals-G40 bg-neutrals-G10">
              <Button
                loading={isPending}
                type="submit"
                className="w-[65px] px-4"
              >
                Save
              </Button>
            </div>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  );
};

export default CreateMilestone;
