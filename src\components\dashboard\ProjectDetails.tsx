"use client";

import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { useInView } from "react-intersection-observer";
import { z } from "zod";
import { format, differenceInDays } from "date-fns";
import { zodResolver } from "@hookform/resolvers/zod";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Button } from "@/components/ui/button";
import { ProjectData } from "@/types/Project";
import { Switch } from "@/components/ui/switch";
import useUpdateProject from "@/services/project/updateProject";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import useContractors from "@/services/contractor/getContractors";
import { LoadingSpinner } from "../ui/loader";
import CalendarIcon from "@/components/icons/Calendar";
import { projectDetailsSchema } from "@/schema/projectDetails";
import { useAuth } from "@/app/contexts/AuthContext";
import useDeleteProject from "@/services/project/deleteProject";
import { useRouter } from "next/navigation";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alertDialog";
import Tick2 from "@/components/icons/Tick2";
import DeleteIcon from "@/components/icons/Delete";

type FormData = z.infer<typeof projectDetailsSchema>;

type ProjectDetailsFormProps = {
  projectData: ProjectData;
  onFormStateChange: (isDirty: boolean) => void;
};

const ProjectDetailsForm: React.FC<ProjectDetailsFormProps> = ({
  projectData,
  onFormStateChange,
}) => {
  const router = useRouter();
  const updateProjectMutation = useUpdateProject();
  const [designCompleteDialogOpen, setDesignCompleteDialogOpen] =
    useState(false);
  const [constructionCompleteDialogOpen, setConstructionCompleteDialogOpen] =
    useState(false);
  const [deleteProjectDialogOpen, setDeleteProjectDialogOpen] = useState(false);
  const [qualityChecklistVisible, setQualityChecklistVisible] = useState(true);

  const handleDeleteSuccess = () => {
    router.push("/projects");
  };

  const deleteProjectMutation = useDeleteProject(handleDeleteSuccess);

  const stripCountryCode = (whatsappNo: string) => {
    if (whatsappNo.startsWith("+91")) {
      return whatsappNo.substring(3);
    }
    return whatsappNo;
  };

  const addCountryCode = (whatsappNo: string) => {
    if (whatsappNo && !whatsappNo.startsWith("+91")) {
      return `+91${whatsappNo}`;
    }
    return whatsappNo;
  };

  const form = useForm<FormData>({
    resolver: zodResolver(projectDetailsSchema),
    defaultValues: {
      name: projectData.name,
      projectType: (projectData as any).projectType || "Commercial",
      numberOfFloors: String((projectData as any).numberOfFloors || ""),
      projectScope: projectData.projectScope as
        | "design"
        | "construction"
        | "both",
      expectedRevenue: String((projectData as any).expectedRevenue || ""),
      requiredMargin: String((projectData as any).requiredMargin || ""),
      designStartDate: (projectData as any).designStartDate
        ? new Date((projectData as any).designStartDate)
        : undefined,
      designEndDate: (projectData as any).designEndDate
        ? new Date((projectData as any).designEndDate)
        : undefined,
      contractorStartDate: (projectData as any).contractorStartDate
        ? new Date((projectData as any).contractorStartDate)
        : undefined,
      contractorEndDate: (projectData as any).contractorEndDate
        ? new Date((projectData as any).contractorEndDate)
        : undefined,
      contractorOrg: projectData.contractorOrg?._id || "",
      clientName: projectData.clientName,
      clientWhatsAppNo: stripCountryCode(projectData.clientWhatsAppNo),
      location: projectData.location,
      status: projectData.status as "ongoing" | "completed" | "halted",
      startDate: projectData.startDate
        ? new Date(projectData.startDate)
        : undefined,
      endDate: projectData.endDate ? new Date(projectData.endDate) : undefined,
    },
    resetOptions: { keepDirtyValues: true },
  });

  const startDate = form.watch("startDate");
  const endDate = form.watch("endDate");

  const onSubmit = async (data: FormData) => {
    try {
      console.log("Form data received:", data);

      const duration =
        startDate && endDate
          ? differenceInDays(
              new Date(endDate.setHours(23, 59, 59)),
              new Date(startDate.setHours(0, 0, 0)),
            )
          : undefined;

      const payload = {
        id: projectData._id,
        // Basic Details
        name: data.name,
        projectType: data.projectType,
        numberOfFloors: data.numberOfFloors
          ? parseInt(data.numberOfFloors)
          : undefined,
        projectScope: data.projectScope,
        expectedRevenue: data.expectedRevenue
          ? parseFloat(data.expectedRevenue)
          : undefined,
        requiredMargin: data.requiredMargin
          ? parseFloat(data.requiredMargin)
          : undefined,
        // Timeline fields
        designStartDate: data.designStartDate
          ? format(data.designStartDate, "yyyy-MM-dd")
          : undefined,
        designEndDate: data.designEndDate
          ? format(data.designEndDate, "yyyy-MM-dd")
          : undefined,
        contractorStartDate: data.contractorStartDate
          ? format(data.contractorStartDate, "yyyy-MM-dd")
          : undefined,
        contractorEndDate: data.contractorEndDate
          ? format(data.contractorEndDate, "yyyy-MM-dd")
          : undefined,
        // Client Details
        clientName: data.clientName,
        clientWhatsAppNo: data.clientWhatsAppNo
          ? addCountryCode(data.clientWhatsAppNo)
          : undefined,
        location: data.location,
        // Other fields
        contractorOrg: data.contractorOrg,
        status: data.status,
        startDate: data.startDate
          ? format(data.startDate, "yyyy-MM-dd")
          : undefined,
        endDate: data.endDate ? format(data.endDate, "yyyy-MM-dd") : undefined,
        duration,
      } as any;

      console.log("Project update payload:", payload);

      await updateProjectMutation.mutateAsync(payload);
    } catch (error) {
      console.error("Failed to update project:", error);
    }
  };

  useEffect(() => {
    form.reset({
      name: projectData.name,
      projectType: (projectData as any).projectType || "Commercial",
      numberOfFloors: String((projectData as any).numberOfFloors || ""),
      projectScope: projectData.projectScope as
        | "design"
        | "construction"
        | "both",
      expectedRevenue: String((projectData as any).expectedRevenue || ""),
      requiredMargin: String((projectData as any).requiredMargin || ""),
      designStartDate: (projectData as any).designStartDate
        ? new Date((projectData as any).designStartDate)
        : undefined,
      designEndDate: (projectData as any).designEndDate
        ? new Date((projectData as any).designEndDate)
        : undefined,
      contractorStartDate: (projectData as any).contractorStartDate
        ? new Date((projectData as any).contractorStartDate)
        : undefined,
      contractorEndDate: (projectData as any).contractorEndDate
        ? new Date((projectData as any).contractorEndDate)
        : undefined,
      contractorOrg: projectData.contractorOrg?._id || "",
      clientName: projectData.clientName,
      clientWhatsAppNo: stripCountryCode(projectData.clientWhatsAppNo),
      location: projectData.location,
      status: projectData.status as "ongoing" | "completed" | "halted",
      startDate: projectData.startDate
        ? new Date(projectData.startDate)
        : undefined,
      endDate: projectData.endDate ? new Date(projectData.endDate) : undefined,
    });
  }, [projectData, form]);

  useEffect(() => {
    onFormStateChange(form.formState.isDirty);
  }, [form.formState.isDirty, onFormStateChange]);

  const { ref, inView } = useInView();

  const {
    contractorOptions,
    isLoading,
    isFetchingNextPage,
    fetchNextPage,
    hasNextPage,
  } = useContractors({
    limit: 10,
  });

  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, fetchNextPage, hasNextPage, isFetchingNextPage]);

  const { isTeamMember: isTeam, isAdmin } = useAuth();

  if (isTeam === null) return null;

  const projectScope = form.watch("projectScope");

  const handleMarkDesignComplete = () => {
    // TODO: Implement mark design as complete API call
    console.log("Mark design as complete");
    setDesignCompleteDialogOpen(false);
  };

  const handleMarkConstructionComplete = () => {
    // TODO: Implement mark construction as complete API call
    console.log("Mark construction as complete");
    setConstructionCompleteDialogOpen(false);
  };

  const handleDeleteProject = () => {
    deleteProjectMutation.mutate(projectData._id);
    setDeleteProjectDialogOpen(false);
  };

  return (
    <Form {...form}>
      <form
        id="projectDetailsForm"
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-6"
      >
        {/* Basic Details Section */}
        <div className="border border-border-gray1 rounded-xl">
          <h5 className="px-6 py-4 text-neutrals-G800 font-semibold border-b border-[#E6E6E6]">
            Basic details
          </h5>
          <div className="p-6 space-y-6">
            {/* Project name - single row */}
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Project name</FormLabel>
                  <FormControl>
                    <Input
                      disabled={!isAdmin}
                      className="text-base text-name-title"
                      placeholder="Project Name"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Type of project and No. of Floors - same row */}
            <div className="grid grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="projectType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Type of project</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger
                        disabled={!isAdmin}
                        className="data-[placeholder]:text-name-title text-name-title font-medium"
                      >
                        <SelectValue placeholder="Select project type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="Commercial">Commercial</SelectItem>
                        <SelectItem value="Residential">Residential</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="numberOfFloors"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>No. of Floors</FormLabel>
                    <FormControl>
                      <Input
                        disabled={!isAdmin}
                        type="number"
                        className="text-base text-name-title"
                        placeholder="Enter number of floors"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Project Scope - single row with radio buttons */}
            <FormField
              control={form.control}
              name="projectScope"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Project Scope</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="grid grid-cols-3"
                    >
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="design" />
                        </FormControl>
                        <FormLabel>Design</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="construction" />
                        </FormControl>
                        <FormLabel>Construction</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2 space-y-0">
                        <FormControl>
                          <RadioGroupItem value="both" />
                        </FormControl>
                        <FormLabel>Both</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Expected revenue and Margin Required - same row */}
            <div className="grid grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="expectedRevenue"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Expected revenue</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[#49515b] text-base">
                          Rs
                        </span>
                        <Input
                          disabled={!isAdmin}
                          type="number"
                          className="text-base text-name-title pl-12"
                          placeholder="40000"
                          {...field}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="requiredMargin"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Margin Required</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          disabled={!isAdmin}
                          type="number"
                          className="text-base text-name-title pr-8"
                          placeholder="50"
                          {...field}
                        />
                        <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-[#49515b] text-base">
                          %
                        </span>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        </div>

        {/* Design Section - Show if projectScope includes design */}
        {(projectScope === "design" || projectScope === "both") && (
          <div className="border border-border-gray1 rounded-xl">
            <div className="px-6 py-4 border-b border-[#E6E6E6] flex justify-between items-center">
              <h5 className="text-neutrals-G800 font-semibold">Design</h5>
              <Button
                onClick={() => setDesignCompleteDialogOpen(true)}
                className="px-3 h-9 font-medium gap-x-1.5"
              >
                <Tick2 />
                Mark as completed
              </Button>
            </div>
            <div className="p-6 space-y-6">
              {/* Design Timeline - Start and End Date */}
              <div className="grid grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="designStartDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Design Start Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              disabled={!isAdmin}
                              variant="outline"
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground",
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date < new Date("1900-01-01")}
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="designEndDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Design End Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              disabled={!isAdmin}
                              variant="outline"
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground",
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date < new Date("1900-01-01")}
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Set Project Status</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger
                        disabled={!isAdmin}
                        className="data-[placeholder]:text-name-title text-name-title font-medium"
                      >
                        <SelectValue placeholder="Project Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ongoing">Ongoing</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="halted">Halted</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        )}

        {/* Construction Section - Show if projectScope includes construction */}
        {(projectScope === "construction" || projectScope === "both") && (
          <div className="border border-border-gray1 rounded-xl">
            <div className="px-6 py-4 border-b border-[#E6E6E6] flex justify-between items-center">
              <h5 className="text-neutrals-G800 font-semibold">Construction</h5>
              <Button
                onClick={() => setConstructionCompleteDialogOpen(true)}
                className="px-3 h-9 font-medium gap-x-1.5"
              >
                <Tick2 />
                Mark as completed
              </Button>
            </div>
            <div className="p-6 grid grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="contractorOrg"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <>
                        <FormLabel>Select Contractor</FormLabel>
                        <Select
                          onValueChange={field.onChange}
                          value={field.value || ""}
                        >
                          <SelectTrigger
                            disabled={!isAdmin}
                            loading={isLoading}
                            className="data-[placeholder]:text-name-title text-name-title font-medium"
                          >
                            <SelectValue placeholder="Select">
                              {field.value === projectData.contractorOrg?._id
                                ? projectData.contractorOrg.name
                                : contractorOptions.find(
                                    (contractor) =>
                                      contractor.value === field.value,
                                  )?.label}
                            </SelectValue>
                          </SelectTrigger>
                          <SelectContent>
                            {contractorOptions.length > 0 ? (
                              <>
                                {contractorOptions.map((contractor) => (
                                  <SelectItem
                                    key={contractor.value}
                                    value={contractor.value}
                                  >
                                    {contractor.label}
                                  </SelectItem>
                                ))}
                                <SelectItem
                                  ref={ref}
                                  value={"none"}
                                  disabled
                                  className={cn(
                                    "data-[disabled]:opacity-100 justify-center ",
                                    isFetchingNextPage ? "mb-1" : "h-0",
                                  )}
                                >
                                  {isFetchingNextPage && (
                                    <LoadingSpinner
                                      size={4}
                                      className="border-2 "
                                    />
                                  )}
                                </SelectItem>
                              </>
                            ) : (
                              <SelectItem value="none" disabled>
                                No contractors found
                              </SelectItem>
                            )}
                          </SelectContent>
                        </Select>
                      </>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Project Status</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <SelectTrigger
                        disabled={!isAdmin}
                        className="data-[placeholder]:text-name-title text-name-title font-medium"
                      >
                        <SelectValue placeholder="Project Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="ongoing">Ongoing</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="halted">Halted</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Construction Timeline - Start and End Date */}
              <div className="grid grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="contractorStartDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Construction Start Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              disabled={!isAdmin}
                              variant="outline"
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground",
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date < new Date("1900-01-01")}
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contractorEndDate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Construction End Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              disabled={!isAdmin}
                              variant="outline"
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground",
                              )}
                            >
                              {field.value ? (
                                format(field.value, "PPP")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => date < new Date("1900-01-01")}
                          />
                        </PopoverContent>
                      </Popover>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </div>
        )}

        {/* Client Details Section */}
        <div className="border border-border-gray1 rounded-xl">
          <h5 className="px-6 py-4 text-neutrals-G800 font-semibold border-b border-[#E6E6E6]">
            Client details
          </h5>
          <div className="p-6 grid grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="clientName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Client name</FormLabel>
                  <FormControl>
                    <Input
                      disabled={!isAdmin}
                      className="text-base text-name-title"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="space-y-2">
              <label className="text-sm font-medium text-neutrals-G700">
                Quality Checklist Visibility
              </label>
              <Switch
                checked={qualityChecklistVisible}
                onCheckedChange={setQualityChecklistVisible}
              />
            </div>

            <FormField
              control={form.control}
              name="clientWhatsAppNo"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Client Phone</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <span className="absolute left-3 mt-[1px] top-1/2 transform -translate-y-1/2 text-[#49515b] text-base">
                        +91
                      </span>
                      <Input
                        disabled={!isAdmin}
                        type="number"
                        className="text-base text-name-title pl-12"
                        placeholder="Enter 10-digit number"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="location"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Location</FormLabel>
                  <FormControl>
                    <Input
                      disabled={!isAdmin}
                      className="text-base text-name-title"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Critical Actions Section */}
        <div className="border border-red-200 rounded-xl bg-red-50">
          <h5 className="px-6 py-4 text-red-800 font-semibold border-b border-red-200">
            Critical Actions
          </h5>
          <div className="p-6">
            <div className="flex items-center justify-between p-4 bg-white border border-red-200 rounded-lg">
              <div className="flex items-center gap-3">
                <DeleteIcon className="fill-red-500" />
                <div>
                  <h6 className="font-semibold text-red-800">
                    Delete this project permanently
                  </h6>
                  <p className="text-sm text-red-600">
                    This action cannot be undone.
                  </p>
                </div>
              </div>
              <Button
                variant="destructive"
                onClick={() => setDeleteProjectDialogOpen(true)}
                className="px-4"
                disabled={deleteProjectMutation.isPending}
              >
                {deleteProjectMutation.isPending
                  ? "Deleting..."
                  : "Delete Project"}
              </Button>
            </div>
          </div>
        </div>

        {/* Mark Design as Complete Dialog */}
        <Dialog
          open={designCompleteDialogOpen}
          onOpenChange={setDesignCompleteDialogOpen}
        >
          <DialogContent className="w-full max-w-[438px] rounded-[12px] p-[24px]">
            <DialogHeader>
              <DialogTitle className="text-[20px] font-[600] text-[#1E1E1E]">
                Mark Design as Complete?
              </DialogTitle>
            </DialogHeader>
            <p className="text-[#474747]">
              Once marked as complete, this action cannot be undone. Are you
              sure you want to proceed?
            </p>
            <DialogFooter className="mt-4">
              <Button
                className="font-[600] text-[#6B6B6B] bg-[#E2E2E2] px-[16px] rounded-[8px]"
                variant="outline"
                onClick={() => setDesignCompleteDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                className="rounded-[8px] px-[16px]"
                onClick={handleMarkDesignComplete}
              >
                Yes, mark as complete
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Mark Construction as Complete Dialog */}
        <Dialog
          open={constructionCompleteDialogOpen}
          onOpenChange={setConstructionCompleteDialogOpen}
        >
          <DialogContent className="w-full max-w-[438px] rounded-[12px] p-[24px]">
            <DialogHeader>
              <DialogTitle className="text-[20px] font-[600] text-[#1E1E1E]">
                Mark Construction as Complete?
              </DialogTitle>
            </DialogHeader>
            <p className="text-[#474747]">
              Once marked as complete, this action cannot be undone. Are you
              sure you want to proceed?
            </p>
            <DialogFooter className="mt-4">
              <Button
                className="font-[600] text-[#6B6B6B] bg-[#E2E2E2] px-[16px] rounded-[8px]"
                variant="outline"
                onClick={() => setConstructionCompleteDialogOpen(false)}
              >
                Cancel
              </Button>
              <Button
                className="rounded-[8px] px-[16px]"
                onClick={handleMarkConstructionComplete}
              >
                Yes, mark as complete
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Delete Project Dialog */}
        <AlertDialog
          open={deleteProjectDialogOpen}
          onOpenChange={setDeleteProjectDialogOpen}
        >
          <AlertDialogContent className="w-full max-w-[438px] rounded-[12px] p-[24px]">
            <AlertDialogHeader>
              <AlertDialogTitle className="text-[20px] font-[600] text-[#1E1E1E]">
                Delete Project
              </AlertDialogTitle>
              <AlertDialogDescription className="text-[#474747]">
                Are you sure you want to permanently delete this project? This
                action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter className="mt-4">
              <AlertDialogCancel className="font-[600] text-[#6B6B6B] bg-[#E2E2E2] px-[16px] rounded-[8px]">
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction
                className="rounded-[8px] px-[16px] bg-red-600 hover:bg-red-700"
                onClick={handleDeleteProject}
                disabled={deleteProjectMutation.isPending}
              >
                {deleteProjectMutation.isPending
                  ? "Deleting..."
                  : "Yes, Delete"}
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </form>
    </Form>
  );
};

export default ProjectDetailsForm;
