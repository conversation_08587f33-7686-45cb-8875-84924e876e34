import { useQuery } from "@tanstack/react-query";

import api from "@/lib/api-client";
import { ArchitectWorklog } from "@/types/ArchitectWorklog";

type GetArchitectWorklog = {
  projectId: string;
  startDate: string;
  endDate: string;
};

export type UseGetArchitectWorklog = GetArchitectWorklog;

const getArchitectWorklog = async ({
  projectId,
  startDate,
  endDate,
}: GetArchitectWorklog): Promise<ArchitectWorklog[]> => {
  const response = await api.get(
    `/time-sheet/get-project-sheets/${projectId}`,
    {
      params: {
        startDate,
        endDate,
      },
    },
  );

  return response.data;
};

const useGetArchitectWorklog = ({
  projectId,
  startDate,
  endDate,
}: UseGetArchitectWorklog) => {
  return useQuery({
    queryKey: ["architect-worklog", projectId, startDate, endDate],
    queryFn: () => getArchitectWorklog({ projectId, startDate, endDate }),
    enabled: !!projectId,
  });
};

export default useGetArchitectWorklog;
