"use client";

import useGetMilestones from "@/services/milestone/getMilestones";
import { useParams } from "next/navigation";

import MilestonesList from "@/components/milestones/milestonesList/MilestonesList";
import CreateMilestone from "@/components/milestones/createMilestone/CreateMilestone";
import withTeamGuard from "@/components/TeamGuard";

const MilestonesPage = () => {
  const { id } = useParams() as { id: string };
  const { data } = useGetMilestones({ projectId: id });

  return (
    <div className="flex flex-col gap-y-6">
      <div className="flex justify-between items-center">
        <div className="space-y-1.5 text-neutrals-G800">
          <h4 className="font-semibold">Milestones</h4>
          <p className="text-sm">
            You have created{" "}
            <strong className="font-bold">{data?.length}</strong> milestone
            {data?.length === 1 ? "" : "s"}
          </p>
        </div>
        <CreateMilestone projectId={id} />
      </div>
      <div className="flex-1">
        <MilestonesList />
      </div>
    </div>
  );
};

export default withTeamGuard(MilestonesPage);
