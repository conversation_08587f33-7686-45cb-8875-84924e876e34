import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Plus } from "lucide-react";
import { toast } from "sonner";

import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormItem,
  FormField,
  FormMessage,
  FormLabel,
} from "@/components/ui/form";
import { checklistItemSchema } from "@/schema/checklist";
import { Input } from "../ui/input";
import useAddItemToChecklist from "@/services/qualityChecklist/addItemToChecklist";

type AddNewItemToChecklistProps = {
  projectId: string;
  qualityChecklistId: string;
};

const AddNewItemToChecklist = ({
  projectId,
  qualityChecklistId,
}: AddNewItemToChecklistProps) => {
  const [isAddNewItemToChecklistOpen, setIsAddNewItemToChecklistOpen] =
    useState(false);

  const form = useForm<z.infer<typeof checklistItemSchema>>({
    resolver: zodResolver(checklistItemSchema),
  });

  const { mutate, isPending } = useAddItemToChecklist(() => {
    onOpenChange(false);
    toast.success("Item added successfully");
  });

  const onSubmit = (data: z.infer<typeof checklistItemSchema>) => {
    console.log(data);
    mutate({
      projectId,
      qualityChecklistId,
      question: data.question,
    });
  };

  const onOpenChange = (open: boolean) => {
    if (!open) {
      form.reset();
    }
    setIsAddNewItemToChecklistOpen(open);
  };

  return (
    <Dialog open={isAddNewItemToChecklistOpen} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        <Button
          type="button"
          variant="link"
          className="hover:no-underline justify-start gap-x-1.5 font-medium h-[30px] px-3.5 pb-3"
        >
          <Plus className="size-4  stroke-[2.5px]" />
          Add question
        </Button>
      </DialogTrigger>
      <DialogContent isCloseIconVisible className="gap-y-[30px]">
        <DialogTitle>Add question</DialogTitle>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col gap-y-6"
          >
            <FormField
              control={form.control}
              name="question"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Enter Question</FormLabel>
                  <FormControl>
                    <Input placeholder="Type here" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button loading={isPending} type="submit" className="ml-auto px-4">
              Save
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AddNewItemToChecklist;
