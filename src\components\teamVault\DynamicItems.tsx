"use client";
import { getFileCategory } from "@/lib/utils";
import {
  FolderFormData,
  TeamVaultFolder,
  TeamVaultItem,
} from "@/types/TeamVault";
import FolderIcon from "../icons/FolderIcon";
import PdfIcon from "../icons/Pdf";
import { Ellip<PERSON>, Trash2 } from "lucide-react";
import { useState } from "react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdownMenu";
import FolderModal from "./FolderModal";
import Edit2 from "../icons/Edit2";
import DeleteModal from "./DeleteModal";
import Image from "next/image";
import Link from "next/link";
import useDeleteContentFromTeamVault from "@/services/teamVault/deleteContent";
import Loader from "../ui/loader";
import useRenameFolderInTeamVault from "@/services/teamVault/renameFolder";

function DynamicItems({
  item,
  handlePathChange,
  refetchContent,
}: {
  item: TeamVaultItem | TeamVaultFolder;
  handlePathChange: (newPath: string) => void;
  refetchContent: () => void;
}) {
  const filetype =
    item.type === "folder"
      ? "folder"
      : getFileCategory((item as TeamVaultItem).s3?.mimeType);
  const [dropdownOpen, setDropdownOpen] = useState(false);
  const [folderModalOpen, setFolderModalOpen] = useState(false);
  const [deleteModalOpen, setDeleteModalOpen] = useState(false);
  const { mutate: deleteItemMutate, isPending: isDeleting } =
    useDeleteContentFromTeamVault(onSuccess);
  const { mutate: renameFolderMutate, isPending: isRenaming } =
    useRenameFolderInTeamVault(onSuccess);
  const handleFolderSubmit = async (data: FolderFormData) => {
    renameFolderMutate({
      folderId: item._id || "",
      newName: data.folderName,
    });
  };
  const handleEdit = () => {
    setFolderModalOpen(true);
    setDropdownOpen(false);
  };
  const handleDeleteModal = () => {
    setDeleteModalOpen(true);
    setDropdownOpen(false);
  };
  const handleDelete = () => {
    deleteItemMutate(item._id || "");
  };
  const handleFolderClick = () => {
    handlePathChange(item._id || "");
  };

  function onSuccess() {
    refetchContent();
  }

  // if (isDeleting || isRenaming) {
  //   return <Loader />;
  // }
  if (filetype === "folder") {
    return (
      <>
        <div className="cursor-pointer relative">
          {(isDeleting || isRenaming) && (
            <div className="absolute inset-0 bg-white/70 backdrop-blur-sm flex items-center justify-center z-10 rounded-md">
              <Loader className="text-white" size={5} />
            </div>
          )}
          <div className="flex flex-col  ">
            <div onDoubleClick={handleFolderClick}>
              <FolderIcon />
            </div>
            <div className="flex justify-between w-[200px] px-3">
              <div className="flex flex-col gap-y-1">
                <p className="text-sm font-medium text-neutrals-G900 truncate max-w-[150px]">
                  {item.name}
                </p>
                {item.itemsCount && (
                  <p className="text-xs text-neutrals-G600">
                    {item.itemsCount} items
                  </p>
                )}
              </div>

              <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
                <DropdownMenuTrigger asChild>
                  <button
                    className="p-1 hover:bg-gray-100 rounded mb-auto"
                    disabled={isDeleting || isRenaming}
                  >
                    <Ellipsis className="size-4 text-neutrals-G600" />
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[120px]">
                  <DropdownMenuItem
                    onClick={handleEdit}
                    className="gap-x-2 cursor-pointer"
                  >
                    <Edit2 />
                    Edit
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={handleDeleteModal}
                    className="gap-x-2 cursor-pointer"
                  >
                    <Trash2 className="size-4" />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>

        {folderModalOpen && (
          <FolderModal
            open={folderModalOpen}
            onOpenChange={setFolderModalOpen}
            title="Edit folder"
            buttonText="Update"
            onSubmit={handleFolderSubmit}
            loading={isRenaming}
            initialValue={item.name}
          />
        )}
        {deleteModalOpen && (
          <DeleteModal
            description="Do you wish to delete this folder?"
            open={deleteModalOpen}
            onOpenChange={setDeleteModalOpen}
            onSubmit={handleDelete}
            isPending={isDeleting}
          />
        )}
      </>
    );
  }
  // File item rendering
  const renderFileIcon = () => {
    switch (filetype) {
      case "docs":
        return (
          <Link
            className="flex items-center justify-center"
            href={(item as TeamVaultItem).s3?.url}
            target="_blank"
          >
            <PdfIcon />
          </Link>
        );
      case "pdf":
        return (
          <Link
            className="flex items-center justify-center"
            href={(item as TeamVaultItem).s3?.url}
            target="_blank"
          >
            <PdfIcon />
          </Link>
        );
      case "image":
        return (
          <Link
            className="bg-gray-200 rounded  flex items-center justify-center"
            href={(item as TeamVaultItem).s3?.url}
            target="_blank"
          >
            <Image
              src={(item as TeamVaultItem).s3?.url}
              alt={item.name}
              className=" object-fill rounded max-w-[220px] max-h-[120px]"
              width={220}
              height={120}
            />
          </Link>
        );
      case "video":
        return (
          <Link href={(item as TeamVaultItem).s3?.url} target="_blank">
            <video
              src={(item as TeamVaultItem).s3?.url}
              className="rounded w-[220px] h-[120px] bg-gray-200 object-fill  flex items-center justify-center"
              width={220}
              height={120}
            >
              <source
                src={(item as TeamVaultItem).s3?.url}
                type={(item as TeamVaultItem).s3?.mimeType}
              />
              Your browser does not support the video tag.
            </video>
          </Link>
        );
      case "audio":
        return (
          <Link
            href={(item as TeamVaultItem).s3?.url}
            target="_blank"
            className="w-20 h-32 bg-gray-200 rounded flex items-center justify-center"
          >
            {/* <div className=""> */}
            <span className="text-xs text-center text-gray-600">AUD</span>
            {/* </div> */}
          </Link>
        );
      case "other":
        return (
          <Link
            href={(item as TeamVaultItem).s3?.url}
            target="_blank"
            className="w-20 h-36 bg-gray-200 rounded flex items-center justify-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              cl="lucide lucide-mic-icon lucide-mic"
            >
              <path d="M12 19v3" />
              <path d="M19 10v2a7 7 0 0 1-14 0v-2" />
              <rect x="9" y="2" width="6" height="13" rx="3" />
            </svg>
          </Link>
        );
      default:
        return null;
    }
  };

  return (
    <>
      <div className="flex flex-col relative">
        {isDeleting && (
          <div className="absolute inset-0 bg-white/70 backdrop-blur-sm flex items-center justify-center z-10 rounded-md">
            <Loader className="text-white" size={5} />
          </div>
        )}
        <div className="cursor-pointer mt-5">{renderFileIcon()}</div>
        <div className="flex justify-between px-5 w-full mt-5">
          <div className="flex flex-col gap-y-1">
            <p className="text-sm font-medium text-neutrals-G900 truncate max-w-[150px]">
              {item.name}
            </p>
            <p className="text-xs text-neutrals-G600">
              Uploaded on{" "}
              {new Date(item.createdAt).toLocaleDateString("en-US", {
                month: "short",
                day: "numeric",
                year: "numeric",
              })}
            </p>
          </div>

          <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
            <DropdownMenuTrigger asChild>
              <button
                className="p-1 hover:bg-gray-100 rounded mb-auto"
                disabled={isDeleting}
              >
                <Ellipsis className="size-4 text-neutrals-G600" />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[120px]">
              <DropdownMenuItem
                onClick={handleDeleteModal}
                className="gap-x-2 cursor-pointer"
              >
                <Trash2 className="size-4" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
      {deleteModalOpen && (
        <DeleteModal
          description="Do you wish to delete this file?"
          open={deleteModalOpen}
          onOpenChange={setDeleteModalOpen}
          onSubmit={handleDelete}
          isPending={isDeleting}
        />
      )}
    </>
  );
}

export default DynamicItems;
