import {
  Table,
  TableBody,
  Table<PERSON>ell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Milestone as MileStoneType } from "@/types/Milestone";
import AddWorkType from "../workType/AddWorkType";
import useGetWorktype from "@/services/milestone/getWorktypes";
import NoDataToShow from "@/components/ui/noDataToShow";
import EditMilestone from "../editMilestone/EditMilestone";
import WorkTypeTableRow from "../workType/WorkTypeTableRow";

type MilestoneProps = {
  milestone: MileStoneType;
};

const Milestone = ({ milestone }: MilestoneProps) => {
  const { data, isPending } = useGetWorktype({
    milestoneId: milestone._id,
    projectId: milestone.projectId,
  });

  const milestoneName =
    milestone.milestoneName || milestone.milestoneTemplateId?.name;

  return (
    <div className="p-3 bg-white rounded-xl border border-border-gray1 flex flex-col">
      <div className="px-3.5 py-2.5 border-b border-[#e6e6e6] flex items-center justify-between gap-x-3">
        <h3 className="text-neutrals-G900 text-xl font-semibold">
          {milestoneName}
        </h3>
        <EditMilestone milestone={milestone} />
      </div>
      <Table>
        <TableHeader className="sticky top-0">
          <TableRow className="*:h-auto *:pb-3 *:pt-4">
            <TableHead className="w-1/6">Work Types</TableHead>
            <TableHead>Total Qty</TableHead>
            <TableHead>Unit</TableHead>
            <TableHead>Optional Qty</TableHead>
            <TableHead>Optional Unit</TableHead>
            <TableHead>Start Date</TableHead>
            <TableHead>End Date</TableHead>
            <TableHead>Days Scheduled</TableHead>
            {/* <TableHead>Actions</TableHead> */}
          </TableRow>
        </TableHeader>
        <TableBody>
          {data?.length === 0 ? (
            <TableRow>
              <TableCell colSpan={9} className="text-center">
                <NoDataToShow />
              </TableCell>
            </TableRow>
          ) : (
            data?.map((workType) => (
              <WorkTypeTableRow
                key={workType._id}
                milestoneTemplateId={milestone.milestoneTemplateId?._id}
                projectId={milestone.projectId}
                workType={workType}
              />
            ))
          )}
          <TableRow className="*:pt-1">
            <TableCell>
              <AddWorkType milestone={milestone} />
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    </div>
  );
};

export default Milestone;
