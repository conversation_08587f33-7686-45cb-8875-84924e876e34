export type Milestone = {
  _id: string;
  milestoneTemplateId: {
    _id: string;
    name: string;
    createdAt: string;
    updatedAt: string;
  };
  projectId: string;
  isCustom?: boolean;
  milestoneName?: string;
  startDate?: string;
  duration?: number;
  paymentDate?: string;
  createdAt: string;
  updatedAt: string;
};

export type MilestoneTemplate = {
  _id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
};
