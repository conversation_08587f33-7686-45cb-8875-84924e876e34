export type WorkTypeUnit = {
  _id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
};

export type WorkType = {
  _id: string;
  milestoneId: string;
  projectId: string;
  worktype: number;
  worktypeName?: string;
  isCustom?: boolean;
  worktypeTemplateId: {
    _id: string;
    worktype: string;
    milestoneTemplateId: string;
    defaultUnit: WorkTypeUnit;
    standardUnits: Array<WorkTypeUnit>;
    unitConversions: Array<any>;
    optionalUnits: Array<WorkTypeUnit>;
    defaultQuantityPerDayMinimum: number;
    defaultQuantityPerDayMaximum: number;
    materialQuantityPerDefaultUnit: Array<{
      material: string;
      quantity: number;
    }>;
    skilledLabourName: string;
  };
  worktypeTemplateProjectId: {
    _id: string;
    milestoneTemplateId: string;
    worktypeTemplateId: string;
    projectId: string;
    defaultQuantityPerDayMinimum: number;
    defaultQuantityPerDayMaximum: number;
    standardUnit: string;
    optionalUnits: Array<string>;
    createdAt: string;
    updatedAt: string;
    __v: number;
  };
  defaultUnit: string;
  totalDefaultQuantity: number;
  preferredUnit: {
    _id: string;
    name: string;
    createdAt: string;
    updatedAt: string;
  };
  totalPreferredQuantity: number;
  optionalUnit: {
    _id: string;
    name: string;
    createdAt: string;
    updatedAt: string;
  };
  totalQuantityOptionalUnit: number;
  startDate: string;
  duration: number;
  materialQuantity: Array<{
    material: string;
    quantity: number;
    _id: string;
  }>;
  createdAt: string;
  updatedAt: string;
};

export type WorktypeTemplate = {
  _id: string;
  milestoneTemplateId: string;
  worktypeTemplateId: {
    _id: string;
    worktype: string;
    milestoneTemplateId: string;
    defaultUnit: WorkTypeUnit;
    standardUnits: Array<WorkTypeUnit>;
    unitConversions: Array<{
      from: string;
      to: string;
      conversionFactor: number;
    }>;
    optionalUnits: Array<WorkTypeUnit>;
    defaultQuantityPerDayMinimum: number;
    defaultQuantityPerDayMaximum: number;
    materialQuantityPerDefaultUnit: Array<{
      material: string;
      quantity: any;
    }>;
    skilledLabourName: string;
  };
  projectId: string;
  defaultQuantityPerDayMinimum: number;
  defaultQuantityPerDayMaximum: number;
  standardUnit: {
    _id: string;
    name: string;
    createdAt: string;
    updatedAt: string;
  };
  optionalUnits: Array<any>;
  createdAt: string;
  updatedAt: string;
};
