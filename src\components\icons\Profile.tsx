import React from "react";

const ProfileIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
    strokeWidth="1.3"
    className={className}
  >
    <path
      d="M3 13.5C3 12.7044 3.31607 11.9413 3.87868 11.3787C4.44129 10.8161 5.20435 10.5 6 10.5H12C12.7956 10.5 13.5587 10.8161 14.1213 11.3787C14.6839 11.9413 15 12.7044 15 13.5C15 13.8978 14.842 14.2794 14.5607 14.5607C14.2794 14.842 13.8978 15 13.5 15H4.5C4.10218 15 3.72064 14.842 3.43934 14.5607C3.15804 14.2794 3 13.8978 3 13.5Z"
      stroke="currentColor"
      strokeLinejoin="round"
    />
    <path
      d="M9 7.5C10.2426 7.5 11.25 6.49264 11.25 5.25C11.25 4.00736 10.2426 3 9 3C7.75736 3 6.75 4.00736 6.75 5.25C6.75 6.49264 7.75736 7.5 9 7.5Z"
      stroke="currentColor"
    />
  </svg>
);

export default ProfileIcon;
