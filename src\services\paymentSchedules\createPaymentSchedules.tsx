"use client";

import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";

type CreatePaymentScheduleInput = {
  scheduleName: string;
  date: string | undefined;
  stageId: string;
  projectId: string;
};

export const useCreatePaymentSchedule = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreatePaymentScheduleInput) => {
      const res = await api.post("/payment-schedule/add", data);
      return res.data;
    },
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["paymentSchedules", variables.projectId],
      });
    },
  });
};
