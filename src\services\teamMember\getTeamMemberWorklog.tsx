import api from "@/lib/api-client";
import { TeamMember } from "@/types/TeamMember";
import { useQuery } from "@tanstack/react-query";

const getTeamMembersByMonth = async (month: string): Promise<TeamMember[]> => {
  const response = await api.get(`/architect-team/get-members?month=${month}`);
  return response.data;
};

const useGetTeamMembersByMonth = (month: string) => {
  return useQuery<any[]>({
    queryKey: ["team-members-by-month", month],
    queryFn: () => getTeamMembersByMonth(month),
    enabled: !!month,
  });
};

export default useGetTeamMembersByMonth;
