export type DesignStage = {
  _id: string;
  duration: any;
  projectId: string;
  stageName: string;
  budgetAllocated: number;
  startDate: string;
  endDate: string;
  isCompleted?: boolean;
  createdBy?: string;
  status: number;
  otherCosts: Array<OtherCost>;
  documents: Array<Document>;
  totalOtherCosts: number;
  totalHours: number;
  members: Array<AssignedMember>;
  chatGroupId: string;
};

export type AssignedMember = {
  memberId: string;
  name: string;
  role: string;
  hours: number;
  _id: string;
  email: string;
  profileUrl?: string;
  isTeamMember: boolean;
  phone: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
  status: number;
};

export type OtherCost = {
  purpose: string;
  amount: number;
  createdAt: string;
  _id: string;
};

export type Document = {
  _id: string;
  s3Link: string;
  filename: string;
  uploadedAt: string;
  isReplaced: boolean;
  uploadedBy: {
    email: string;
    name: string;
    isTeamMember: boolean;
    isActive: number;
    createdAt: string;
    updatedAt: string;
    __v: number;
  };
};

export type DocumentHistory = {
  s3Link: string;
  filename?: string;
  uploadedAt: string;
  uploadedBy: {
    _id: string;
    name: string;
  };
  isReplaced: boolean;
  isDeletedfromHistory?: boolean;
  _id: string;
};
