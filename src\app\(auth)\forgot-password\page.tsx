"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { useSendPasswordResetEmail } from "react-firebase-hooks/auth";
import { auth } from "../../firebase";
import { useRouter } from "next/navigation";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormField,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import Image from "next/image";

interface ForgotPasswordForm {
  email: string;
}

const forgotPasswordSchema = z.object({
  email: z.string().email(""),
});

const ForgotPasswordPage: React.FC = () => {
  const [sendPasswordResetEmail, sending, error] =
    useSendPasswordResetEmail(auth);
  const router = useRouter();
  const form = useForm<ForgotPasswordForm>({
    resolver: zodResolver(forgotPasswordSchema),
    defaultValues: { email: "" },
  });

  const onSubmit = async (data: ForgotPasswordForm) => {
    try {
      await sendPasswordResetEmail(data.email);
      router.push(`/check-email?email=${encodeURIComponent(data.email)}`);
    } catch (err) {
      console.error("Failed to send reset link:", err);
    }
  };

  return (
    <div className="w-[491px] h-[683px] p-10 bg-white rounded-3xl shadow-[0px_4px_24px_0px_rgba(0,0,0,0.12)] flex flex-col justify-start items-center gap-10">
      <Image
        src="/projectsmatelogo.png"
        width={163}
        height={54}
        alt="Projectsmate Logo"
        className="w-[163px] h-[54px] object-contain"
      />
      <div className="self-stretch h-[293px] pt-[60px] flex flex-col justify-center items-center gap-8">
        <div className="self-stretch flex flex-col justify-start items-start gap-6">
          <div className="self-stretch flex flex-col justify-start items-start gap-2">
            <h1 className="self-stretch text-center text-neutrals-G900 text-xl font-semibold  ">
              Forgot Password?
            </h1>
            <p className="self-stretch text-center text-neutrals-G600 text-base font-normal  ">
              No worries! Just enter your email address below and we’ll send you
              a link to reset your password.
            </p>
          </div>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="self-stretch flex flex-col gap-8"
            >
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <div className="self-stretch flex flex-col justify-start items-start gap-2 w-full">
                    <label className="self-stretch text-Grey-Dark-Grey text-sm font-normal  ">
                      Email
                    </label>
                    <FormControl>
                      <Input
                        {...field}
                        className="self-stretch px-3 py-2.5 bg-neutrals-G30 rounded-[6px] text-name-title shadow-input border border-border-gray"
                      />
                    </FormControl>
                    <FormMessage />
                  </div>
                )}
              />
              {error && (
                <span className="text-red-500 text-sm">
                  Failed to send reset link. Please try again.
                </span>
              )}
              <Button
                type="submit"
                className="self-stretch h-[43px] px-4 py-3 bg-primary rounded-[6px] text-white text-base font-medium  "
                loading={sending}
              >
                Send reset link
              </Button>
            </form>
          </Form>
        </div>
      </div>
    </div>
  );
};

export default ForgotPasswordPage;
