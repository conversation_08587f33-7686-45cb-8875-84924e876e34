import { useQuery } from "@tanstack/react-query";

import api from "@/lib/api-client";
import { LabourCount } from "@/types/LabourCount";
import { QueryConfig } from "@/lib/react-query";

export type GetLabourCount = {
  projectId: string;
  worktypeTemplateId: string;
  duration: number;
  quantity: number;
  unit: string;
};

const getLabourCount = async ({
  projectId,
  worktypeTemplateId,
  duration,
  quantity,
  unit,
}: GetLabourCount): Promise<LabourCount> => {
  const response = await api.get(
    `/worktypes/labour-count/${projectId}/${worktypeTemplateId}`,
    {
      params: {
        duration,
        quantity,
        unit,
      },
    },
  );
  return response.data;
};

type UseGetMilestones = GetLabourCount;

const useGetLabourCount = (
  args: UseGetMilestones,
  queryConfig?: QueryConfig<any>,
) => {
  return useQuery({
    queryKey: ["labour-count", args],
    queryFn: async () => getLabourCount(args),

    ...queryConfig,
  });
};

export default useGetLabourCount;
