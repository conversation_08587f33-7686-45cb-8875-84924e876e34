"use client";

import * as React from "react";
import * as ProgressPrimitive from "@radix-ui/react-progress";
import { cva, VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const progressVariants = cva("h-full w-full flex-1 transition-all", {
  variants: {
    variant: {
      default: "bg-primary",
      warning: "bg-[#DA9500]",
      success: "bg-[#4CAF50]",
      danger: "bg-[#C00]",
    },
  },
  defaultVariants: {
    variant: "default",
  },
});

const Progress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root> &
    VariantProps<typeof progressVariants>
>(({ className, value, variant, ...props }, ref) => (
  <ProgressPrimitive.Root
    ref={ref}
    className={cn(
      "relative h-4 w-full overflow-hidden rounded-full bg-secondary",
      className,
    )}
    {...props}
  >
    <ProgressPrimitive.Indicator
      className={cn(progressVariants({ variant }))}
      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}
    />
  </ProgressPrimitive.Root>
));
Progress.displayName = ProgressPrimitive.Root.displayName;

export { Progress };

// "use client";

// import * as React from "react";
// import * as ProgressPrimitive from "@radix-ui/react-progress";
// import { cva, type VariantProps } from "class-variance-authority";
// import { cn } from "@/lib/utils";

// const progressVariants = cva("h-full w-full flex-1 transition-all", {
//   variants: {
//     variant: {
//       default: "bg-primary",
//       warning: "bg-[#DA9500]",
//       success: "bg-[#4CAF50]",
//       danger: "bg-[#C00]",
//     },
//   },
//   defaultVariants: {
//     variant: "default",
//   },
// });

// export interface ProgressProps
//   extends React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>,
//     VariantProps<typeof progressVariants> {
//   value?: number | null;
// }

// const Progress = React.forwardRef<
//   React.ElementRef<typeof ProgressPrimitive.Root>,
//   ProgressProps
// >(({ className, value, variant, ...props }, ref) => {
//   // Ensure value is within 0-100 range
//   const normalizedValue = Math.min(Math.max(value || 0, 0), 100);

//   // Automatically determine variant based on value if not explicitly set
//   const computedVariant =
//     variant ||
//     (normalizedValue < 40
//       ? "danger"
//       : normalizedValue < 90
//         ? "warning"
//         : "success");

//   return (
//     <ProgressPrimitive.Root
//       ref={ref}
//       className={cn(
//         "relative h-4 w-full overflow-hidden rounded-full bg-secondary",
//         className,
//       )}
//       {...props}
//     >
//       <ProgressPrimitive.Indicator
//         className={cn(progressVariants({ variant: computedVariant }))}
//         style={{ transform: `translateX(-${100 - normalizedValue}%)` }}
//       />
//     </ProgressPrimitive.Root>
//   );
// });

// Progress.displayName = ProgressPrimitive.Root.displayName;

// export { Progress };
