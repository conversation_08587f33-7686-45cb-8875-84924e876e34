import { ComponentProps } from "react";
import { cva, VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const Timeline = ({ className, ...props }: ComponentProps<"ul">) => {
  return <ul className={cn("flex list-none relative", className)} {...props} />;
};

const TimelineItem = ({ className, ...props }: ComponentProps<"li">) => {
  return (
    <li
      className={cn(
        "grid grid-rows-[minmax(0,_1fr)_auto] grid-cols-[minmax(0,_1fr)_auto_minmax(0,_1fr)] transition-all shrink-0 items-center",
        className,
      )}
      {...props}
    />
  );
};

const timelineHeadingVariants = cva(
  "row-start-3 text-center row-end-4 mt-4 col-start-1 col-end-4 text-sm",
  {
    variants: {
      status: {
        default: "text-neutrals-G100",
        done: "text-neutrals-G600",
      },
    },
    defaultVariants: {
      status: "default",
    },
  },
);

interface TimelineHeadingProps
  extends ComponentProps<"div">,
    VariantProps<typeof timelineHeadingVariants> {}

const TimelineHeading = ({
  className,
  status,
  ...props
}: TimelineHeadingProps) => {
  return (
    <div
      className={cn(timelineHeadingVariants({ status, className }))}
      {...props}
    />
  );
};

const timelineDotVariants = cva(
  "size-[18px] rounded-full col-start-2 row-start-2",
  {
    variants: {
      status: {
        default: "bg-neutrals-G40",
        done: "bg-primary-blue-B400",
      },
    },
    defaultVariants: {
      status: "default",
    },
  },
);

interface TimelineDotProps
  extends ComponentProps<"div">,
    VariantProps<typeof timelineDotVariants> {}

const TimelineDot = ({ className, status, ...props }: TimelineDotProps) => {
  return (
    <div
      className={cn(timelineDotVariants({ status, className }))}
      {...props}
    />
  );
};

const timelineLineVariants = cva("h-1.5", {
  variants: {
    status: {
      default: "bg-neutrals-G40",
      done: "bg-primary-blue-B400",
    },
    side: {
      left: "row-start-2 col-start-1",
      right: "row-start-2 col-start-3 row-end-auto",
    },
  },
  defaultVariants: {
    status: "default",
    side: "left",
  },
});

interface TimelineLineProps
  extends ComponentProps<"hr">,
    VariantProps<typeof timelineLineVariants> {}

const TimelineLine = ({
  className,
  status,
  side,
  ...props
}: TimelineLineProps) => {
  return (
    <hr
      className={cn(timelineLineVariants({ status, side, className }))}
      {...props}
    />
  );
};

export { Timeline, TimelineItem, TimelineHeading, TimelineDot, TimelineLine };
