import { useState } from "react";
import { toast } from "sonner";
import { format } from "date-fns";

import DeleteIcon from "@/components/icons/Delete";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alertDialog";
import { TableCell, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { calculateEndDate } from "@/lib/utils";
import useDeleteWorkType from "@/services/milestone/deleteWorkType";
import { WorkType } from "@/types/Worktype";
import EditWorkType from "./EditWorkType";

const DeleteWorkTypeAlertDialog = ({
  projectId,
  workType,
}: {
  projectId: string;
  workType: WorkType;
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleSuccess = () => {
    setIsOpen(false);
    toast.success("Work type deleted successfully");
  };

  const { mutate: deleteWorkType, isPending: isPendingDeleteWorkType } =
    useDeleteWorkType(handleSuccess);

  const handleDeleteWorkType = () => {
    deleteWorkType({
      projectId,
      worktypeId: workType._id,
    });
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger>
        <DeleteIcon />
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Remove work type?</AlertDialogTitle>
          <AlertDialogDescription>
            Doing this will remove all the data you entered for the work type{" "}
            <span className="text-neutrals-G900 font-medium">
              &apos;{workType.worktypeTemplateId.worktype}&apos;.
            </span>
            ”?
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button
              onClick={handleDeleteWorkType}
              loading={isPendingDeleteWorkType}
            >
              Yes, Remove
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

type WorkTypeTableRowProps = {
  projectId: string;
  milestoneTemplateId: string;
  workType: WorkType;
};

const WorkTypeTableRow = ({
  projectId,
  milestoneTemplateId,
  workType,
}: WorkTypeTableRowProps) => {
  const unitDisplay = workType.preferredUnit
    ? workType.preferredUnit.name
    : "NA";

  const formatDate = (date: Date | string | undefined) => {
    if (!date) return "NA";
    try {
      return format(new Date(date), "dd MMM yyyy");
    } catch (error) {
      return "NA";
    }
  };

  return (
    <TableRow key={workType._id} className="*:py-3.5">
      <TableCell>
        {workType.worktypeTemplateId?.worktype ?? workType.worktypeName}{" "}
      </TableCell>
      <TableCell>{workType.totalPreferredQuantity}</TableCell>
      <TableCell>{unitDisplay}</TableCell>
      <TableCell>{workType.totalQuantityOptionalUnit}</TableCell>{" "}
      <TableCell>{workType.optionalUnit?.name}</TableCell>
      <TableCell>{formatDate(workType.startDate)}</TableCell>
      <TableCell>
        {workType.startDate && workType.duration
          ? formatDate(calculateEndDate(workType.startDate, workType.duration))
          : "NA"}
      </TableCell>
      <TableCell>{workType.duration ?? "NA"}</TableCell>
      <TableCell className="text-center space-x-3  space-y-0">
        <EditWorkType
          projectId={projectId}
          milestoneTemplateId={milestoneTemplateId}
          workType={workType}
        />
        <DeleteWorkTypeAlertDialog projectId={projectId} workType={workType} />
      </TableCell>
    </TableRow>
  );
};

export default WorkTypeTableRow;
