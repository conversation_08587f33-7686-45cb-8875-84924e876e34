"use client";

import { useAuth } from "@/app/contexts/AuthContext";
import { auth } from "../../firebase";
import { useSignInWithGoogle } from "react-firebase-hooks/auth";
import { signInWithEmailAndPassword } from "firebase/auth";
import { z } from "zod";
import { useForm } from "react-hook-form";
import Image from "next/image";
import Cookies from "js-cookie";
import { zodResolver } from "@hookform/resolvers/zod";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormMessage,
} from "@/components/ui/form";
import useSendUserDetails from "@/services/auth/sendUserDetails";
import { toast } from "sonner";
import Link from "next/link";
import { useState } from "react";
import { Eye, EyeOff } from "lucide-react";

const getErrorMessage = (errorCode: string): string => {
  switch (errorCode) {
    case "auth/invalid-credential":
      return "Invalid email or password. Please try again.";
    case "auth/user-not-found":
      return "No account found with this email. Please check your email or sign up.";
    case "auth/wrong-password":
      return "Incorrect password. Please try again or reset your password.";
    case "auth/invalid-email":
      return "Invalid email address. Please enter a valid email.";
    case "auth/user-disabled":
      return "This account has been disabled. Please contact support.";
    case "auth/too-many-requests":
      return "Too many unsuccessful login attempts. Please try again later or reset your password.";
    case "auth/network-request-failed":
      return "Network error. Please check your internet connection and try again.";
    case "auth/popup-closed-by-user":
      return "Google sign-in was cancelled. Please try again.";
    default:
      return "An unexpected error occurred. Please try again later.";
  }
};

const formSchema = z.object({
  email: z.string().email({ message: "Invalid email address" }),
  password: z.string().min(1, { message: "" }),
});

const LoginPage = () => {
  const { loading: authLoading } = useAuth();

  const [signInWithGoogle, , googleLoading, googleError] =
    useSignInWithGoogle(auth);

  const handleSuccess = () => {
    toast.success("User loggedin successfully");
  };
  const { mutate: sendUserDetails, isPending: isPendingSendUserDetails } =
    useSendUserDetails(handleSuccess);

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
    },
  });

  const handleSendUserDetails = ({ name = "" }: { name: string | null }) => {
    sendUserDetails({ name, organisationType: "architect" });
  };

  const handleGoogleSignIn = async () => {
    try {
      const userCredential = await signInWithGoogle([""], {
        prompt: "select_account",
      });
      if (userCredential) {
        const user = userCredential.user;
        const idToken = await user.getIdToken();

        Cookies.set("token", idToken);

        handleSendUserDetails({
          name: user.displayName,
        });
      }
    } catch (err) {
      if (err instanceof Error && "code" in err) {
        toast.error(getErrorMessage(err.code as string));
      } else {
        toast.error("An unexpected error occurred. Please try again.");
      }
    }
  };

  const handleSignInWithEmailAndPassword = async (data: {
    email: string;
    password: string;
  }) => {
    try {
      const userCredential = await signInWithEmailAndPassword(
        auth,
        data.email,
        data.password,
      );
      const user = userCredential.user;
      const idToken = await user.getIdToken();

      Cookies.set("token", idToken);

      handleSendUserDetails({
        name: user.displayName,
      });
    } catch (err) {
      if (err instanceof Error && "code" in err) {
        toast.error(getErrorMessage(err.code as string));
      } else {
        toast.error("An unexpected error occurred. Please try again.");
      }
    }
  };

  const [showPassword, setShowPassword] = useState(false);

  return (
    <div className="w-[491px] min-h-[683px] p-10 bg-white rounded-3xl shadow-[0px_4px_24px_0px_rgba(0,0,0,0.12)] flex flex-col justify-start items-center gap-10 overflow-auto scrollbar-hide">
      <div className=" flex justify-center">
        <Image
          src="/projectsmatelogo.png"
          width={163}
          height={54}
          alt="Projectsmate Logo"
          className="w-[163px] h-[54px] object-contain"
        />
      </div>

      <div className="self-stretch flex flex-col justify-center items-center gap-8">
        <div className="self-stretch flex flex-col justify-start items-start gap-6">
          <div className="self-stretch h-[51px] flex flex-col justify-start items-start">
            <div className="self-stretch text-center text-card-title text-xl font-semibold ">
              Welcome Back!
            </div>
            <div className="self-stretch text-center text-neutrals-G600 text-base font-normal">
              Upgrade your process with Projectsmate
            </div>
          </div>

          <div className="self-stretch h-[311px] flex flex-col justify-start items-start gap-6">
            <Button
              variant="outline"
              className="self-stretch px-4 py-3 h-[48px] rounded-[6px] border border-border-gray justify-center items-center gap-2 inline-flex hover:bg-slate-50"
              onClick={handleGoogleSignIn}
              disabled={googleLoading}
            >
              <Image
                src="/Google.png"
                alt="Google"
                width={24}
                height={24}
                className="w-6 h-6"
              />
              <span className="text-neutrals-G900 text-base font-medium  ">
                {googleLoading ? "Please wait..." : "Sign in with Google"}
              </span>
            </Button>

            {googleError && (
              <p className="text-red-500">
                {getErrorMessage(googleError.code)}
              </p>
            )}

            <div className="self-stretch justify-center items-center gap-[18px] inline-flex">
              <div className="w-[130px] h-px bg-border-gray" />
              <div className="text-neutrals-G400 text-sm font-normal  ">OR</div>
              <div className="w-[130px] h-px bg-border-gray" />
            </div>

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(handleSignInWithEmailAndPassword)}
                className="self-stretch h-[198px] flex flex-col justify-start items-start gap-6 w-full"
              >
                <div className="self-stretch h-[153px] flex flex-col justify-center items-center gap-5 w-full">
                  <FormField
                    control={form.control}
                    name="email"
                    render={({ field }) => (
                      <div className="self-stretch flex flex-col justify-start items-start gap-2 w-full">
                        <label className="self-stretch text-Grey-Dark-Grey text-sm font-normal  ">
                          Email
                        </label>
                        <FormControl>
                          <Input
                            {...field}
                            className="self-stretch px-3 py-2.5 bg-neutrals-G30 rounded-[6px] shadow-input border border-border-gray text-name-title"
                          />
                        </FormControl>
                        <FormMessage />
                      </div>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="password"
                    render={({ field }) => (
                      <div className="self-stretch flex flex-col justify-start items-start gap-2 w-full">
                        <label className="self-stretch text-Grey-Dark-Grey text-sm font-normal">
                          Password
                        </label>
                        <div className="relative w-full">
                          <FormControl>
                            <Input
                              type={showPassword ? "text" : "password"}
                              {...field}
                              className="self-stretch px-3 py-2.5 bg-neutrals-G30 rounded-[6px] shadow-input border border-border-gray text-name-title [&::-ms-reveal]:hidden [&::-webkit-inner-spin-button]:appearance-none"
                            />
                          </FormControl>
                          {field.value && (
                            <Button
                              type="button"
                              variant="ghost"
                              size="icon"
                              className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                              onClick={() => setShowPassword(!showPassword)}
                            >
                              {showPassword ? (
                                <EyeOff className="h-4 w-4 text-neutrals-G100" />
                              ) : (
                                <Eye className="h-4 w-4 text-neutrals-G100" />
                              )}
                            </Button>
                          )}
                        </div>
                        <FormMessage />
                      </div>
                    )}
                  />
                </div>

                <div className="self-stretch justify-end items-center inline-flex w-full mb-2">
                  <Link
                    href="/forgot-password"
                    className="text-primary text-sm font-medium  hover:underline"
                  >
                    Forgot password?
                  </Link>
                </div>

                <Button
                  type="submit"
                  className="self-stretch h-[43px] px-4 py-3 bg-primary rounded-[6px] justify-center items-center gap-2 inline-flex hover:bg-primary/90 mb-2"
                  loading={authLoading || isPendingSendUserDetails}
                >
                  <span className="text-white text-base font-medium  ">
                    Login
                  </span>
                </Button>

                <div className="self-stretch flex justify-center items-center gap-1 w-full">
                  <span className="text-neutrals-G800 text-sm font-medium  ">
                    Don&apos;t have an account?
                  </span>
                  <Link
                    href="/signup"
                    className="text-primary text-sm font-medium  hover:underline "
                  >
                    Create new account
                  </Link>
                </div>
              </form>
            </Form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
