import { useEffect } from "react";

import { socket } from "@/lib/socket";
import { getToken } from "@/lib/utils";

export const useSocket = () => {
  useEffect(() => {
    function onConnect() {
      console.log("socket connected");
    }

    function onDisconnect() {
      console.log("socket disconnected");
    }

    function onConnectionError(err: Error) {
      console.error("socket connection failed", err.message);
    }

    socket.on("connect", onConnect);
    socket.on("disconnect", onDisconnect);
    socket.on("connect_error", onConnectionError);

    return () => {
      socket.off("connect", onConnect);
      socket.off("disconnect", onDisconnect);
      socket.off("connect_error", onConnectionError);
    };
  }, []);

  const connectSocket = async () => {
    const token = await getToken();
    if (token) {
      (socket.auth as { token: string }).token = token;
      socket.connect();
    }
  };

  const disconnectSocket = () => {
    socket.disconnect();
  };

  return { connectSocket, disconnectSocket };
};
