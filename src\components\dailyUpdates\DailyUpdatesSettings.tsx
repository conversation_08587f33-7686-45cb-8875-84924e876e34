import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";

import Settings from "@/components/icons/Settings";
import {
  Dialog,
  DialogClose,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { Form, FormField } from "@/components/ui/form";
import { dailyUpdatesSettingsSchema } from "@/schema/dailyUpdate";
import { Switch } from "../ui/switch";
import { Button } from "../ui/button";
import useUpdateProjectQuantityFieldOptional from "@/services/project/updateProjectQuantityFieldOPtional";
import { X } from "lucide-react";

const DailyUpdatesSettings = ({
  value,
  projectId,
}: {
  value: boolean;
  projectId: string;
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const { mutate, isPending } = useUpdateProjectQuantityFieldOptional(() =>
    setIsOpen(false),
  );

  const form = useForm<z.infer<typeof dailyUpdatesSettingsSchema>>({
    resolver: zodResolver(dailyUpdatesSettingsSchema),
    defaultValues: {
      isQuantityFieldOptional: value,
    },
  });

  const onSubmit = (data: z.infer<typeof dailyUpdatesSettingsSchema>) => {
    mutate({
      projectId,
      isQuantityFieldOptional: data.isQuantityFieldOptional,
    });
  };

  const handleDialogOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      form.reset();
    }
  };

  const dialogClose = () => {
    handleDialogOpenChange(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogOpenChange}>
      <DialogTrigger asChild>
        <button className="flex items-center gap-1">
          <Settings />
          <span className="text-name-title text-sm">Settings</span>
        </button>
      </DialogTrigger>

      <DialogContent className="w-[510px] rounded-xl">
        <DialogHeader>
          <DialogTitle className="flex items-center justify-between text-xl font-semibold text-neutrals-G900 mb-1.5 ">
            Daily update settings
            <DialogClose asChild>
              <Button type="button" variant="outline" onClick={dialogClose}>
                <X className="h-6 w-6" />
              </Button>
            </DialogClose>
          </DialogTitle>
        </DialogHeader>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col gap-6"
          >
            <div className="flex justify-between items-center">
              <div className="flex flex-col gap-2">
                <p className="text-sm font-medium text-neutrals-G900">
                  Evening update
                </p>
                <p className="text-sm text-Grey-Dark-Grey">
                  Make completed quantity field optional
                </p>
              </div>
              <FormField
                control={form.control}
                name="isQuantityFieldOptional"
                render={({ field }) => (
                  <Switch
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                )}
              />
            </div>

            <div className="flex justify-end">
              <Button loading={isPending} type="submit" className="px-4 py-3">
                Save
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default DailyUpdatesSettings;
