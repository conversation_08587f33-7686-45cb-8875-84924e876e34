import { useCallback, useRef } from "react";
import { UseFormReturn, FieldValues } from "react-hook-form";

export type CloseReason = "explicit" | "implicit";

interface UseSheetFormStateOptions<T extends FieldValues> {
  form: UseFormReturn<T>;
  onSuccess?: () => void;
  preserveOnImplicitClose?: boolean;
  setOpen: (open: boolean) => void;
}

interface UseSheetFormStateReturn {
  handleOpenChange: (open: boolean) => void;
  handleExplicitClose: () => void;
  setCloseReason: (reason: CloseReason) => void;
}

export function useSheetFormState<T extends FieldValues>({
  form,
  onSuccess,
  preserveOnImplicitClose = true,
  setOpen,
}: UseSheetFormStateOptions<T>): UseSheetFormStateReturn {
  const closeReasonRef = useRef<CloseReason>("implicit");

  const setCloseReason = useCallback((reason: CloseReason) => {
    closeReasonRef.current = reason;
  }, []);

  const handleExplicitClose = useCallback(() => {
    setCloseReason("explicit");
  }, [setCloseReason]);

  const handleOpenChange = useCallback(
    (open: boolean) => {
      setOpen(open);

      if (open) {
        closeReasonRef.current = "implicit";
      } else {
        const closeReason = closeReasonRef.current;

        if (closeReason === "explicit" || !preserveOnImplicitClose) {
          form.reset();
          form.clearErrors();
        }

        closeReasonRef.current = "implicit";

        if (onSuccess) {
          onSuccess();
        }
      }
    },
    [form, onSuccess, preserveOnImplicitClose, setOpen],
  );

  return {
    handleOpenChange,
    handleExplicitClose,
    setCloseReason,
  };
}
