import { toast } from "sonner";

import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";

const addQualityChecklist = async ({
  projectId,
  qualityCheckListTemplateId,
}: {
  projectId: string;
  qualityCheckListTemplateId: string;
}) => {
  const response = await api({
    url: `/quality-checklist/${projectId}`,
    method: "POST",
    data: { qualityCheckListTemplateId },
  });
  return response.data;
};

const useAddQualityChecklist = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: addQualityChecklist,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["quality-checklist"] });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to create quality checklist",
      );
    },
  });
};

export default useAddQualityChecklist;
