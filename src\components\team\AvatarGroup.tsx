import { ComponentProps } from "react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import ProfileIcon from "../icons/Profile";
import { cn } from "@/lib/utils";

const AvatarGroup = ({
  className,
  children,
  ...props
}: ComponentProps<"div">) => {
  return (
    <div className={cn("flex -space-x-2", className)} {...props}>
      {children}
    </div>
  );
};

type AvatarMembersProps = {
  members: { src: string | undefined; alt: string }[];
  maxVisible?: number;
};

const AvatarMembers = ({ members, maxVisible = 3 }: AvatarMembersProps) => {
  const visibleCount = Math.min(members.length, maxVisible);
  const remainingMembers = members.length - visibleCount;

  const avatarsToShow = remainingMembers > 0 ? visibleCount - 1 : visibleCount;

  const visibleMembers = members.slice(0, avatarsToShow);

  return (
    <>
      {visibleMembers.map((member, index) => (
        <Avatar
          key={index}
          className="size-7 outline outline-[#F8FCFA] outline-[1.5px]"
        >
          <AvatarImage src={member.src} alt={member.alt} />
          <AvatarFallback>
            <ProfileIcon className="size-3/4" />
          </AvatarFallback>
        </Avatar>
      ))}
      {remainingMembers > 0 && (
        <div className="size-7 relative outline text-white flex items-center justify-center outline-[#F8FCFA] outline-[1.5px] rounded-full bg-[#1F62BC] text-xs font-semibold">
          2+
        </div>
      )}
    </>
  );
};

export { AvatarGroup, AvatarMembers };
