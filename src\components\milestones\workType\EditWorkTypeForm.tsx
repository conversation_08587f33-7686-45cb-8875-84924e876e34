import { ComponentProps } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Form } from "@/components/ui/form";
import { workTypeSchema } from "@/schema/workType";
import WorkTypeForm<PERSON>ield from "./formFields/WorkType";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { UseMutateFunction } from "@tanstack/react-query";
import useUpdateWorkType, {
  UpdateWorkType,
} from "@/services/milestone/updateWorkType";
import { WorkType } from "@/types/Worktype";
import { calculateEndDate } from "@/lib/utils";
import { DialogClose } from "@/components/ui/dialog";

type SubmitButtonProps = ComponentProps<typeof Button> & {
  onButtonClick: (
    mutate: UseMutateFunction<any, any, UpdateWorkType, unknown>,
  ) => void;
  onSuccess?: () => void;
};

const SubmitButton = ({
  onButtonClick,
  onSuccess,
  children,
  ...props
}: SubmitButtonProps) => {
  const { mutate, isPending } = useUpdateWorkType(onSuccess);

  return (
    <Button
      onClick={(e) => {
        e.preventDefault();
        onButtonClick(mutate);
      }}
      type="button"
      loading={isPending}
      {...props}
    >
      {children}
    </Button>
  );
};

type AddWorkTypeFormProps = {
  projectId: string;
  workType: WorkType;
  milestoneTemplateId: string;
  onSuccess?: () => void;
};

const EditWorkTypeForm = ({
  projectId,
  workType,
  milestoneTemplateId,
  onSuccess,
}: AddWorkTypeFormProps) => {
  const handleSuccess = () => {
    onSuccess?.();
    toast.success("Work type updated successfully");
  };

  const form = useForm<z.infer<typeof workTypeSchema>>({
    resolver: zodResolver(workTypeSchema),
    defaultValues: {
      worktypeTemplateId: workType.worktypeTemplateProjectId?._id ?? "",
      worktypeName: workType.worktypeName ?? "",
      isCustom: !!workType.isCustom,
      startDate: workType.startDate ? new Date(workType.startDate) : undefined,
      endDate:
        workType.startDate && workType.duration
          ? new Date(
              calculateEndDate(workType.startDate, workType.duration)
                .toISOString()
                .split("T")[0],
            )
          : undefined,
      duration: workType.duration ?? undefined,
      totalQty:
        workType.totalPreferredQuantity !== undefined
          ? workType.totalPreferredQuantity.toString()
          : "",
      unit: workType.preferredUnit?._id ?? "",
      optionalQty: {
        totalQty:
          workType.totalQuantityOptionalUnit !== undefined
            ? workType.totalQuantityOptionalUnit.toString()
            : "",
        unit: workType.optionalUnit?._id ?? "",
      },
    },
  });

  const handleSubmit = (
    mutate: UseMutateFunction<any, any, UpdateWorkType, unknown>,
  ) => {
    form.handleSubmit((data) => {
      const payload = {
        projectId,
        worktypeId: workType._id,
        startDate: data.startDate?.toLocaleDateString("en-CA"),
        duration: data.duration,
        totalQuantity: Number(data.totalQty),
        unit: data.unit,
        totalQuantityOptionalUnit:
          Number(data.optionalQty.totalQty) || undefined,
        optionalUnit: data.optionalQty.unit || undefined,
        //worktypeOrder: workType.worktypeOrder,
      };

      mutate(payload);
    })();
  };

  return (
    <Form {...form}>
      <form className="flex flex-col gap-y-3">
        <WorkTypeFormField
          isEdit
          milestoneTemplateId={milestoneTemplateId}
          form={form}
        />
        <div className="flex gap-x-4">
          <SubmitButton
            onButtonClick={(mutate) => handleSubmit(mutate)}
            onSuccess={handleSuccess}
            className="px-4 mt-3 ml-auto w-[65px]"
          >
            Save
          </SubmitButton>
        </div>
      </form>
    </Form>
  );
};

export default EditWorkTypeForm;
