import { Fragment, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

import RoundedPlus from "../icons/RoundedPlus";
import { <PERSON><PERSON> } from "../ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { otherCostSchema } from "@/schema/designStage";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Form } from "../ui/form";
import { Input } from "@/components/ui/input";
import { cn } from "@/lib/utils";
import MinusCircle from "../icons/MinusCircle";
import { OtherCost } from "@/types/DesignStage";
import NoDataToShow from "../ui/noDataToShow";
import useAddOtherCosts from "@/services/designStage/addOtherCosts";
import useDeleteOtherCost from "@/services/designStage/deleteOtherCosts";

type OtherCostsProps = {
  totalCosts: number;
  data: OtherCost[];
  stageId: string;
};

const OtherCosts = ({ totalCosts, data, stageId }: OtherCostsProps) => {
  const [open, setOpen] = useState(false);
  const form = useForm<z.infer<typeof otherCostSchema>>({
    resolver: zodResolver(otherCostSchema),
    defaultValues: {
      stageId,
      purpose: "",
      amount: 0,
    },
  });

  const {
    mutate: addExpense,
    isPending: isPendingAddExpense,
    isSuccess: isSuccessAddExpense,
  } = useAddOtherCosts();

  const { mutate: deleteCost, isPending: isDeleting } = useDeleteOtherCost();

  const handleDelete = (costId: string) => {
    deleteCost({ stageId, costId });
  };

  const onSubmit = async (values: z.infer<typeof otherCostSchema>) => {
    addExpense(values);
  };

  useEffect(() => {
    if (!open || isSuccessAddExpense) {
      form.reset();
      form.clearErrors();
    }
  }, [open, form, isSuccessAddExpense]);

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          variant="input"
          className="py-1.5 px-1.5 h-[30px] text-xs text-[#7F8FA4]"
        >
          Other costs :
          <span className="font-semibold ml-1">₨ {totalCosts}</span>{" "}
          <RoundedPlus className="ml-2" />
        </Button>
      </DialogTrigger>
      <DialogContent
        isCloseIconVisible
        className="text-neutrals-G900 gap-y-6 sm:max-w-[35.42%]"
      >
        <DialogHeader>
          <DialogTitle>Other Costs</DialogTitle>
          <DialogDescription className="text-neutrals-G600">
            Add extra expense to this stage
          </DialogDescription>
        </DialogHeader>

        <div className="flex flex-col gap-y-3">
          <div className="p-4 border border-neutrals-G40 rounded-[8px] grid grid-cols-2 text-sm">
            <h6 className="text-neutrals-G600 mb-4">Cost Purpose</h6>
            <h6 className="text-neutrals-G600 mb-4">Amount</h6>
            {data?.length === 0 ? (
              <NoDataToShow className="col-span-3" />
            ) : (
              data?.map((cost, index) => (
                <Fragment key={cost._id}>
                  <p
                    className={cn(
                      "text-neutrals-G900 font-medium ",
                      index === 0 ? "mt-[13.5px]" : "mt-[23px]",
                      index === data?.length - 1 ? "mb-[13.5px]" : "",
                    )}
                  >
                    {cost.purpose}
                  </p>
                  <div
                    className={cn(
                      "flex justify-between text-neutrals-G900 font-medium",
                      index === 0 ? "mt-[13.5px]" : "mt-[23px]",
                      index === data?.length - 1 ? "mb-[13.5px]" : "",
                    )}
                  >
                    <p>₨ {cost.amount}</p>
                    <button
                      type="button"
                      onClick={() => handleDelete(cost._id)}
                      disabled={isDeleting}
                    >
                      <MinusCircle />
                    </button>
                  </div>
                </Fragment>
              ))
            )}
          </div>
          <Form {...form}>
            <form
              onSubmit={form.handleSubmit(onSubmit)}
              className="flex items-end gap-x-2"
            >
              <FormField
                control={form.control}
                name="purpose"
                render={({ field }) => (
                  <FormItem className="flex-1">
                    <FormLabel>Purpose</FormLabel>
                    <FormControl>
                      <Input {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem className="basis-[4.5rem]">
                    <FormLabel>Amount</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button disabled={isPendingAddExpense} className="px-4">
                Add Expense
              </Button>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default OtherCosts;
