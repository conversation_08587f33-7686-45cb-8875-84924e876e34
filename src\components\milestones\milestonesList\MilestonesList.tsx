"use client";

import useGetMilestones from "@/services/milestone/getMilestones";
import Milestone from "./Milestone";
import { useParams } from "next/navigation";
import NoDataToShow from "@/components/ui/noDataToShow";
import Loader from "@/components/ui/loader";

const MilestonesList = () => {
  const { id } = useParams() as { id: string };
  const { data, isPending } = useGetMilestones({ projectId: id });

  if (isPending) return <Loader />;
  return (
    <>
      {data?.length === 0 ? (
        <div className="flex justify-center">
          {" "}
          <NoDataToShow />
        </div>
      ) : (
        <div className="flex flex-col gap-y-2">
          {data?.map((milestone) => (
            <Milestone key={milestone._id} milestone={milestone} />
          ))}
        </div>
      )}
    </>
  );
};

export default MilestonesList;
