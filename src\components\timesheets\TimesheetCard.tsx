import File3 from "../icons/File3";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import { Textarea } from "../ui/textarea";
import { TimesheetEntry } from "@/types/Timesheet";
import { useState, useRef } from "react";
import EditTimesheet from "./EditTimeSheet";

type TimesheetCardProps = {
  data: TimesheetEntry;
  current?: boolean;
};

const TimesheetCard = ({ data, current }: TimesheetCardProps) => {
  const [open, setOpen] = useState(false);
  const [noteDialogOpen, setNoteDialogOpen] = useState(false);
  const preventEditRef = useRef(false);

  const handleClick = () => {
    if (preventEditRef.current) return;
    setOpen(true);
  };

  return (
    <>
      <div
        className="bg-white py-3 px-3.5 rounded-xl border border-neutrals-G40 space-y-6 cursor-pointer"
        {...(current ? { onClick: handleClick } : {})}
      >
        <div className="flex justify-between items-start text-neutrals-G900 font-semibold">
          <h3>{data.projectName}</h3>
          <div className="text-sm px-3 py-1 rounded-3xl bg-primary-blue-B40">
            {data.hour} {data.hour === 1 ? "hr" : "hrs"}
          </div>
        </div>
        <div className="flex justify-between items-center">
          <p className="text-neutrals-G600">{data.stageName}</p>
          <Dialog
            open={noteDialogOpen}
            onOpenChange={(isOpen) => {
              setNoteDialogOpen(isOpen);

              // Prevent EditTimesheet modal from opening right after dialog closes
              if (!isOpen) {
                preventEditRef.current = true;
                setTimeout(() => {
                  preventEditRef.current = false;
                }, 100); // Give it a slight buffer
              }
            }}
          >
            <DialogTrigger onClick={(e) => e.stopPropagation()}>
              <File3 className="text-primary-blue-B400" />
            </DialogTrigger>
            <DialogContent
              isCloseIconVisible={true}
              onClick={(e) => e.stopPropagation()}
            >
              <DialogHeader className="font-semibold text-xl">
                Note
              </DialogHeader>
              <Textarea
                className="resize-none h-[135px] disabled:text-[#1d2836] shadow-none"
                value={data.description}
                disabled
              />
            </DialogContent>
          </Dialog>
        </div>
      </div>
      <EditTimesheet setOpen={setOpen} open={open} data={data} />
    </>
  );
};

export default TimesheetCard;
