import { z } from "zod";

export const productivityDatabaseSchema = z.object({
  standardUnits: z.array(
    z
      .object({
        unit: z.string().min(1, { message: "" }),
        minQuantity: z.string().min(1, { message: "" }),
        //  .max(12, { message: "" }),
        maxQuantity: z.string().min(1, { message: "" }),
        //  .max(12, { message: "" }),
      })
      .refine(
        (data) => {
          return Number(data.maxQuantity) > Number(data.minQuantity);
        },
        {
          message: "Max quantity must be greater than min quantity",
          path: ["maxQuantity"],
        },
      ),
  ),
  optionalUnits: z.array(
    z
      .object({
        unit: z.string().min(1, { message: "" }),
        minQuantity: z
          .string()
          .min(1, { message: "" })
          .max(12, { message: "" }),
        maxQuantity: z
          .string()
          .min(1, { message: "" })
          .max(12, { message: "" }),
      })
      .refine(
        (data) => {
          return Number(data.maxQuantity) >= Number(data.minQuantity);
        },
        {
          message: "Max quantity must be greater than min quantity",
          path: ["maxQuantity"],
        },
      ),
  ),
});
