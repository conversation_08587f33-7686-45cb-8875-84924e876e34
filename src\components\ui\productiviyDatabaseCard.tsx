// import React from "react";
// import EditIcon from "../icons/Edit";

// const ProductivityCard = React.forwardRef<
//   HTMLDivElement,
//   React.HTMLAttributes<HTMLDivElement>
// >(({ className, ...props }, ref) => (
//   <div
//     ref={ref}
//     className="w-60 h-[211px] relative bg-white rounded-xl border border-neutral-200 overflow-hidden"
//     {...props}
//   />
// ));
// ProductivityCard.displayName = "ProductivityCard";

// const ProductivityCardHeader = React.forwardRef<
//   HTMLDivElement,
//   React.HTMLAttributes<HTMLDivElement> & { title: string; onEdit?: () => void }
// >(({ className, title, onEdit, ...props }, ref) => (
//   <div
//     ref={ref}
//     className="flex justify-between items-center px-5 pt-6"
//     {...props}
//   >
//     <h3 className="text-neutral-900 text-base font-medium">{title}</h3>
//     {onEdit && (
//       <button
//         onClick={onEdit}
//         className="w-4 h-4 text-neutral-500 hover:text-neutral-700"
//       >
//         <EditIcon />
//       </button>
//     )}
//   </div>
// ));
// ProductivityCardHeader.displayName = "ProductivityCardHeader";

// const ProductivityCardContent = React.forwardRef<
//   HTMLDivElement,
//   React.HTMLAttributes<HTMLDivElement> & {
//     standardUnit: { value: string; unit: string };
//     optionalUnit?: { value: string; unit: string };
//   }
// >(({ className, standardUnit, optionalUnit, ...props }, ref) => (
//   <div ref={ref} className="px-5 pt-4" {...props}>
//     <p className="text-neutral-600 text-xs leading-[18px]">
//       Daily output expected
//     </p>
//     <div className="mt-2">
//       <span className="text-neutral-900 text-sm font-medium">
//         {standardUnit.value}
//       </span>
//       <span className="text-neutral-600 text-xs ml-1">/day</span>
//     </div>
//     {optionalUnit && (
//       <>
//         <p className="text-neutral-600 text-xs leading-[18px] mt-4">OR</p>
//         <div className="mt-2">
//           <span className="text-neutral-900 text-sm font-medium">
//             {optionalUnit.value}
//           </span>
//           <span className="text-neutral-600 text-xs ml-1">/day</span>
//         </div>
//       </>
//     )}
//   </div>
// ));
// ProductivityCardContent.displayName = "ProductivityCardContent";

// const ProductivitySection = React.forwardRef<
//   HTMLDivElement,
//   React.HTMLAttributes<HTMLDivElement> & { title: string }
// >(({ className, title, ...props }, ref) => (
//   <div
//     ref={ref}
//     className="w-full bg-white rounded-xl border border-neutral-200 overflow-hidden p-5"
//     {...props}
//   >
//     <h2 className="text-neutral-900 text-xl font-semibold mb-6">{title}</h2>
//     <div className="grid grid-cols-4 gap-4">{props.children}</div>
//   </div>
// ));
// ProductivitySection.displayName = "ProductivitySection";

// export {
//   ProductivityCard,
//   ProductivityCardHeader,
//   ProductivityCardContent,
//   ProductivitySection,
// };

import React from "react";
import EditIcon from "../icons/Edit";

const ProductivityCard = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement>
>(({ className, ...props }, ref) => (
  <div
    ref={ref}
    className="min-w-60 h-[211px] relative bg-white rounded-xl border border-neutral-200 overflow-hidden"
    {...props}
  />
));
ProductivityCard.displayName = "ProductivityCard";

const ProductivityCardHeader = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    title: string;
    onEdit?: () => void;
    editComponent?: React.ReactNode;
  }
>(({ className, title, onEdit, editComponent, ...props }, ref) => (
  <div
    ref={ref}
    className="flex justify-between  items-start px-5 pt-6"
    {...props}
  >
    <h3 className="text-neutral-900 text-base font-medium">{title}</h3>
    {editComponent}
  </div>
));
ProductivityCardHeader.displayName = "ProductivityCardHeader";

const ProductivityCardContent = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & {
    standardUnit: { value: string; unit: string };
    optionalUnit?: { value: string; unit: string };
  }
>(({ className, standardUnit, optionalUnit, ...props }, ref) => (
  <div ref={ref} className="px-5 pt-4" {...props}>
    <p className="text-neutral-600 text-xs leading-[18px]">
      Daily output expected
    </p>
    <div className="mt-2">
      <span className="text-neutral-900 text-sm font-medium">
        {standardUnit.value}
      </span>
      <span className="text-neutral-600 text-xs ml-1">/day</span>
    </div>
    {optionalUnit && (
      <>
        <p className="text-neutral-600 text-xs leading-[18px] mt-4">OR</p>
        <div className="mt-2">
          <span className="text-neutral-900 text-sm font-medium">
            {optionalUnit.value}
          </span>
          <span className="text-neutral-600 text-xs ml-1">/day</span>
        </div>
      </>
    )}
  </div>
));
ProductivityCardContent.displayName = "ProductivityCardContent";

const ProductivitySection = React.forwardRef<
  HTMLDivElement,
  React.HTMLAttributes<HTMLDivElement> & { title: string }
>(({ className, title, ...props }, ref) => (
  <div
    ref={ref}
    className="w-full bg-white rounded-xl border border-neutral-200 overflow-hidden p-5"
    {...props}
  >
    <h2 className="text-neutral-900 text-xl font-semibold mb-6">{title}</h2>
    <div className="grid grid-cols-4 gap-4">{props.children}</div>
  </div>
));
ProductivitySection.displayName = "ProductivitySection";

export {
  ProductivityCard,
  ProductivityCardHeader,
  ProductivityCardContent,
  ProductivitySection,
};
