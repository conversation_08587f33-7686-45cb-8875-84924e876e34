import { useQuery } from "@tanstack/react-query";

import api from "@/lib/api-client";
import { CreatedMessage } from "@/types/Socket";

export type GetMessages = {
  groupId: string;
};

const getMessages = async ({
  groupId,
}: GetMessages): Promise<{
  totalMessages: number;
  messages: CreatedMessage[];
}> => {
  const response = await api.get(`/chat/get-messages/${groupId}`);
  return response.data;
};

type UseGetMessages = GetMessages;

const useGetMessages = ({ groupId }: Partial<UseGetMessages>) => {
  return useQuery({
    queryKey: ["messages", groupId],
    queryFn: async () => getMessages({ groupId: groupId! }),
    enabled: !!groupId,
    staleTime: 0,
  });
};

export default useGetMessages;
