import { useMutation, useQueryClient } from "@tanstack/react-query";
import api from "@/lib/api-client";
import { toast } from "sonner";

type CreateContractorParams = {
  email: string;
  name: string;
  organisationName: string;
};

const useCreateContractor = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (newContractor: CreateContractorParams) => {
      const response = await api.post("/contractors", newContractor);
      return response;
    },

    onSuccess: () => {
      toast.success("Contractor created successfully");
      queryClient.invalidateQueries({ queryKey: ["contractors"] });
    },

    onError: (error: any) => {
      console.log("Error", error);
      if (error.response) {
        toast.error(error.response.data.message);
      } else {
        toast.error("Failed to create contractor");
      }
    },
  });
};

export default useCreateContractor;
