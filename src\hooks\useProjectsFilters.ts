import { useState, useEffect, useCallback } from "react";
import { useSearchParams } from "next/navigation";
import { Status } from "@/types/Project";

type SelectValue = Status | "all" | "";

export const useProjectsFilters = () => {
  const searchParams = useSearchParams();

  const [searchTerm, setSearchTerm] = useState(
    searchParams.get("search") || "",
  );
  const [select, setSelect] = useState<SelectValue>(
    (searchParams.get("select") as Status) || "",
  );

  // Update URL when filters change
  useEffect(() => {
    const params = new URLSearchParams(searchParams.toString());

    if (searchTerm) {
      params.set("search", searchTerm);
    } else {
      params.delete("search");
    }

    if (select && select !== "all") {
      params.set("select", select);
    } else {
      params.delete("select");
    }

    const newUrl = `?${params.toString()}`;
    const currentUrl = window.location.search;

    if (newUrl !== currentUrl) {
      window.history.pushState(null, "", newUrl);
    }
  }, [searchTerm, select, searchParams]);

  const handleFiltersChange = useCallback(
    (newSearchTerm: string, newSelect: SelectValue) => {
      setSearchTerm(newSearchTerm);
      setSelect(newSelect);
    },
    [],
  );

  return {
    searchTerm,
    select,
    handleFiltersChange,
  };
};
