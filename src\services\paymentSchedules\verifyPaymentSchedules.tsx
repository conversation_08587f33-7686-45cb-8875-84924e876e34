"use client";

import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export const useVerifyPayment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (scheduleId: string) => {
      const res = await api.put(
        `/payment-schedule/verifyPayment/${scheduleId}`,
      );
      return res.data;
    },
    onSuccess: (_data, scheduleId) => {
      toast.success("Verified payment successfully");
      queryClient.invalidateQueries({
        queryKey: ["paymentSchedules"],
      });

      queryClient.invalidateQueries({
        queryKey: ["paymentSchedule", scheduleId],
      });
    },
    onError: () => {
      toast.error("Failed to verify payment");
    },
  });
};
