import { toast } from "sonner";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { z } from "zod";

import api from "@/lib/api-client";
import { teamMemberSchema } from "@/schema/teamMember";

const updateTeamMember = async (
  body: Omit<z.infer<typeof teamMemberSchema>, "email"> & {
    userId: string;
  },
) => {
  const response = await api({
    url: "/architect-team/update-members",
    method: "PUT",
    data: body,
  });
  return response.data;
};

const useUpdateTeamMember = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateTeamMember,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["team-members"] });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to update team member",
      );
    },
  });
};

export default useUpdateTeamMember;
