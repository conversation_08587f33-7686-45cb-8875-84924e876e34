import { cn } from "@/lib/utils";

const ProjectSettingsIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="15"
    height="12"
    fill="none"
    viewBox="0 0 15 12"
    className={className}
  >
    <mask id="path-1-inside-1_1101_1416" fill="#fff">
      <path d="M6.75 3.75A2.247 2.247 0 0 1 4.5 6a2.247 2.247 0 0 1-2.25-2.25A2.247 2.247 0 0 1 4.5 1.5a2.247 2.247 0 0 1 2.25 2.25M9 12H0v-1.5c0-1.658 2.018-3 4.5-3S9 8.842 9 10.5M3.75 3.75c0 .412.338.75.75.75s.75-.338.75-.75A.75.75 0 0 0 4.5 3a.75.75 0 0 0-.75.75M1.5 10.5h6c0-.825-1.343-1.5-3-1.5s-3 .675-3 1.5M15 6v1.5H8.25V6M15 3v1.5H8.25V3M15 0v1.5H8.25V0z"></path>
    </mask>
    <path
      fill="currentColor"
      d="M6.75 3.75A2.247 2.247 0 0 1 4.5 6a2.247 2.247 0 0 1-2.25-2.25A2.247 2.247 0 0 1 4.5 1.5a2.247 2.247 0 0 1 2.25 2.25M9 12H0v-1.5c0-1.658 2.018-3 4.5-3S9 8.842 9 10.5M3.75 3.75c0 .412.338.75.75.75s.75-.338.75-.75A.75.75 0 0 0 4.5 3a.75.75 0 0 0-.75.75M1.5 10.5h6c0-.825-1.343-1.5-3-1.5s-3 .675-3 1.5M15 6v1.5H8.25V6M15 3v1.5H8.25V3M15 0v1.5H8.25V0z"
    ></path>
    <path
      fill="currentColor"
      d="M0 12h-1v1h1zm1.5-1.5h-1v1h1zm6 0v1h1v-1zm7.5-3v1h1v-1zm-6.75 0h-1v1h1zm6.75-3v1h1v-1zm-6.75 0h-1v1h1zM15 0h1v-1h-1zm0 1.5v1h1v-1zm-6.75 0h-1v1h1zm0-1.5v-1h-1v1zm-2.5 3.75C5.75 4.443 5.193 5 4.5 5v2a3.247 3.247 0 0 0 3.25-3.25zM4.5 5c-.693 0-1.25-.557-1.25-1.25h-2A3.247 3.247 0 0 0 4.5 7zM3.25 3.75c0-.693.557-1.25 1.25-1.25v-2a3.247 3.247 0 0 0-3.25 3.25zM4.5 2.5c.693 0 1.25.557 1.25 1.25h2A3.247 3.247 0 0 0 4.5.5zM9 11H0v2h9zm-8 1v-1.5h-2V12zm0-1.5c0-.39.237-.865.874-1.29.629-.419 1.555-.71 2.626-.71v-2c-1.411 0-2.735.38-3.735 1.046C-.23 8.208-1 9.232-1 10.5zm3.5-2c1.071 0 1.997.291 2.626.71.637.425.874.9.874 1.29h2c0-1.268-.771-2.292-1.765-2.954C7.235 6.88 5.911 6.5 4.5 6.5zM2.75 3.75c0 .965.785 1.75 1.75 1.75v-2c.14 0 .25.11.25.25zM4.5 5.5c.965 0 1.75-.785 1.75-1.75h-2c0-.14.11-.25.25-.25zm1.75-1.75C6.25 2.785 5.465 2 4.5 2v2c-.14 0-.25-.11-.25-.25zM4.5 2c-.965 0-1.75.785-1.75 1.75h2c0 .14-.11.25-.25.25zm-3 9.5h6v-2h-6zm7-1c0-.996-.783-1.629-1.43-1.953C6.36 8.192 5.45 8 4.5 8v2c.706 0 1.297.146 1.674.335.438.219.326.336.326.165zM4.5 8c-.951 0-1.86.192-2.57.547C1.284 8.87.5 9.504.5 10.5h2c0 .17-.112.054.326-.165.377-.189.968-.335 1.674-.335zM14 6v1.5h2V6zm1 .5H8.25v2H15zm-5.75 1V6h-2v1.5zM14 3v1.5h2V3zm1 .5H8.25v2H15zm-5.75 1V3h-2v1.5zM14 0v1.5h2V0zm1 .5H8.25v2H15zm-5.75 1V0h-2v1.5zm-1-.5H15v-2H8.25z"
      mask="url(#path-1-inside-1_1101_1416)"
    ></path>
  </svg>
);

export default ProjectSettingsIcon;
