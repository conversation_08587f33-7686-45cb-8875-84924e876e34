import { UseFormReturn } from "react-hook-form";
import { Check, ChevronDown, Plus } from "lucide-react";
import { format } from "date-fns";
import { z } from "zod";
import { useRef, useState } from "react";

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Popover,
  PopoverClose,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import CalendarIcon from "@/components/icons/Calendar";
import { Calendar } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { milestoneSchema } from "@/schema/milestone";
import useGetMilestonesTemplate from "@/services/milestone/getMilestonesTemplate";
import Duration from "./Duration";

type MilestoneFormFieldsProps = {
  form: UseFormReturn<z.infer<typeof milestoneSchema>>;
  isEdit?: boolean;
};

const MilestoneFormFields = ({
  form,
  isEdit = false,
}: MilestoneFormFieldsProps) => {
  const { data: milestonesTemplates, isPending: isPendingMilestonesTemplates } =
    useGetMilestonesTemplate();

  const popoverCloseRef = useRef<HTMLButtonElement>(null);
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const selectedTemplate = milestonesTemplates?.find(
    (template) => template._id === form.watch("milestoneTemplateId"),
  );

  const isCustom = form.watch("isCustom");
  const milestoneName = form.watch("milestoneName");

  const handleCreateNewMilestone = () => {
    form.setValue("isCustom", true);
    form.setValue("milestoneName", searchQuery);
    form.setValue("milestoneTemplateId", "");
    resetWorkTypesFields();
    setOpen(false);
  };

  // NOTE: whenever the milestone changes needs to reset the worktypes, as the worktype depends on the milestone.
  const resetWorkTypesFields = () => {
    const isWorkTypesArrayEmpty = form.getValues("worktypes")?.length === 0;

    if (!isWorkTypesArrayEmpty) {
      form.resetField("worktypes");
    }
  };

  const getDisplayName = () => {
    if (isCustom && milestoneName) {
      return milestoneName;
    }
    if (selectedTemplate) {
      return selectedTemplate.name;
    }
    if (searchQuery) {
      return searchQuery;
    }
  };

  return (
    <div className="flex flex-col gap-y-3">
      <FormField
        control={form.control}
        name="milestoneTemplateId"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Select Milestone</FormLabel>
            <Popover open={open} onOpenChange={setOpen} modal>
              <PopoverTrigger asChild>
                <FormControl>
                  <Button
                    variant="input"
                    role="combobox"
                    aria-expanded={open}
                    className="w-full justify-between"
                    disabled={isEdit || isPendingMilestonesTemplates}
                  >
                    {getDisplayName() || (
                      <span className="text-neutrals-G100 font-semibold">
                        Choose
                      </span>
                    )}
                    <ChevronDown className="ml-2 size-5 shrink-0 text-gray" />
                  </Button>
                </FormControl>
              </PopoverTrigger>
              <PopoverContent className="w-[28.6rem]">
                <Command>
                  <CommandInput
                    placeholder="Search milestone..."
                    value={searchQuery}
                    onValueChange={setSearchQuery}
                  />
                  <CommandList className="scrollbar-hide  overflow-y-auto">
                    <CommandEmpty className="pt-3">
                      <Button
                        variant="outline"
                        size="sm"
                        className="flex gap-x-1 text-neutrals-G600 text-base font-normal w-full justify-start"
                        onClick={handleCreateNewMilestone}
                        disabled={!searchQuery.trim()}
                      >
                        <Plus className="size-[18px] text-primary-blue-B900" />
                        {searchQuery.trim() ? (
                          <>
                            Create milestone
                            <span className="text-primary-blue-B900">
                              `{searchQuery}`
                            </span>
                          </>
                        ) : (
                          "Type to create a new milestone"
                        )}
                      </Button>
                    </CommandEmpty>
                    <CommandGroup>
                      {milestonesTemplates
                        ?.filter(({ name }) =>
                          name
                            .toLowerCase()
                            .includes(searchQuery.toLowerCase()),
                        )
                        .map(({ _id, name }, index) => (
                          <CommandItem
                            key={_id}
                            value={name}
                            onSelect={() => {
                              if (isCustom) {
                                form.setValue("isCustom", false);
                              }
                              field.onChange(_id);
                              resetWorkTypesFields();
                              setOpen(false);
                            }}
                            className="flex items-center gap-x-2 p-3"
                          >
                            {++index}. {name}
                            <Check
                              className={cn(
                                "ml-auto h-4 w-4",
                                field.value === _id
                                  ? "opacity-100"
                                  : "opacity-0",
                              )}
                            />
                          </CommandItem>
                        ))}
                    </CommandGroup>
                  </CommandList>
                </Command>
              </PopoverContent>
            </Popover>
            <FormMessage />
          </FormItem>
        )}
      />
      <div className="flex gap-x-2">
        <FormField
          control={form.control}
          name="startDate"
          render={({ field }) => (
            <FormItem className="basis-1/2">
              <FormLabel>Start Date</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="input"
                      className={cn(
                        "text-left",
                        !field.value && "text-neutrals-G100",
                      )}
                    >
                      {field.value ? (
                        format(field.value, "dd/MM/yyyy")
                      ) : (
                        <span>Choose date</span>
                      )}
                      <CalendarIcon className="ml-auto" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <PopoverClose ref={popoverCloseRef} className="hidden" />
                  <Calendar
                    selected={field.value ?? undefined}
                    onSelect={(value) => {
                      popoverCloseRef.current?.click();
                      field.onChange(value);
                    }}
                    mode="single"
                    showOutsideDays={false}
                    // startMonth={new Date(Date.now())}
                    endMonth={
                      new Date(Date.now() + 1000 * 60 * 60 * 24 * 365 * 10)
                    }
                  />
                </PopoverContent>
              </Popover>

              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="endDate"
          render={({ field }) => (
            <FormItem className="basis-1/2">
              <FormLabel>End Date</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="input"
                      className={cn(
                        "text-left",
                        !field.value && "text-neutrals-G100",
                      )}
                    >
                      {field.value ? (
                        format(field.value, "dd/MM/yyyy")
                      ) : (
                        <span>Choose date</span>
                      )}
                      <CalendarIcon className="ml-auto" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <PopoverClose ref={popoverCloseRef} className="hidden" />
                  <Calendar
                    selected={field.value ?? undefined}
                    onSelect={(value) => {
                      popoverCloseRef.current?.click();
                      field.onChange(value);
                    }}
                    mode="single"
                    showOutsideDays={false}
                    endMonth={
                      new Date(Date.now() + 1000 * 60 * 60 * 24 * 365 * 10)
                    }
                  />
                </PopoverContent>
              </Popover>

              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      <Duration
        value={form.watch("duration") || 0}
        onChange={(val) => {
          form.setValue("duration", val);
        }}
        startDate={form.watch("startDate")}
        endDate={form.watch("endDate")}
      />

      {/* <div>
        <span className="text-sm font-medium text-cancel-btn flex items-center gap-2 mb-3">
          Payment scheduled date
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger>
                <div className="w-[18px] h-[18px] bg-border-gray rounded-full flex items-center justify-center">
                  <span className="text-[10px] font-normal">i</span>
                </div>
              </TooltipTrigger>
              <TooltipContent side="bottom" sideOffset={5}>
                <p className="text-sm  text-name-title p-1">
                  Reminders will be automatically sent to the architect,
                  <br />
                  contractor, and client, ensuring timely preparation and
                  <br />
                  coordination for payment processing.
                </p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        </span>

        <FormField
          control={form.control}
          name="paymentDate"
          render={({ field }) => (
            <div className="w-full">
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="input"
                      className={cn(
                        "text-left font-normal w-full",
                        !field.value && "text-muted-foreground",
                      )}
                    >
                      {field.value ? (
                        format(field.value, "dd/MM/yyyy")
                      ) : (
                        <span>Select</span>
                      )}
                      <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <PopoverClose ref={popoverCloseRef} className="hidden" />
                  <Calendar
                    selected={field.value ?? undefined}
                    onSelect={(value) => {
                      popoverCloseRef.current?.click();
                      field.onChange(value);
                    }}
                    mode="single"
                    showOutsideDays={false}
                    endMonth={
                      new Date(Date.now() + 1000 * 60 * 60 * 24 * 365 * 10)
                    }
                  />
                </PopoverContent>
              </Popover>
              <FormMessage />
            </div>
          )}
        />
      </div> */}
    </div>
  );
};

export default MilestoneFormFields;
