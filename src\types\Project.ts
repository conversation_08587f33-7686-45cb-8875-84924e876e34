export type Document = {
  _id: string;
  fileName: string;
  storagePath: string;
  category: string;
  milestone?: string;
};

export type ContractorOrg = {
  _id: string;
  name: string;
};

export type Status = "ongoing" | "completed" | "halted";

export type ProjectData = {
  _id: string;
  name: string;
  projectId: string;
  clientName: string;
  clientWhatsAppNo: string;
  startDate: string;
  duration: number;
  location: string;
  status: Status;
  architectOrg: string;
  owner: string;
  endDate: string;
  projectScope: string;
  users: string[];
  createdAt: string;
  updatedAt: string;
  __v: number;
  siteDocuments: Document[];
  contractorOrg: ContractorOrg;
  contractorOwner?: {
    _id: string;
    name: string;
    email: string;
    phone?: string;
  };
  meetingEventLink: string;
  contractorDiscussionChatGroupId: string;
  designDiscussionChatGroupId: string;
};

export type ProjectsResponse = {
  success?: boolean;
  data: ProjectData[];
  message?: string;
  totalCount?: number;
  page?: number;
  limit?: number;
};

export type ProjectAnalytics = {
  projectId: string;
  projectName: string;
  startDate: string;
  endDate: string;
  noOfWorker: number;
  workerProductivity: number;
  projectedEndDate: string;
  progress: number;
  milestones: Array<{
    _id: string;
    name: string;
    noOfWorkers: number;
    progress: number;
    startDate: string;
    endDate: string;
    projectedEndDate: string;
    workTypes: Array<{
      name: string;
      noOfWorkers: number;
      noOfDays: number;
      productivity: number;
      totalWorkDone: number;
      status: number;
    }>;
  }>;
};

export type ProjectAnalyticsTotal = {
  _id: string;
  projectName: string;
  location: string;
  projectId: string;
  startDate: string;
  status: Status;
  endDate: string;
  noOfWorker: number;
  progress: number;
  workerProductivity: number;
  projectedEndDate: string;
  // should be replaced with actual keys once api is ready
  stages: number;
  architects: number;
  expectedRevenue: number;
  margin: number;
};
