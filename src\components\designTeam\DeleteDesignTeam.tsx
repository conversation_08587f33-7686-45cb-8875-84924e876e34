"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useState } from "react";
import { toast } from "sonner";

import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogAction,
  AlertDialogCancel,
} from "@/components/ui/alertDialog";
import DeleteIcon from "../icons/Delete";
import useRemoveDesignTeamMember from "@/services/designTeam/removeDesignTeamMember";
import { useQueryClient } from "@tanstack/react-query";

type DeleteDesignTeamProps = {
  projectId: string;
  userId: string;
};

const DeleteDesignTeam = ({ projectId, userId }: DeleteDesignTeamProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const queryClient = useQueryClient();

  const { mutate, isPending } = useRemoveDesignTeamMember(() => {
    setIsOpen(false);
    toast.success("Team member removed successfully!");
    queryClient.invalidateQueries({ queryKey: ["project-analytics-complete"] });
    // queryClient.invalidateQueries({ queryKey: ["project-analytics-total"] });
  });

  const handleDelete = () => {
    mutate({ projectId, userId });
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger>
        <DeleteIcon />
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Remove team member?</AlertDialogTitle>
          <AlertDialogDescription>
            Doing this will remove this team member from the project and stages
            will no longer be assigned.
          </AlertDialogDescription>
        </AlertDialogHeader>

        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button loading={isPending} onClick={handleDelete}>
              Yes, Remove
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DeleteDesignTeam;
