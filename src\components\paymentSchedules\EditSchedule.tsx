"use client";

import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import {
  She<PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON>et<PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";
import { paymentScheduleSchema } from "@/schema/paymentSchedule";
import PaymentScheduleFormFields from "./PaymentScheduleFormFields";
import EditIcon from "@/components/icons/Edit";
import { PaymentSchedule } from "@/services/paymentSchedules/getPaymentSchedules";
import { useParams } from "next/navigation";
import { toast } from "sonner";
import { format, parseISO } from "date-fns";
import { useUpdatePaymentSchedule } from "@/services/paymentSchedules/updatePaymentSchedules";
import useGetDesignStages from "@/services/designStage/getDesignStages";
import { useSheetFormState } from "@/hooks/useSheetFormState";

const EditSchedule = ({ schedule }: { schedule: PaymentSchedule }) => {
  const [open, setOpen] = useState(false);

  const params = useParams();
  const projectId = params?.id as string;

  const { data: stages, isLoading } = useGetDesignStages({
    projectId: projectId,
    search: "",
  });

  const form = useForm<z.infer<typeof paymentScheduleSchema>>({
    resolver: zodResolver(paymentScheduleSchema),
    defaultValues: {
      scheduleName: "",
      stageId: "",
      date: undefined,
    },
  });

  const updateSchedule = useUpdatePaymentSchedule();

  const onSubmit = async (values: z.infer<typeof paymentScheduleSchema>) => {
    console.log(values, "payment values");
    if (!projectId) return;

    updateSchedule.mutate(
      {
        scheduleId: schedule?._id,
        scheduleName: values?.scheduleName,
        stageId: values?.stageId,
        date: format(new Date(values?.date), "yyyy-MM-dd"),
      },
      {
        onSuccess: () => {
          toast.success("Schedule updated successfully");
          setOpen(false);
        },
        onError: () => {
          toast.error("Failed to update schedule");
        },
      },
    );
  };

  const { handleOpenChange, handleExplicitClose } = useSheetFormState({
    form,
    setOpen,
    preserveOnImplicitClose: true, // For edit forms, preserve data on implicit close
  });

  useEffect(() => {
    if (open && schedule) {
      form.reset({
        scheduleName: schedule.scheduleName || "",
        stageId: schedule?.stageId || "",
        date: schedule.date ? parseISO(schedule.date) : undefined,
      });
    }
  }, [open, form, schedule]);

  return (
    <Sheet open={open} onOpenChange={handleOpenChange}>
      <SheetTrigger>
        <EditIcon />
      </SheetTrigger>
      <SheetContent
        className="max-w-[507px] flex flex-col gap-0"
        onExplicitClose={handleExplicitClose}
      >
        <SheetHeader>
          <SheetTitle>Edit payment schedule</SheetTitle>
        </SheetHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col justify-between flex-1"
          >
            <div className="space-y-4">
              <PaymentScheduleFormFields
                form={form}
                filteredStageId={schedule?.stageId}
              />
            </div>
            <Button
              className="ml-auto px-4 py-3 sticky bottom-0"
              type="submit"
              //   loading={isPending}
            >
              Save
            </Button>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  );
};

export default EditSchedule;
