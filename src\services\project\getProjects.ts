import { useInfiniteQuery } from "@tanstack/react-query";
import { ProjectData } from "@/types/Project";
import api from "@/lib/api-client";

type UseInfiniteProjectsParams = {
  searchTerm?: string;
  select?: string;
  itemsPerPage?: number;
};

type UseInfiniteProjectsReturn = {
  data: ProjectData[] | undefined;
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  fetchNextPage: () => void;
  hasNextPage: boolean;
  isFetchingNextPage: boolean;
};

const fetchProjects = async (
  searchTerm: string = "",
  select: string = "",
  page: number = 1,
  limit: number = 30,
): Promise<ProjectData[]> => {
  const params: Record<string, string> = {
    page: page.toString(),
    limit: limit.toString(),
  };

  if (searchTerm) params.search = searchTerm;
  if (select && select !== "all") params.select = select;

  const queryParams = new URLSearchParams(params).toString();
  const response = await api.get<ProjectData[]>(`/projects?${queryParams}`);

  return response.data || [];
};

const useGetProjects = ({
  searchTerm = "",
  select = "",
  itemsPerPage = 30,
}: UseInfiniteProjectsParams = {}): UseInfiniteProjectsReturn => {
  const {
    data,
    isLoading,
    isError,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useInfiniteQuery({
    queryKey: ["projects", searchTerm, select],
    queryFn: ({ pageParam = 1 }) =>
      fetchProjects(searchTerm, select, pageParam, itemsPerPage),
    getNextPageParam: (lastPage, allPages) => {
      return lastPage.length === itemsPerPage ? allPages.length + 1 : undefined;
    },
    refetchOnMount: true,
    refetchOnWindowFocus: true,
    initialPageParam: 1,
  });

  const flattenedData = data?.pages.flatMap((page) => page);

  return {
    data: flattenedData,
    isLoading,
    isError,
    error: error as Error | null,
    fetchNextPage,
    hasNextPage: !!hasNextPage,
    isFetchingNextPage,
  };
};

export default useGetProjects;
