import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import api from "@/lib/api-client";

const deleteTimesheet = async (id: string) => {
  const response = await api({
    url: `/time-sheet/delete/${id}`,
    method: "DELETE",
  });
  return response.data;
};

const useDeleteTimesheet = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: deleteTimesheet,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["time-sheet"] });
      toast.success("Timesheet deleted successfully!");
      onSuccess?.();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to delete timesheet",
      );
    },
  });
};

export default useDeleteTimesheet;
