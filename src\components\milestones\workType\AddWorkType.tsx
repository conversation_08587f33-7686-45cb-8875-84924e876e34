import { useState } from "react";
import { Plus } from "lucide-react";

import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import AddWorkTypeForm from "./AddWorkTypeForm";
import { Milestone } from "@/types/Milestone";

type AddWorkTypeProps = {
  milestone: Milestone;
};

const AddWorkType = ({ milestone }: AddWorkTypeProps) => {
  const [isAddWorkTypeDialogOpen, setIsAddWorkTypeDialogOpen] = useState(false);

  return (
    <>
      <Button
        onClick={() => setIsAddWorkTypeDialogOpen(true)}
        variant="link"
        className="h-[30px] gap-x-1.5 font-medium hover:no-underline"
      >
        <Plus className="size-4 stroke-[2.5]" />
        Add work type
      </Button>

      <Dialog
        open={isAddWorkTypeDialogOpen}
        onOpenChange={setIsAddWorkTypeDialogOpen}
      >
        <DialogContent
          isCloseIconVisible
          className="gap-y-[30px] sm:max-w-[510px]"
        >
          <DialogTitle>
            Add work type under {milestone.milestoneName}
          </DialogTitle>
          <AddWorkTypeForm
            milestone={milestone}
            onSuccess={() => setIsAddWorkTypeDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};

export default AddWorkType;
