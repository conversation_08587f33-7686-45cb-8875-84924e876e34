import { toast } from "sonner";
import { useMutation, useQueryClient } from "@tanstack/react-query";

import api from "@/lib/api-client";

const deleteTeamMember = async ({ userId }: { userId: string }) => {
  const response = await api({
    url: `/architect-team/delete-members/${userId}`,
    method: "PUT",
  });
  return response.data;
};

const useDeleteTeamMember = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteTeamMember,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["team-members"] });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to delete team member",
      );
    },
  });
};

export default useDeleteTeamMember;
