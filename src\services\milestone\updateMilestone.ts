import { toast } from "sonner";

import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";

type UpdateMilestone = {
  projectId: string;
  milestoneId: string;
  milestoneName?: string | undefined;
  startDate: string | undefined;
  duration: number | undefined;
  paymentDate?: string | undefined;
};

const updateMilestone = async ({
  projectId,
  milestoneId,
  ...body
}: UpdateMilestone): Promise<{ milestoneTemplateId: string }> => {
  const response = await api({
    url: `/milestones/${projectId}/${milestoneId}`,
    method: "PUT",
    data: body,
  });
  return response.data;
};

const useUpdateMilestone = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateMilestone,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["milestones"] });
      if (onSuccess) onSuccess();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to update milestone",
      );
    },
  });
};

export default useUpdateMilestone;
