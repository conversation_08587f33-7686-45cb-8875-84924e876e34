const DesignIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="18"
    height="18"
    fill="none"
    viewBox="0 0 18 18"
    className={className}
  >
    <g fill="#474747" clipPath="url(#clip0_1747_12809)">
      <path d="M5.354 9.559a.5.5 0 0 0-.854.353v2.184c0 .775.629 1.404 1.404 1.404h2.184a.5.5 0 0 0 0-1h-.612A1.976 1.976 0 0 1 5.5 10.524v-.612a.5.5 0 0 0-.146-.353"></path>
      <path d="m17.435 16.145-.783-.794a.381.381 0 0 0-.652.268c0 .21-.17.381-.381.381H3.038A1.037 1.037 0 0 1 2 14.963v-.538a.5.5 0 0 1 .5-.5h.1a.4.4 0 0 0 0-.8h-.1a.5.5 0 0 1-.5-.5V10.3a.5.5 0 0 1 .5-.5h.1a.4.4 0 0 0 0-.8h-.1a.5.5 0 0 1-.5-.5V6.3a.5.5 0 0 1 .5-.5h.1a.4.4 0 0 0 0-.8h-.1a.5.5 0 0 1-.5-.5v-.321a.821.821 0 0 1 1.4-.582l5.718 5.685a.5.5 0 0 0 .705-.709L1.855.645A.5.5 0 0 0 1 1v15.5a.5.5 0 0 0 .5.5h15.58a.5.5 0 0 0 .355-.855"></path>
      <path d="M12 15h2a1 1 0 0 0 1-1V4.859a2 2 0 0 0-.243-.956l-.907-1.668a1 1 0 0 0-.88-.5 1 1 0 0 0-.88.54l-.862 1.65A2 2 0 0 0 11 4.85V14a1 1 0 0 0 1 1m0-10.159c0-.158.038-.315.11-.455.366-.71 1.384-.709 1.767-.**************.315.123.483V11a1 1 0 0 1-2 0zm0 8.584a.61.61 0 0 1 .61-.61h.78a.61.61 0 1 1 0 1.22h-.78a.61.61 0 0 1-.61-.61"></path>
    </g>
    <defs>
      <clipPath id="clip0_1747_12809">
        <path fill="#fff" d="M0 0h18v18H0z"></path>
      </clipPath>
    </defs>
  </svg>
);

export default DesignIcon;
