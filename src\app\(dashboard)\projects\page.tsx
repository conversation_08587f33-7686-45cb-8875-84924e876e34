"use client";

import React from "react";

import ProjectsHeader from "@/components/projects/ProjectsHeader";
import ProjectsFilters from "@/components/projects/ProjectsFilters";
import ProjectsList from "@/components/projects/ProjectsList";
import { useProjectsFilters } from "@/hooks/useProjectsFilters";

const ProjectsPage: React.FC = () => {
  const { searchTerm, select, handleFiltersChange } = useProjectsFilters();

  return (
    <div className="flex flex-col h-screen">
      <ProjectsHeader />

      <div className="flex-1 overflow-auto p-5 scrollbar-hide">
        <ProjectsFilters
          initialSearchTerm={searchTerm}
          initialSelect={select}
          onFiltersChange={handleFiltersChange}
        />
        <ProjectsList searchTerm={searchTerm} select={select} />
      </div>
    </div>
  );
};

export default ProjectsPage;
