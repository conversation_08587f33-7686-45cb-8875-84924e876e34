export type Notification = {
  _id: string;
  organisationId: string;
  userId: string;
  notificationType:
    | "payment_schedule_delayed_notification"
    | "normal_notification";
  message: string;
  read: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
};

export type NotificationResponse = {
  statusCode: number;
  success: boolean;
  message: string;
  data: Notification[];
  totalCount: number;
  page: number;
  limit: number;
};

export type NotificationCount = { total: number; read: number; unread: number };
