import { useFieldArray, UseFormReturn } from "react-hook-form";
import { z } from "zod";
import { Plus, X } from "lucide-react";

import { milestoneSchema } from "@/schema/milestone";
import { Button } from "@/components/ui/button";
import { workTypeSchema } from "@/schema/workType";
import WorkTypeForm<PERSON>ield from "../workType/formFields/WorkType";
import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alertDialog";

const defaultValues: z.infer<typeof workTypeSchema> = {
  worktypeTemplateId: "",
  isCustom: false,
  duration: undefined, //initially set to 0
  totalQty: "",
  unit: "",
  optionalQty: {
    totalQty: "",
    unit: "",
  },
};

type WorkTypeFormFieldsProps = {
  form: UseFormReturn<z.infer<typeof milestoneSchema>>;
  isEdit?: boolean;
};

const WorkType = ({ form }: WorkTypeFormFieldsProps) => {
  const milestoneTemplateId = form.watch("milestoneTemplateId") || " ";

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "worktypes",
  });

  const addWorkType = () => {
    append(defaultValues);
  };

  const deleteWorkType = (index: number) => {
    remove(index);
  };

  return (
    <div className="flex flex-col gap-y-4">
      {fields.map((field, index) => (
        <div
          key={field.id}
          className="relative p-6 flex flex-col gap-y-3 rounded-xl border border-neutrals-G40"
        >
          <WorkTypeFormField
            form={form}
            fieldIndex={index}
            milestoneTemplateId={milestoneTemplateId}
          />
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <button type="button" className="absolute top-3 right-3">
                <X className="size-4 text-neutrals-G400" />
              </button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Remove work type?</AlertDialogTitle>
                <AlertDialogDescription>
                  Doing this will remove all the data you entered for the work
                  type.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction asChild>
                  <Button onClick={() => deleteWorkType(index)}>
                    Yes, Remove{" "}
                  </Button>
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      ))}
      <Button
        onClick={addWorkType}
        type="button"
        variant="link"
        className="hover:no-underline justify-start gap-x-1.5 font-medium h-[30px]"
      >
        <Plus className="size-4 5  stroke-[2.5px]" />
        Add work type
      </Button>
    </div>
  );
};

export default WorkType;
