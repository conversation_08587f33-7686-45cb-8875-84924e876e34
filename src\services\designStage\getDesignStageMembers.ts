import { useQuery } from "@tanstack/react-query";
import api from "@/lib/api-client";

const getDesignStageMembers = async (projectId: string): Promise<any> => {
  const response = await api.get(
    `/design-stage/get-design-stage-members/${projectId}`,
  );
  return response.data;
};
const useGetDesignStageMembers = (projectId: string, enabled = true) => {
  return useQuery({
    queryKey: ["design-stage-members", projectId],
    queryFn: () => getDesignStageMembers(projectId),
    enabled: !!projectId && enabled,
  });
};

export default useGetDesignStageMembers;
