- [33m2a233ca[m[33m ([m[1;36mHEAD[m[33m -> [m[1;32mtesting[m[33m, [m[1;31morigin/featureAuth[m[33m, [m[1;32mfeatureAuth[m[33m)[m userfriendly error displaying in ui implemented
- [33m0a09834[m completed the authentication flow and ui
- [33m81700b0[m cleared errors shown while executing build
- [33m61c96ca[m integrated a logout buttn on home page for testing purpose
- [33m781309a[m login and sinup page now console the Id token of the user for both google signin or email/password
- [33m6de520c[m completed ui works for all authentication page
- [33m171d237[m ui for login page completed added functionalities almost 80 percent completed
- [33md2fff1b[m refactored authentication
- [33md19293b[m created login/pages.tsx
- [33m1c49155[m[33m ([m[1;31morigin/testing[m[33m)[m initial commit for Auth, completed basic login signup forgot passowrd workflow
- [33m867660e[m[33m ([m[1;32mmain[m[33m)[m Initial commit from Create Next App

Todo : form dirty state save changes button in project details.
1.Login Scale issue
2.project card errror reset fix
3.project details page , save changes button not disabled whn form not dirty due to commmenting contractor field
4.loading button ui  
5. heading dislpay in site document page header
6.dailyupdate svg
