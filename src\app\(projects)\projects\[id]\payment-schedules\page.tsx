"use client";

import NoDataToShow from "@/components/ui/noDataToShow";
import File2 from "@/components/icons/File2";
import PaymentStatusCard from "@/components/paymentSchedules/PaymentStatusCard";
import CreateSchedule from "@/components/paymentSchedules/CreateSchedule";
import EditSchedule from "@/components/paymentSchedules/EditSchedule";
import DeleteSchedule from "@/components/paymentSchedules/DeleteSchedule";
import { useGetPaymentSchedules } from "@/services/paymentSchedules/getPaymentSchedules";
import { useParams } from "next/navigation";
import withTeamGuard from "@/components/TeamGuard";

const PaymentSchedules = () => {
  const params = useParams();
  const projectId = params?.id as string;

  const { data: schedules, isPending } = useGetPaymentSchedules(projectId);

  console.log(schedules, "data3");

  return (
    <div className="flex flex-col gap-y-5">
      <div className="flex justify-between items-center py-3.5 border-b border-neutrals-G40">
        <div className="space-y-1 text-neutrals-G900">
          <h4 className="font-semibold">Payment Schedules</h4>
          <p className="text-xs text-neutrals-G300">
            Manage all the Payment Schedules over here.
          </p>
        </div>
        <CreateSchedule />
      </div>

      <div className="flex-1 overflow-y-auto scrollbar-hide">
        {isPending ? (
          <div>loading....</div>
        ) : schedules?.data?.length === 0 ? (
          <NoDataToShow />
        ) : (
          <div className="flex flex-col gap-y-[18px]">
            {schedules?.data?.map((schedule: any, index: number) => (
              <div
                key={schedule._id}
                className="bg-white border rounded-xl border-[#D7D7D7] p-5 space-y-8"
              >
                <div className="flex justify-between items-start gap-x-3 flex-wrap">
                  <div className="space-y-2">
                    <h6 className="text-neutrals-G200 text-xs">SL</h6>
                    <p className="text-neutrals-G800 font-medium">
                      {index + 1}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h6 className="text-neutrals-G200 text-xs">
                      Schedule name
                    </h6>
                    <p className="text-neutrals-G800 font-medium">
                      {schedule.scheduleName}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h6 className="text-neutrals-G200 text-xs">
                      Assigned stage
                    </h6>
                    <p className="text-neutrals-G800 font-medium">
                      {schedule.stageDetails?.stageName || "N/A"}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h6 className="text-neutrals-G200 text-xs">Date</h6>
                    <p className="text-neutrals-G800 font-medium">
                      {schedule.date}
                    </p>
                  </div>
                  <div className="space-y-2">
                    <h6 className="text-neutrals-G200 text-xs">
                      Documents uploaded
                    </h6>
                    <p className="text-neutrals-G800 font-medium flex gap-x-[3px] items-center ">
                      {schedule?.stageDetails?.documentsCount}
                      <button className="[&>svg]:mb-0.5">
                        <File2 />
                      </button>
                    </p>
                  </div>
                  <div className="flex gap-x-3">
                    <EditSchedule schedule={schedule} />
                    <DeleteSchedule scheduleId={schedule?._id} />
                  </div>
                </div>

                <PaymentStatusCard
                  schedule={schedule}
                  totalBudget={schedules?.totalBudget}
                />
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default withTeamGuard(PaymentSchedules);
