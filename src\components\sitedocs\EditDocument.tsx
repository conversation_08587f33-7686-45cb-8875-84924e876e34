import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
// import {
//   Command,
//   CommandEmpty,
//   CommandGroup,
//   CommandInput,
//   CommandItem,
//   CommandList,
// } from "@/components/ui/command";
// import {
//   Popover,
//   PopoverContent,
//   PopoverTrigger,
// } from "@/components/ui/popover";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  useGenerateS3Url,
  useUploadToS3,
  useUpdateSiteDocument,
} from "@/services/project/sitedocs-hooks";
import useGetMilestones from "@/services/milestone/getMilestones";
import { toast } from "sonner";
import { Check, ChevronDown, Plus, X } from "lucide-react";
import CustomFileInput from "@/components/ui/fileUpload";
import EditIcon from "../icons/Edit";
import { SiteDocument } from "@/types/SiteDocuments";
// import { Input } from "../ui/input";
// import { cn } from "@/lib/utils";
import DeleteRedIcon from "../icons/DeleteRed";
import FileIcon from "../icons/File";

// const DEFAULT_CATEGORIES = ["3D Model", "Rough plan", "Estimate", "Sample"];

const formSchema = z.object({
  category: z.string().min(1, ""),
  file: z.any().optional(),
  milestoneId: z.string().min(1, ""),
});

type FormData = z.infer<typeof formSchema>;

interface EditDocumentDialogProps {
  document: SiteDocument;
  profileId: string;
  onEdit: () => void;
}
const EditDocumentDialog: React.FC<EditDocumentDialogProps> = ({
  document,
  profileId,
  onEdit,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [showFileInput, setShowFileInput] = useState(false);
  const [isFileReady, setIsFileReady] = useState(false);
  // const [categoryOpen, setCategoryOpen] = useState(false);
  // const [searchQuery, setSearchQuery] = useState("");

  const { data: milestones } = useGetMilestones({ projectId: profileId });
  const generateS3Url = useGenerateS3Url();
  const uploadToS3 = useUploadToS3();
  const updateDocument = useUpdateSiteDocument(profileId);

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      category: document.category,
      milestoneId: document.milestoneId?._id || "",
    },
  });

  useEffect(() => {
    if (isOpen) {
      form.reset({
        category: document.category,
        milestoneId: document.milestoneId?._id || "",
      });
      setShowFileInput(false);
      setIsFileReady(false);
      // setSearchQuery("");
    }
  }, [isOpen, document, form]);

  const handleClose = () => {
    setIsOpen(false);
  };

  const handleFileChange = (file: File | null) => {
    if (file) {
      form.setValue("file", file, { shouldDirty: true });
    }
  };

  const handleFileUpload = () => {
    if (form.getValues("file")) {
      setIsFileReady(true);
      toast.success("File ready for upload");
    }
  };

  // const handleCreateCategory = () => {
  //   form.setValue("category", searchQuery, { shouldDirty: true });
  //   setCategoryOpen(false);
  // };

  // const filteredCategories = DEFAULT_CATEGORIES.filter((category) =>
  //   category.toLowerCase().includes(searchQuery.toLowerCase()),
  // );

  const handleRemoveFile = () => {
    setShowFileInput(true);
    form.setValue("file", null);
    setIsFileReady(false);
  };

  const onSubmit = async (data: FormData) => {
    try {
      let storagePath = document.storagePath;
      let fileName = document.fileName;

      if (showFileInput && data.file) {
        const signedUrl = await generateS3Url.mutateAsync({
          fileName: data.file.name,
          projectId: profileId,
        });

        await uploadToS3.mutateAsync({
          signedUrl,
          file: data.file,
        });

        const url = new URL(signedUrl);
        storagePath = url.pathname;
        fileName = data.file.name;
      }

      await updateDocument.mutateAsync({
        documentId: document._id,
        data: {
          category: data.category,
          fileName,
          storagePath,
          milestoneId: data.milestoneId,
        },
      });

      setIsOpen(false);
      onEdit();
      toast.success("Document updated successfully");
    } catch (error) {
      console.error("Error updating document:", error);
      toast.error("Failed to update document. Please try again.");
    }
  };

  return (
    <>
      <button onClick={() => setIsOpen(true)}>
        <EditIcon />
      </button>

      <Dialog open={isOpen} onOpenChange={setIsOpen}>
        <DialogContent className="w-[510px] p-6">
          <DialogTitle className="mb-[30px] flex justify-between items-center">
            Edit site documents
            <DialogClose asChild>
              <Button type="button" variant="outline" onClick={handleClose}>
                <X className="h-6 w-6" />
              </Button>
            </DialogClose>
          </DialogTitle>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="file"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-Grey-Dark-Grey text-sm">
                      Upload files
                    </FormLabel>
                    <FormControl>
                      {showFileInput ? (
                        <CustomFileInput
                          onFileChange={(file) => {
                            handleFileChange(file);
                          }}
                          onFileUpload={handleFileUpload}
                        />
                      ) : (
                        <div className="px-3 py-2.5 bg-white rounded-md shadow-input border border-border-gray flex justify-between items-center">
                          <span className="text-name-title text-sm font-normal truncate inline-flex items-center gap-1">
                            <FileIcon className="h-5 w-5" /> {document.fileName}
                          </span>
                          <button
                            onClick={handleRemoveFile}
                            className="text-gray-500 hover:text-gray-700"
                          >
                            <DeleteRedIcon className="h-5 w-5" />
                          </button>
                        </div>
                      )}
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="milestoneId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-Grey-Dark-Grey text-sm">
                      Applicable to milestones till
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="shadow-input">
                          <SelectValue placeholder="Select milestone" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {milestones?.map((milestone) => (
                          <SelectItem key={milestone._id} value={milestone._id}>
                            {milestone.isCustom
                              ? milestone.milestoneName
                              : milestone.milestoneTemplateId?.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />

              <div className="flex justify-end ">
                <Button
                  type="submit"
                  className="px-4 py-3 bg-primary text-white hover:bg-primary/90"
                  loading={updateDocument.isPending}
                  disabled={
                    !form.formState.isDirty ||
                    (showFileInput && !isFileReady) ||
                    generateS3Url.isPending ||
                    uploadToS3.isPending
                  }
                >
                  Save
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default EditDocumentDialog;
