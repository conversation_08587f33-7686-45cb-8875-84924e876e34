function InProgress({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="35"
      height="35"
      viewBox="0 0 35 35"
      fill="none"
      className={className}
    >
      <path
        d="M17.5 4.75928C14.9801 4.75928 12.5168 5.50651 10.4216 6.90648C8.32644 8.30645 6.69343 10.2963 5.72911 12.6243C4.7648 14.9524 4.51249 17.5141 5.00409 19.9856C5.4957 22.4571 6.70913 24.7273 8.49096 26.5091C10.2728 28.2909 12.543 29.5043 15.0144 29.9959C17.4859 30.4875 20.0476 30.2352 22.3757 29.2709C24.7038 28.3066 26.6936 26.6736 28.0936 24.5784C29.4935 22.4832 30.2408 20.0199 30.2408 17.5C30.2369 14.1221 28.8933 10.8837 26.5048 8.49521C24.1163 6.10669 20.8779 4.76313 17.5 4.75928ZM17.5 28.4206C14.6037 28.4206 11.826 27.2701 9.77797 25.2221C7.72995 23.1741 6.57939 20.3963 6.57939 17.5C6.57939 14.6037 7.72995 11.826 9.77797 9.77796C11.826 7.72995 14.6037 6.57938 17.5 6.57938V17.5L25.2182 25.2182C24.2061 26.2341 23.0032 27.04 21.6787 27.5896C20.3541 28.1392 18.9341 28.4216 17.5 28.4206Z"
        fill="#DA9500"
      />
    </svg>
  );
}

export default InProgress;
