import axios from "axios";
import { useQuery } from "@tanstack/react-query";
import { ProjectData } from "@/types/Project";

const getProjectByIdWithToken = async (
  id: string,
  token: string,
): Promise<ProjectData | null> => {
  try {
    const response = await axios.get<ProjectData>(
      `${process.env.NEXT_PUBLIC_API_URL}/projects/${id}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    );
    if (response.data) {
      return response.data;
    }
    return null;
  } catch (error) {
    console.error(`Error fetching project with ID ${id} using token:`, error);
    return null;
  }
};

const useGetProjectByIdWithToken = (id: string, token: string) => {
  return useQuery({
    queryKey: ["project", id, token],
    queryFn: () => getProjectByIdWithToken(id, token),
    enabled: !!id && !!token,
  });
};

export default useGetProjectByIdWithToken;
