import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";

import api from "@/lib/api-client";

const addFolderToTeamVault = async (body: {
  name: string;
  parentId: string;
}) => {
  const { data } = await api({
    url: `/vault/folder`,
    method: "POST",
    data: body,
  });
  return data;
};

const useAddFolderToTeamVault = (onSuccess?: () => void) => {
  return useMutation({
    mutationFn: addFolderToTeamVault,
    onSuccess: () => {
      onSuccess?.();
    },
    onError: () => {
      toast.error("Failed to add folder to team vault");
    },
  });
};

export default useAddFolderToTeamVault;
