import { useEffect } from "react";
import useGetDashboardAlerts from "@/services/dashboard/getDashboardAlerts";
import DrawingsCard from "./DrawingsCard";
import NoDataToShow from "@/components/ui/noDataToShow";
import Loader from "@/components/ui/loader";
import ErrorText from "@/components/ui/errortext";

const DrawingsList = () => {
  const {
    data,
    isLoading,
    isError,
    hasNextPage,
    fetchNextPage,
    isFetchingNextPage,
  } = useGetDashboardAlerts("drawing_delayed");

  useEffect(() => {
    const handleScroll = () => {
      const scrolledToBottom =
        window.innerHeight + window.scrollY >=
        document.documentElement.scrollHeight - 100;
      if (scrolledToBottom && hasNextPage && !isFetchingNextPage) {
        fetchNextPage();
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, [hasNextPage, isFetchingNextPage, fetchNextPage]);

  if (isLoading) return <Loader />;
  if (isError) return <ErrorText entity="drawings" />;

  const allAlerts = data?.pages?.flat() ?? [];
  if (!allAlerts.length) return <NoDataToShow className="mt-24" />;

  return (
    <>
      <div className="flex items-start text-neutrals-G600 text-xs px-3.5 gap-x-4 pt-2">
        <div className="basis-1/2">MILESTONE</div>
        <div className="basis-1/2">EXPECTED UPLOAD DATE</div>
      </div>
      {allAlerts.map((alert) => (
        <DrawingsCard key={alert._id} alert={alert} />
      ))}
      {isFetchingNextPage && (
        <div>
          <Loader className="h-auto mt-4" />
        </div>
      )}
    </>
  );
};

export default DrawingsList;
