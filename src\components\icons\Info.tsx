const InfoIcon = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="17"
    height="16"
    fill="none"
    viewBox="0 0 17 16"
    className={className}
  >
    <path
      fill="#2F80ED"
      d="M7.854 7.342a.646.646 0 1 1 1.291 0v3.875a.646.646 0 1 1-1.291 0zm.645-3.184a.646.646 0 1 0 0 1.292.646.646 0 0 0 0-1.292"
    ></path>
    <path
      fill="#2F80ED"
      fillRule="evenodd"
      d="M8.5 1.542a6.458 6.458 0 1 0 0 12.916 6.458 6.458 0 0 0 0-12.916M3.332 8a5.167 5.167 0 1 0 10.333 0A5.167 5.167 0 0 0 3.333 8"
      clipRule="evenodd"
    ></path>
  </svg>
);

export default InfoIcon;
