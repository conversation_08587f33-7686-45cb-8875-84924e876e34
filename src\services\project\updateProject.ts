import api from "@/lib/api-client";
import { ProjectData } from "@/types/Project";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

export const UPDATE_PROJECT_MUTATION_KEY = ["update-project"];

type UpdateProjectPayload = {
  id: string;
  // Basic Details
  name: string;
  projectType?: string;
  numberOfFloors?: number;
  projectScope?: string;
  expectedRevenue?: number;
  requiredMargin?: number;
  // Timeline fields
  designStartDate?: string;
  designEndDate?: string;
  contractorStartDate?: string;
  contractorEndDate?: string;
  // Client Details
  clientName: string;
  clientWhatsAppNo: string;
  location: string;
  // Other fields
  contractorOrg?: string;
  status: string;
  startDate?: string;
  endDate?: string;
  duration?: number;
};

const updateProject = async ({
  id,
  name,
  projectType,
  numberOfFloors,
  projectScope,
  expectedRevenue,
  requiredMargin,
  designStartDate,
  designEndDate,
  contractorStartDate,
  contractorEndDate,
  clientName,
  clientWhatsAppNo,
  location,
  contractorOrg,
  status,
  startDate,
  endDate,
  duration,
}: UpdateProjectPayload) => {
  try {
    const response = await api.put<ProjectData>(`/projects/${id}`, {
      name,
      projectType,
      numberOfFloors,
      projectScope,
      expectedRevenue,
      requiredMargin,
      designStartDate,
      designEndDate,
      contractorStartDate,
      contractorEndDate,
      clientName,
      clientWhatsAppNo,
      location,
      contractorOrg,
      status,
      startDate,
      endDate,
      duration,
    });
    return response.data;
  } catch (error) {
    console.error("Error updating project:", error);
    throw error;
  }
};

const useUpdateProject = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationKey: UPDATE_PROJECT_MUTATION_KEY,
    mutationFn: updateProject,
    onSuccess: () => {
      // Invalidate the specific project
      queryClient.invalidateQueries({
        queryKey: ["project"],
      });

      // Invalidate all projects queries
      queryClient.invalidateQueries({ queryKey: ["projects"] });
      queryClient.invalidateQueries({ queryKey: ["design-project-analytics"] });

      toast.success("Project updated successfully!");
    },
    onError: (error: any) => {
      //TODO :  if user select end date first and the choose start dat, then validation should be done
      toast.error(
        error.response?.data?.message ||
          error.message ||
          "Failed to update project.",
      );
    },
  });
};

export default useUpdateProject;
