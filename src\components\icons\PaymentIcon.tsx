const PaymentIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    className={className}
  >
    <path
      d="M11.2498 10.3525C10.7798 10.3525 10.3846 10.1922 10.064 9.87163C9.74344 9.55108 9.58316 9.1558 9.58316 8.6858C9.58316 8.2158 9.74344 7.82052 10.064 7.49996C10.3846 7.17941 10.7798 7.01913 11.2498 7.01913C11.7198 7.01913 12.1151 7.17941 12.4357 7.49996C12.7562 7.82052 12.9165 8.2158 12.9165 8.6858C12.9165 9.1558 12.7562 9.55108 12.4357 9.87163C12.1151 10.1922 11.7198 10.3525 11.2498 10.3525ZM6.08983 13.0133C5.71928 13.0133 5.40233 12.8814 5.139 12.6175C4.87511 12.3536 4.74316 12.0366 4.74316 11.6666V5.70496C4.74316 5.33496 4.87511 5.0183 5.139 4.75496C5.40289 4.49163 5.71955 4.35969 6.089 4.35913H16.4098C16.7804 4.35913 17.0973 4.4908 17.3607 4.75413C17.624 5.01746 17.7559 5.33441 17.7565 5.70496V11.6666C17.7565 12.0366 17.6246 12.3533 17.3607 12.6166C17.0973 12.8811 16.7807 13.0133 16.4107 13.0133H6.08983ZM6.92316 12.18H15.5765C15.5765 11.8066 15.7084 11.4889 15.9723 11.2266C16.2357 10.9644 16.5523 10.8333 16.9223 10.8333V6.5383C16.5501 6.5383 16.2329 6.40663 15.9707 6.1433C15.7079 5.87941 15.5765 5.56246 15.5765 5.19246H6.92316C6.92316 5.56524 6.79122 5.88274 6.52733 6.14496C6.264 6.40719 5.94733 6.5383 5.57733 6.5383V10.8333C5.94955 10.8333 6.26705 10.9652 6.52983 11.2291C6.79205 11.4925 6.92316 11.8091 6.92316 12.1791M15.2882 15.5125H3.58983C3.21928 15.5125 2.90233 15.3808 2.639 15.1175C2.37511 14.8536 2.24316 14.5366 2.24316 14.1666V6.82663H3.0765V14.1666C3.0765 14.2944 3.12983 14.4119 3.2365 14.5191C3.34372 14.6264 3.4615 14.68 3.58983 14.68H15.2882V15.5125ZM6.08983 12.1791H5.5765V5.1933H6.08983C5.95094 5.1933 5.83066 5.24413 5.729 5.3458C5.62733 5.44746 5.5765 5.56746 5.5765 5.7058V11.6666C5.5765 11.8055 5.62733 11.9258 5.729 12.0275C5.83066 12.1291 5.95094 12.18 6.08983 12.18"
      fill="currentColor"
    />
  </svg>
);

export default PaymentIcon;
