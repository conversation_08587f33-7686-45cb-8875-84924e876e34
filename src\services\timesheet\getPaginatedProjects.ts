import useGetProjectsForTimesheet from "@/services/timesheet/getProjectsForTimesheet";
import { useEffect, useMemo } from "react";
import { useInView } from "react-intersection-observer";

export const useGetPaginatedProjects = () => {
  const { ref, inView } = useInView();

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, ...rest } =
    useGetProjectsForTimesheet({ search: "" });

  const projects = useMemo(
    () => (data ? data.pages.flatMap((page) => page.data) : []),
    [data],
  );

  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, fetchNextPage, hasNextPage, isFetchingNextPage]);

  return { data: projects, ...rest, isFetchingNextPage, ref };
};
