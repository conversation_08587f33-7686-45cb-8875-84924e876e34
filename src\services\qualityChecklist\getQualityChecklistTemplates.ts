import { useQuery } from "@tanstack/react-query";

import api from "@/lib/api-client";
import { QualityChecklistData } from "@/types/QualityChecklist";

type GetQualityChecklistTemplates = {
  worktypeId: string;
};

const getQualityChecklistTemplates = async ({
  worktypeId,
}: GetQualityChecklistTemplates): Promise<QualityChecklistData[]> => {
  const response = await api.get(`/quality-checklist/templates/${worktypeId}`);
  return response.data;
};

const useGetQualityChecklistTemplates = ({
  worktypeId,
}: {
  worktypeId: string;
}) => {
  return useQuery({
    queryKey: ["quality-checklist-templates", worktypeId],
    queryFn: () => getQualityChecklistTemplates({ worktypeId }),
    enabled: !!worktypeId,
  });
};

export default useGetQualityChecklistTemplates;
