import { ComponentProps } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { Form } from "@/components/ui/form";
import { workTypeSchema } from "@/schema/workType";
import WorkTypeF<PERSON><PERSON>ield from "./formFields/WorkType";
import { Button } from "@/components/ui/button";
import useCreateWorkType, {
  CreateWorkType,
} from "@/services/milestone/createWorkType";
import { toast } from "sonner";
import { UseMutateFunction } from "@tanstack/react-query";
import { Milestone } from "@/types/Milestone";

type SubmitButtonProps = ComponentProps<typeof Button> & {
  onButtonClick: (
    mutate: UseMutateFunction<any, any, CreateWorkType, unknown>,
  ) => void;
  onSuccess?: () => void;
};

const SubmitButton = ({
  onButtonClick,
  onSuccess,
  children,
  ...props
}: SubmitButtonProps) => {
  const { mutate, isPending } = useCreateWorkType(onSuccess);

  return (
    <Button
      onClick={(e) => {
        e.preventDefault();
        onButtonClick(mutate);
      }}
      type="button"
      loading={isPending}
      {...props}
    >
      {children}
    </Button>
  );
};

type AddWorkTypeFormProps = {
  milestone: Milestone;
  onSuccess?: () => void;
};

const showSuccessToast = () => {
  toast.success("Work type created successfully");
};

const AddWorkTypeForm = ({ milestone, onSuccess }: AddWorkTypeFormProps) => {
  const { projectId, _id: milestoneId, milestoneTemplateId } = milestone;

  const handleSuccess = () => {
    onSuccess?.();
    showSuccessToast();
  };

  const form = useForm<z.infer<typeof workTypeSchema>>({
    resolver: zodResolver(workTypeSchema),
    defaultValues: {
      worktypeTemplateId: "",
      duration: undefined, //initially set to 0
      totalQty: "",
      unit: "",
      optionalQty: {
        totalQty: "",
        unit: "",
      },
    },
  });

  const handleSubmit = (
    mutate: UseMutateFunction<any, any, CreateWorkType, unknown>,
  ) => {
    form.handleSubmit((data) => {
      const payload = {
        projectId,
        milestoneId,
        duration: data.duration || undefined,
        totalQuantityOptionalUnit:
          Number(data.optionalQty.totalQty) || undefined,
        optionalUnit: data.optionalQty.unit || undefined,
        startDate: data.startDate?.toLocaleDateString("en-CA") || undefined,
        totalQuantity: Number(data.totalQty) || undefined,
        unit: data.unit || undefined,
      };

      if (data.isCustom) {
        mutate({
          ...payload,
          worktypeName: data.worktypeName,
          isCustom: true,
          worktypeTemplateId: undefined,
        });
      } else {
        mutate({
          ...payload,
          worktypeTemplateId: data.worktypeTemplateId,
          worktypeName: undefined,
          isCustom: false,
        });
      }
    })();
  };

  return (
    <Form {...form}>
      <form className="flex flex-col gap-y-3">
        <WorkTypeFormField
          milestoneTemplateId={milestoneTemplateId?._id}
          form={form}
        />
        <SubmitButton
          onButtonClick={(mutate) => handleSubmit(mutate)}
          onSuccess={handleSuccess}
          className="px-4 mt-3 ml-auto w-[65px]"
        >
          Save
        </SubmitButton>
      </form>
    </Form>
  );
};

export default AddWorkTypeForm;
