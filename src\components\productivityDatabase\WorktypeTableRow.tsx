// import { useParams } from "next/navigation";

// import { ProductivityDatabase } from "@/types/ProductivityDatabase";
// import InfoIcon from "../icons/Info";
// import { TableCell, TableRow } from "../ui/table";
// import ModifyQuantity from "./ModifyQuantity";

// type WorkTypeTableRowProps = {
//   milestone: { _id: string; name: string };
//   worktype: ProductivityDatabase["worktypes"][0];
// };

// const WorkTypeTableRow = ({ milestone, worktype }: WorkTypeTableRowProps) => {
//   const { id } = useParams() as { id: string };

//   const noOfRowsSpanForWorkType =
//     worktype.standardUnits.length + worktype.optionalUnits.length;

//   const noOfRowsSpanForStandardUnits = worktype.standardUnits.length;

//   function formatNumber(value: number | string) {
//     const num = Number(value);
//     return Number.isInteger(num) ? num : num.toFixed(2);
//   }

//   return (
//     <>
//       <TableRow suppressHydrationWarning={true} className="*:text-center">
//         <TableCell rowSpan={noOfRowsSpanForWorkType} className="!text-left">
//           {worktype.name}
//         </TableCell>
//         <TableCell
//           rowSpan={noOfRowsSpanForStandardUnits}
//           className="!text-left"
//         >
//           Standard unit{" "}
//         </TableCell>
//         <TableCell>
//           {formatNumber(worktype.standardUnits[0]?.minQuantity || "--")}
//         </TableCell>
//         <TableCell>
//           {formatNumber(worktype.standardUnits[0]?.maxQuantity || "--")}
//         </TableCell>
//         <TableCell>{worktype.standardUnits[0]?.unit.name || "--"}</TableCell>
//         <TableCell rowSpan={noOfRowsSpanForWorkType}>
//           <ModifyQuantity
//             projectId={id}
//             milestone={milestone}
//             worktype={worktype}
//           />
//         </TableCell>
//       </TableRow>
//       {worktype.standardUnits.slice(1).map((standardUnit) => (
//         <TableRow key={crypto.randomUUID()} className="*:text-center">
//           <TableCell>{formatNumber(standardUnit.minQuantity)}</TableCell>
//           <TableCell>{formatNumber(standardUnit.maxQuantity)}</TableCell>
//           <TableCell>{standardUnit.unit.name}</TableCell>
//         </TableRow>
//       ))}

//       {worktype.optionalUnits.map((optionalUnit) => (
//         <TableRow key={crypto.randomUUID()} className="*:text-center">
//           <TableCell>
//             <div className="flex gap-x-2.5 items-center">
//               Optional unit <InfoIcon />
//             </div>
//           </TableCell>
//           <TableCell>{formatNumber(optionalUnit.minQuantity)}</TableCell>
//           <TableCell>{formatNumber(optionalUnit.maxQuantity)}</TableCell>
//           <TableCell>{optionalUnit.unit.name}</TableCell>
//         </TableRow>
//       ))}
//     </>
//   );
// };

// export default WorkTypeTableRow;
