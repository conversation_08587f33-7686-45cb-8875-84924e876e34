import React from "react";

import PageHeader from "@/components/layout/pageHeader/PageHeader";
import CreateProject from "@/components/projects/createProject/CreateProject";
import { getIsTeamMemberFromCookies, getIsAdminFromCookies } from "@/lib/utils";

const ProjectsHeader: React.FC = React.memo(() => {
  const isTeam = getIsTeamMemberFromCookies();
  const isAdmin = getIsAdminFromCookies();

  return (
    <PageHeader>
      <div>
        <PageHeader.Heading>Projects</PageHeader.Heading>
        <PageHeader.Description>
          Manage all projects here.
        </PageHeader.Description>
      </div>
      {(!isTeam || isAdmin) && <CreateProject />}
    </PageHeader>
  );
});

ProjectsHeader.displayName = "ProjectsHeader";

export default ProjectsHeader;
