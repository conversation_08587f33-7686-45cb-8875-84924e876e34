import Image from "next/image";
import MetricItem from "./MetricsItems";

const AnalyticsProjetctSection = ({
  title,
  imageSrc,
  ongoing,
  halted,
  completed,
}: {
  title: string;
  imageSrc: string;
  ongoing: number;
  halted: number;
  completed: number;
}) => {
  return (
    <div className="flex-1 flex flex-col gap-4">
      <h3 className="text-sm text-neutrals-G600">{title}</h3>
      <div className="bg-white rounded-xl border border-neutrals-G40 shadow-lg flex items-center min-h-[203px] overflow-hidden p-4">
        <Image
          src={imageSrc}
          className="w-[228px] h-[171px] rounded-xl object-cover shrink-0"
          width={228}
          height={171}
          alt={title}
        />
        <div className="flex-1 min-h-[171px] flex flex-col gap-5 pl-6">
          <MetricItem type="ongoing" value={ongoing} label="Ongoing Projects" />
          <MetricItem type="halted" value={halted} label="Halted Projects" />
          <MetricItem
            type="completed"
            value={completed}
            label="Completed Projects"
          />
        </div>
      </div>
    </div>
  );
};

export default AnalyticsProjetctSection;
