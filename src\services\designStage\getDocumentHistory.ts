import { useQuery } from "@tanstack/react-query";

import api from "@/lib/api-client";
import { DocumentHistory } from "@/types/DesignStage";

export type GetDocumentHistory = {
  stageId: string;
};

const GetDocumentHistory = async ({
  stageId,
}: GetDocumentHistory): Promise<DocumentHistory[]> => {
  const response = await api.get(`/design-stage/document-history/${stageId}`);
  return response.data;
};

type UseGetDocumentHistory = GetDocumentHistory;

const useGetDocumentHistory = ({ stageId }: UseGetDocumentHistory) => {
  return useQuery({
    queryKey: ["design-stage-document-history", stageId],
    queryFn: async () => GetDocumentHistory({ stageId }),
    enabled: !!stageId,
  });
};

export default useGetDocumentHistory;
