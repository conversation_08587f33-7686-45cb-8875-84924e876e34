import React from "react";

const NotificationPanelContext = React.createContext({ onClose: () => {} });

export const useNotificationPanelContext = () => {
  const context = React.useContext(NotificationPanelContext);
  if (!context) {
    throw new Error(
      "useNotificationPanelContext must be used within a NotificationPanelWrapper",
    );
  }
  return context;
};

interface NotificationPanelWrapperProps {
  showNotifications: boolean;
  onClose: () => void;
  children: React.ReactNode;
}

const NotificationPanelWrapper: React.FC<NotificationPanelWrapperProps> = ({
  showNotifications,
  onClose,
  children,
}) => {
  if (!showNotifications) return null;

  return (
    <div
      className="fixed top-0 right-0 w-full h-screen bg-transparent z-[1000]"
      onClick={(e) => e.stopPropagation()}
    >
      <div
        className="absolute top-0 right-0 h-screen bg-white shadow-lg z-[1000] overflow-y-auto scrollbar-hide"
        onClick={(e) => e.stopPropagation()}
      >
        <NotificationPanelContext.Provider value={{ onClose }}>
          {children}
        </NotificationPanelContext.Provider>
      </div>
      <div
        className="fixed top-0 left-0 w-full h-screen bg-black opacity-50 z-40"
        onClick={onClose}
      />
    </div>
  );
};

export default NotificationPanelWrapper;
