import { useInfiniteQuery } from "@tanstack/react-query";
import api from "@/lib/api-client";
import { Alert } from "@/types/DelayedDrawings";

interface APIResponse {
  statusCode: number;
  success: boolean;
  message: string;
  data: Alert[];
  totalCount: number;
  page: number;
  limit: number;
}

const getDashboardAlerts = async (
  type: string,
  page: number,
  limit: number = 10,
): Promise<Alert[]> => {
  const response = await api.get<any, APIResponse>(
    `dashboard-alerts/dashboard/${type}`,
    {
      params: { page, limit },
    },
  );
  return response.data;
};

const useGetDashboardAlerts = (type: string) => {
  return useInfiniteQuery<Alert[], Error>({
    queryKey: ["alerts", type],
    queryFn: ({ pageParam = 1 }) =>
      getDashboardAlerts(type, pageParam as number),
    getNextPageParam: (_, pages) => {
      if (pages[pages.length - 1]?.length < 10) return undefined;
      return pages.length + 1;
    },
    initialPageParam: 1,
  });
};

export default useGetDashboardAlerts;
