import { useQuery } from "@tanstack/react-query";
import api from "@/lib/api-client";
import { QualityChecklist } from "@/types/QualityChecklist";

const getQualityChecklist = async (
  projectId: string,
): Promise<QualityChecklist[]> => {
  const response = await api.get(`/quality-checklist/${projectId}`);
  // console.log(response.data);
  return response.data;
};

const useGetQualityChecklist = (projectId: string) => {
  return useQuery({
    queryKey: ["quality-checklist", projectId],
    queryFn: () => getQualityChecklist(projectId),
    refetchOnWindowFocus: true,
    staleTime: 0,
  });
};

export default useGetQualityChecklist;
