import { useQuery } from "@tanstack/react-query";

import api from "@/lib/api-client";
import { DashboardAnalytics } from "@/types/DashboardAnalytics";

const getDashboardAnalytics = async (): Promise<DashboardAnalytics> => {
  const response = await api.get("/analytics/dashboard-analytics-architect");
  return response.data;
};

const useGetDashboardAnalytics = () => {
  return useQuery({
    queryKey: ["dashboard-analytics"],
    queryFn: getDashboardAnalytics,
  });
};

export default useGetDashboardAnalytics;
