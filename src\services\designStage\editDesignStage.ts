import { z } from "zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import api from "@/lib/api-client";
import { designStageSchema } from "@/schema/designStage";

type EditDesignStage = Omit<
  z.infer<typeof designStageSchema>,
  "startDate" | "endDate"
> & {
  startDate: string;
  stageId: string | undefined;
};

const editDesignStage = async ({ stageId, ...body }: EditDesignStage) => {
  const response = await api({
    url: `/design-stage/edit/${stageId}`,
    method: "PUT",
    data: body,
  });

  return response.data;
};

const useEditDesignStage = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: editDesignStage,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["design-stage"] });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to update design stage",
      );
    },
  });
};

export default useEditDesignStage;
