import { StateCreator } from "zustand";

import { CreatedMessage } from "@/types/Socket";

export interface DesignDiscussionSlice {
  designDiscussionMessages: { [id: string]: CreatedMessage[] };
  getDesignDiscussionMessagesByChatId: (chatId: string) => CreatedMessage[];
  updateDesignDiscussionMessages: (newMessages: {
    [id: string]: CreatedMessage[];
  }) => void;
  replaceDesignDiscussionMessagesWithChatId: (
    chatId: string,
    newMessages: CreatedMessage[],
  ) => void;
}

export const designDiscussionSlice: StateCreator<DesignDiscussionSlice> = (
  set,
  get,
) => ({
  designDiscussionMessages: {},
  getDesignDiscussionMessagesByChatId: (chatId) => {
    return get().designDiscussionMessages[chatId];
  },
  updateDesignDiscussionMessages: (newMessages) => {
    set({ designDiscussionMessages: newMessages });
  },

  replaceDesignDiscussionMessagesWithChatId: (chatId, newMessages) => {
    set({
      designDiscussionMessages: {
        ...get().designDiscussionMessages,
        [chatId]: newMessages,
      },
    });
  },
});
