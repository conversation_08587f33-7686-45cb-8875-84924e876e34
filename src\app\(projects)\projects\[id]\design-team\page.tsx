"use client";

import { useParams, useRouter, useSearchParams } from "next/navigation";
import { useState } from "react";

import Search from "@/components/icons/Search";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import AddDesignTeam from "@/components/designTeam/AddDesignTeam";
import DeleteDesignTeam from "@/components/designTeam/DeleteDesignTeam";
import useGetDesignTeamMembers from "@/services/designTeam/getDesignTeamMembers";
import Loader from "@/components/ui/loader";
import ErrorText from "@/components/ui/errortext";
import NoDataToShow from "@/components/ui/noDataToShow";
import { useAuth } from "@/app/contexts/AuthContext";

const DesignTeam = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const searchTerm = searchParams.get("search") || "";

  const { id } = useParams() as { id: string };

  const [search, setSearch] = useState(searchTerm);

  const { data, isPending, isError } = useGetDesignTeamMembers({
    projectId: id,
    search: searchTerm,
  });

  const handleSearch = () => {
    router.push(`?search=${search}`);
  };

  const { isTeamMember: isTeam } = useAuth();

  if (isTeam === null) return null;
  if (isPending) return <Loader />;
  if (isError) return <ErrorText entity="design team members" />;

  return (
    <div className="flex flex-col gap-y-3">
      <div className="flex justify-between items-center py-3.5 border-b border-neutrals-G40">
        <div className="space-y-1 text-neutrals-G900">
          <h4 className="font-semibold">Design Team</h4>
          <p className="text-xs text-neutrals-G300">
            Assign your design team for this project
          </p>
        </div>
        {/* {!isTeam && */}
        <AddDesignTeam projectId={id} />
        {/* } */}
      </div>
      <div className="flex-1 overflow-y-auto scrollbar-hide">
        <form
          onSubmit={(e) => {
            e.preventDefault();
            handleSearch();
          }}
          className="flex w-fit bg-neutrals-G40 rounded-lg overflow-hidden mb-4"
        >
          <Input
            type="search"
            placeholder="Search by name or ID"
            className="w-1/4 min-w-[297px] h-[2.25rem] border-none bg-inherit placeholder:text-neutrals-G400 placeholder:text-sm placeholder:font-normal"
            value={search}
            onChange={(e) => setSearch(e.currentTarget.value)}
          />
          <button className="pr-3 py-2">
            <Search />
          </button>
        </form>
        {data.length === 0 ? (
          <NoDataToShow />
        ) : (
          <div className="p-3 rounded-xl border border-gray">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-10">SI</TableHead>
                  <TableHead>Team member</TableHead>
                  <TableHead>Role</TableHead>
                  <TableHead>Stages assigned</TableHead>
                  {/* <TableHead>Assigned hours</TableHead> */}
                  <TableHead>Uploads</TableHead>
                  <TableHead className="w-12">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.map((member, index) => (
                  <TableRow key={member.userId} className="hover:bg-gray-50">
                    <TableCell className="font-medium">{++index}</TableCell>
                    <TableCell className="break-all">{member.name}</TableCell>
                    <TableCell>{member.role}</TableCell>
                    <TableCell>
                      {member.totalStagesAssigned.toString()}
                    </TableCell>
                    {/* <TableCell>
                      {member.totalHoursAssigned}{" "}
                      {member.totalHoursAssigned === 1 ? "hour" : "hours"}
                    </TableCell> */}
                    <TableCell>{member.userUploads}</TableCell>
                    <TableCell>
                      <div className="flex justify-end ">
                        {/* {!isTeam && ( */}
                        <DeleteDesignTeam
                          projectId={id}
                          userId={member.userId}
                        />
                        {/* )} */}
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </div>
    </div>
  );
};

export default DesignTeam;
