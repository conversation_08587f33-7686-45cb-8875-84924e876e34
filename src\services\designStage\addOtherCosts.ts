import { z } from "zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import api from "@/lib/api-client";
import { otherCostSchema } from "@/schema/designStage";

type AddOtherCosts = z.infer<typeof otherCostSchema>;

const addOtherCosts = async (body: AddOtherCosts) => {
  const response = await api({
    url: "/design-stage/add-other-cost",
    method: "PUT",
    data: body,
  });

  return response.data;
};

const useAddOtherCosts = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: addOtherCosts,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["design-stage"] });
      queryClient.invalidateQueries({ queryKey: ["design-stage-budget"] });

      onSuccess?.();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to add other costs",
      );
    },
  });
};

export default useAddOtherCosts;
