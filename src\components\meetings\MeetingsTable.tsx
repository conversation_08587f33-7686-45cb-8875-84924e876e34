// import React, { useEffect } from "react";
// import { useInView } from "react-intersection-observer";
// import { format } from "date-fns";
// import {
//   Table,
//   TableHeader,
//   TableBody,
//   TableHead,
//   TableRow,
//   TableCell,
// } from "@/components/ui/table";
// import { Meeting, MeetingTableData } from "@/types/Meetings";
// import { InfiniteData } from "@tanstack/react-query";
// import Loader from "../ui/loader";

// interface MeetingTableProps {
//   data:
//     | InfiniteData<{
//         data: Meeting[];
//         totalCount: number;
//         page: number;
//         limit: number;
//       }>
//     | undefined;
//   isLoading: boolean;

//   hasNextPage: boolean | undefined;
//   fetchNextPage: () => Promise<any>;
// }

// const MeetingTable: React.FC<MeetingTableProps> = ({
//   data,
//   isLoading,

//   hasNextPage,
//   fetchNextPage,
// }) => {
//   const { ref, inView } = useInView();

//   useEffect(() => {
//     if (inView && hasNextPage) {
//       fetchNextPage();
//     }
//   }, [inView, fetchNextPage, hasNextPage]);

//   const meetings = Array.isArray(data?.pages?.[0]) ? data.pages[0] : [];

//   const formatMeetingData = (meeting: Meeting): MeetingTableData => {
//     const meetingDate = new Date(meeting.meetingDate);
//     const formattedData: MeetingTableData = {
//       projectName: meeting.projectId.name || "N/A",
//       time: isNaN(meetingDate.getTime())
//         ? "N/A"
//         : format(meetingDate, "hh:mm a"),
//       date: isNaN(meetingDate.getTime())
//         ? "N/A"
//         : format(meetingDate, "dd MMM yyyy"),
//     };
//     return formattedData;
//   };

//   if (isLoading && !meetings.length) {
//     return (
//       <Table>
//         <TableHeader>
//           <TableRow>
//             <TableHead className="w-[50%]">Project Name</TableHead>
//             <TableHead className="w-[50%]">Time</TableHead>
//             <TableHead className="w-[50%]">Date</TableHead>
//           </TableRow>
//         </TableHeader>
//         <TableBody>
//           <TableRow>
//             <TableCell colSpan={3} className="text-center">
//               <Loader size={5} />
//             </TableCell>
//           </TableRow>
//         </TableBody>
//       </Table>
//     );
//   }

//   if (!meetings.length) {
//     return (
//       <Table>
//         <TableHeader>
//           <TableRow>
//             <TableHead className="w-[50%]">Project Name</TableHead>
//             <TableHead className="w-[50%]">Time</TableHead>
//             <TableHead className="w-[50%]">Date</TableHead>
//           </TableRow>
//         </TableHeader>
//         <TableBody>
//           <TableRow>
//             <TableCell colSpan={3} className="text-center">
//               No meetings
//             </TableCell>
//           </TableRow>
//         </TableBody>
//       </Table>
//     );
//   }

//   return (
//     <Table>
//       <TableHeader>
//         <TableRow>
//           <TableHead className="w-[50%]">Project Name</TableHead>
//           <TableHead className="w-[25%]">Time</TableHead>
//           <TableHead className="w-[25%]">Date</TableHead>
//         </TableRow>
//       </TableHeader>
//       <TableBody>
//         {Array.isArray(meetings) &&
//           meetings.map((meeting: Meeting, index: number) => {
//             const formattedData = formatMeetingData(meeting);
//             return (
//               <TableRow
//                 key={meeting?._id || index}
//                 className="hover:bg-gray-50"
//               >
//                 <TableCell>{formattedData.projectName}</TableCell>
//                 <TableCell>{formattedData.time}</TableCell>
//                 <TableCell>{formattedData.date}</TableCell>
//               </TableRow>
//             );
//           })}
//         {hasNextPage && (
//           <TableRow ref={ref}>
//             <TableCell colSpan={3} className="text-center p-4">
//               {isLoading ? <Loader size={5} /> : "Load more..."}
//             </TableCell>
//           </TableRow>
//         )}
//       </TableBody>
//     </Table>
//   );
// };

// export default MeetingTable;

import React, { useEffect } from "react";
import { useInView } from "react-intersection-observer";
import { format } from "date-fns";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import { Meeting, MeetingTableData } from "@/types/Meetings";
import { InfiniteData } from "@tanstack/react-query";
import Loader from "../ui/loader";

interface MeetingTableProps {
  data:
    | InfiniteData<{
        data: Meeting[];
        totalCount: number;
        page: number;
        limit: number;
      }>
    | undefined;
  isLoading: boolean;
  hasNextPage: boolean | undefined;
  fetchNextPage: () => Promise<any>;
}

const MeetingTable: React.FC<MeetingTableProps> = ({
  data,
  isLoading,
  hasNextPage,
  fetchNextPage,
}) => {
  const { ref, inView } = useInView();

  useEffect(() => {
    if (inView && hasNextPage) {
      fetchNextPage();
    }
  }, [inView, fetchNextPage, hasNextPage]);

  const meetings = Array.isArray(data?.pages?.[0]) ? data.pages[0] : [];

  const formatMeetingData = (meeting: Meeting): MeetingTableData => {
    const meetingDate = new Date(meeting.meetingDate);
    const formattedData: MeetingTableData = {
      projectName: meeting.projectId.name || "N/A",
      time: isNaN(meetingDate.getTime())
        ? "N/A"
        : format(meetingDate, "hh:mm a"),
      date: isNaN(meetingDate.getTime())
        ? "N/A"
        : format(meetingDate, "dd MMM yyyy"),
    };
    return formattedData;
  };

  if (isLoading && !meetings.length) {
    return (
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-1/3">Project Name</TableHead>
            <TableHead className="w-1/3">Time</TableHead>
            <TableHead className="w-1/3">Date</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell colSpan={3} className="text-center">
              <Loader size={5} />
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>
    );
  }

  if (!meetings.length) {
    return (
      <div className="flex justify-center items-center min-h-[406px]">
        <div className="text-center text-neutrals-G600 text-sm font-normal">
          No meetings
        </div>
      </div>
    );
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead className="w-1/3">Project Name</TableHead>
          <TableHead className="w-1/3">Time</TableHead>
          <TableHead className="w-1/3">Date</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {Array.isArray(meetings) &&
          meetings.map((meeting: Meeting, index: number) => {
            const formattedData = formatMeetingData(meeting);
            return (
              <TableRow
                key={meeting?._id || index}
                className="hover:bg-gray-50"
              >
                <TableCell>{formattedData.projectName}</TableCell>
                <TableCell>{formattedData.time}</TableCell>
                <TableCell>{formattedData.date}</TableCell>
              </TableRow>
            );
          })}
        {hasNextPage && (
          <TableRow ref={ref}>
            <TableCell colSpan={3} className="text-center p-4">
              {isLoading ? <Loader size={5} /> : "Load more..."}
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
  );
};

export default MeetingTable;
