import React from "react";
import { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useInView } from "react-intersection-observer";
import { zodResolver } from "@hookform/resolvers/zod";
import { Plus } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Popover,
  PopoverClose,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import CalendarIcon from "@/components/icons/Calendar";
import { cn } from "@/lib/utils";
import { format, differenceInDays } from "date-fns";
import useCreateProject from "@/services/project/createProject";
import { toast } from "sonner";
import useContractors from "@/services/contractor/getContractors";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { LoadingSpinner } from "@/components/ui/loader";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import ProjectsIcon from "@/components/icons/Projects";
import ProfileIcon from "@/components/icons/Profile";
import DesignIcon from "@/components/icons/DesignIcon";
import Construction from "@/components/icons/Construction";
import { projectSchema } from "@/schema/project";
import Duration from "@/components/milestones/formFields/Duration";
import { useSheetFormState } from "@/hooks/useSheetFormState";

const CreateProject = () => {
  const [open, setOpen] = useState(false);
  const form = useForm<z.infer<typeof projectSchema>>({
    resolver: zodResolver(projectSchema),
    defaultValues: {
      name: "",
      projectType: "",
      numberOfFloors: undefined,
      expectedRevenue: undefined,
      requiredMargin: undefined,
      projectScope: "both",
      contractorOrg: "",
      clientName: "",
      clientWhatsAppNo: "",
      location: "",
    },
  });

  const { ref, inView } = useInView();

  const {
    contractorOptions,
    isLoading,
    isFetchingNextPage,
    hasNextPage,
    fetchNextPage,
  } = useContractors({
    limit: 10,
  });

  const createProjectMutation = useCreateProject();

  const popoverCloseRef = useRef<HTMLButtonElement>(null);

  const startDate = form.watch("startDate");
  const endDate = form.watch("endDate");

  useEffect(() => {
    if (startDate && endDate) {
      const days = differenceInDays(endDate, startDate);
      if (days > 0) {
        form.setValue("duration", days);
      }
    }
  }, [startDate, endDate, form]);

  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, fetchNextPage, hasNextPage, isFetchingNextPage]);

  const onSubmit = async (values: z.infer<typeof projectSchema>) => {
    try {
      // Create payload with only non-empty values
      const payload: any = {
        name: values.name, // Always include name as it's required
      };

      // Only add fields that have actual values
      if (values.startDate) {
        payload.startDate = format(values.startDate, "yyyy-MM-dd");
      }
      if (values.endDate) {
        payload.endDate = format(values.endDate, "yyyy-MM-dd");
      }
      if (values.designStartDate) {
        payload.designStartDate = format(values.designStartDate, "yyyy-MM-dd");
      }
      if (values.designEndDate) {
        payload.designEndDate = format(values.designEndDate, "yyyy-MM-dd");
      }
      if (values.contractorStartDate) {
        payload.contractorStartDate = format(
          values.contractorStartDate,
          "yyyy-MM-dd",
        );
      }
      if (values.contractorEndDate) {
        payload.contractorEndDate = format(
          values.contractorEndDate,
          "yyyy-MM-dd",
        );
      }
      if (values.projectType && values.projectType.trim()) {
        payload.projectType = values.projectType;
      }
      if (
        values.numberOfFloors !== undefined &&
        values.numberOfFloors !== null
      ) {
        payload.numberOfFloors = values.numberOfFloors;
      }
      if (
        values.expectedRevenue !== undefined &&
        values.expectedRevenue !== null
      ) {
        payload.expectedRevenue = values.expectedRevenue;
      }
      if (
        values.requiredMargin !== undefined &&
        values.requiredMargin !== null
      ) {
        payload.requiredMargin = values.requiredMargin;
      }
      if (values.projectScope) {
        payload.projectScope = values.projectScope;
      }
      if (values.contractorOrg && values.contractorOrg.trim()) {
        payload.contractorOrg = values.contractorOrg;
      }
      if (values.clientName && values.clientName.trim()) {
        payload.clientName = values.clientName;
      }
      if (values.clientWhatsAppNo && values.clientWhatsAppNo.trim()) {
        payload.clientWhatsAppNo = `+91${values.clientWhatsAppNo}`;
      }
      if (values.location && values.location.trim()) {
        payload.location = values.location;
      }
      if (values.duration !== undefined && values.duration !== null) {
        payload.duration = values.duration;
      }
      if (
        values.designDuration !== undefined &&
        values.designDuration !== null
      ) {
        payload.designDuration = values.designDuration;
      }
      if (
        values.contractorDuration !== undefined &&
        values.contractorDuration !== null
      ) {
        payload.contractorDuration = values.contractorDuration;
      }

      await createProjectMutation.mutateAsync(payload);
      toast.success("Project created successfully!");
      form.reset();
      setOpen(false);
    } catch (error: any) {
      // console.log(error?.response?.data?.message, "create error");
      console.error("Error creating project:", error);
      toast.error(
        error?.response?.data?.message ||
          "Failed to create project, Please try again.",
      );
    }
  };

  const { handleOpenChange, handleExplicitClose } = useSheetFormState({
    form,
    setOpen,
    preserveOnImplicitClose: true, // Preserve form data when clicking outside
  });

  const projectScope = form.watch("projectScope");

  console.log(projectScope, "project scope");

  const projectTypes = [
    "Residential",
    "Commercial",
    "Apartment",
    "Hospitality",
    "PEB",
    "Villa Project",
    "Wellness",
    "Institutional",
    "Assembly",
    "Industrial",
  ];

  return (
    <Sheet open={open} onOpenChange={handleOpenChange}>
      <SheetTrigger asChild>
        <Button className="gap-2 pl-4 pr-3">
          Create Project
          <Plus className="size-[22px] stroke-[2.5px]" />
        </Button>
      </SheetTrigger>
      <SheetContent
        className="max-w-[507px]"
        onExplicitClose={handleExplicitClose}
      >
        <SheetHeader>
          <SheetTitle>Create a new project</SheetTitle>
        </SheetHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col gap-y-6"
          >
            <div className="space-y-10">
              <div className="space-y-4">
                <div className="flex gap-1 items-center text-neutrals-G600 font-bold text-xs">
                  <ProjectsIcon className="size-3.5 text-neutrals-G800" />
                  BASIC DETAILS
                </div>
                <div className="space-y-4 p-4 border  rounded-[8px] border-neutrals-G40">
                  <FormField
                    control={form.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Project name</FormLabel>
                        <FormControl>
                          <Input placeholder="Name" {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="flex gap-x-2">
                    <FormField
                      control={form.control}
                      name="startDate"
                      render={({ field }) => (
                        <FormItem className="w-1/2">
                          <FormLabel>Start Date</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="input"
                                  className={cn(
                                    "text-left",
                                    !field.value && "text-neutrals-G100",
                                  )}
                                >
                                  {field.value ? (
                                    format(field.value, "dd/MM/yyyy")
                                  ) : (
                                    <span>Choose date</span>
                                  )}
                                  <CalendarIcon className="ml-auto" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent
                              className="w-auto p-0"
                              align="start"
                            >
                              <PopoverClose
                                ref={popoverCloseRef}
                                className="hidden"
                              />
                              <Calendar
                                selected={field.value}
                                onSelect={(value) => {
                                  console.log(value);
                                  field.onChange(value);
                                  popoverCloseRef.current?.click();
                                }}
                                mode="single"
                                disabled={() => false}
                                endMonth={
                                  new Date(
                                    Date.now() + 1000 * 60 * 60 * 24 * 365 * 10,
                                  )
                                }
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="endDate"
                      render={({ field }) => (
                        <FormItem className="w-1/2">
                          <FormLabel>End Date</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="input"
                                  className={cn(
                                    "text-left",
                                    !field.value && "text-neutrals-G100",
                                  )}
                                >
                                  {field.value ? (
                                    format(field.value, "dd/MM/yyyy")
                                  ) : (
                                    <span>Choose date</span>
                                  )}
                                  <CalendarIcon className="ml-auto" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent
                              className="w-auto p-0"
                              align="start"
                            >
                              <PopoverClose
                                ref={popoverCloseRef}
                                className="hidden"
                              />
                              <Calendar
                                selected={field.value}
                                onSelect={(value) => {
                                  field.onChange(value);
                                  popoverCloseRef.current?.click();
                                }}
                                mode="single"
                                disabled={(date) =>
                                  startDate
                                    ? date <
                                      new Date(startDate.getTime() + 86400000)
                                    : false
                                }
                                endMonth={
                                  new Date(
                                    Date.now() + 1000 * 60 * 60 * 24 * 365 * 10,
                                  )
                                }
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <Duration
                    value={form.watch("duration") || 0}
                    onChange={(val) => {
                      form.setValue("duration", val);
                    }}
                    startDate={form.watch("startDate")}
                    endDate={form.watch("endDate")}
                  />
                  <div className="flex gap-x-2">
                    <FormField
                      control={form.control}
                      name="projectType"
                      render={({ field }) => (
                        <FormItem className="w-1/2">
                          <FormLabel>Type of project</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value || ""}
                          >
                            <FormControl>
                              <SelectTrigger loading={isLoading}>
                                <SelectValue placeholder="Choose" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent
                              hideScrollDownButton
                              position="popper"
                              className="max-h-52 max-w-[--radix-select-trigger-width]"
                            >
                              {projectTypes.map((type) => (
                                <SelectItem key={type} value={type}>
                                  {type}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>

                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="numberOfFloors"
                      render={({ field }) => (
                        <FormItem className="w-1/2">
                          <FormLabel>No of floors</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <div className="flex gap-x-2">
                    <FormField
                      control={form.control}
                      name="expectedRevenue"
                      render={({ field }) => (
                        <FormItem className="basis-3/4">
                          <FormLabel>Expected revenue</FormLabel>
                          <FormControl>
                            <Input type="number" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="requiredMargin"
                      render={({ field }) => (
                        <FormItem className="w-1/2">
                          <FormLabel>Margin Required</FormLabel>
                          <FormControl>
                            <Input type="number" min={0} max={100} {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                  <FormField
                    control={form.control}
                    name="projectScope"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Project Scope</FormLabel>
                        <FormControl>
                          <RadioGroup
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            className="grid grid-cols-3"
                          >
                            <FormItem className="flex items-center space-x-2 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="design" />
                              </FormControl>
                              <FormLabel>Design</FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-2 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="construction" />
                              </FormControl>
                              <FormLabel>Construction</FormLabel>
                            </FormItem>
                            <FormItem className="flex items-center space-x-2 space-y-0">
                              <FormControl>
                                <RadioGroupItem value="both" />
                              </FormControl>
                              <FormLabel>Both</FormLabel>
                            </FormItem>
                          </RadioGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {(projectScope === "design" || projectScope === "both") && (
                <div className="space-y-4">
                  <div className="flex gap-1 items-center text-neutrals-G600 text-xs font-bold">
                    <DesignIcon className="text-neutrals-G800 size-3.5" />
                    DESIGN DETAILS
                  </div>
                  <div className="space-y-4 p-4 border  rounded-[8px] border-neutrals-G40">
                    <div className="flex gap-x-2">
                      <FormField
                        control={form.control}
                        name="designStartDate"
                        render={({ field }) => (
                          <FormItem className="w-1/2">
                            <FormLabel>Start Date</FormLabel>
                            <Popover>
                              <PopoverTrigger asChild>
                                <FormControl>
                                  <Button
                                    variant="input"
                                    className={cn(
                                      "text-left",
                                      !field.value && "text-neutrals-G100",
                                    )}
                                  >
                                    {field.value ? (
                                      format(field.value, "dd/MM/yyyy")
                                    ) : (
                                      <span>Choose date</span>
                                    )}
                                    <CalendarIcon className="ml-auto" />
                                  </Button>
                                </FormControl>
                              </PopoverTrigger>
                              <PopoverContent
                                className="w-auto p-0"
                                align="start"
                              >
                                <PopoverClose
                                  ref={popoverCloseRef}
                                  className="hidden"
                                />
                                <Calendar
                                  selected={field.value}
                                  onSelect={(value) => {
                                    console.log(value);
                                    field.onChange(value);
                                    popoverCloseRef.current?.click();
                                  }}
                                  mode="single"
                                  disabled={() => false}
                                  endMonth={
                                    new Date(
                                      Date.now() +
                                        1000 * 60 * 60 * 24 * 365 * 10,
                                    )
                                  }
                                />
                              </PopoverContent>
                            </Popover>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="designEndDate"
                        render={({ field }) => (
                          <FormItem className="w-1/2">
                            <FormLabel>End Date</FormLabel>
                            <Popover>
                              <PopoverTrigger asChild>
                                <FormControl>
                                  <Button
                                    variant="input"
                                    className={cn(
                                      "text-left",
                                      !field.value && "text-neutrals-G100",
                                    )}
                                  >
                                    {field.value ? (
                                      format(field.value, "dd/MM/yyyy")
                                    ) : (
                                      <span>Choose date</span>
                                    )}
                                    <CalendarIcon className="ml-auto" />
                                  </Button>
                                </FormControl>
                              </PopoverTrigger>
                              <PopoverContent
                                className="w-auto p-0"
                                align="start"
                              >
                                <PopoverClose
                                  ref={popoverCloseRef}
                                  className="hidden"
                                />
                                <Calendar
                                  selected={field.value}
                                  onSelect={(value) => {
                                    field.onChange(value);
                                    popoverCloseRef.current?.click();
                                  }}
                                  mode="single"
                                  disabled={(date) =>
                                    startDate
                                      ? date <
                                        new Date(startDate.getTime() + 86400000)
                                      : false
                                  }
                                  endMonth={
                                    new Date(
                                      Date.now() +
                                        1000 * 60 * 60 * 24 * 365 * 10,
                                    )
                                  }
                                />
                              </PopoverContent>
                            </Popover>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <Duration
                      value={form.watch("designDuration") || 0}
                      onChange={(val) => {
                        form.setValue("designDuration", val);
                      }}
                      startDate={form.watch("designStartDate")}
                      endDate={form.watch("designEndDate")}
                    />
                  </div>
                </div>
              )}

              {(projectScope === "construction" || projectScope === "both") && (
                <div className="space-y-4">
                  <div className="flex gap-1 items-center text-neutrals-G600 text-xs font-bold">
                    <Construction className="text-neutrals-G800 size-3.5" />
                    CONSTRUCTION DETAILS
                    <span className="font-normal text-neutrals-G400">
                      (Optional)
                    </span>
                  </div>
                  <div className="space-y-4 p-4 border  rounded-[8px] border-neutrals-G40">
                    <FormField
                      control={form.control}
                      name="contractorOrg"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Contractor</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            value={field.value || ""}
                          >
                            <FormControl>
                              <SelectTrigger loading={isLoading}>
                                <SelectValue placeholder="Select Contractor" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent
                              hideScrollDownButton
                              position="popper"
                              className="max-h-52 max-w-[--radix-select-trigger-width]"
                            >
                              {contractorOptions.length > 0 ? (
                                <>
                                  {contractorOptions.map((contractor) => (
                                    <SelectItem
                                      key={contractor.value}
                                      value={contractor.value}
                                    >
                                      {contractor.label}
                                    </SelectItem>
                                  ))}
                                  <SelectItem
                                    ref={ref}
                                    value={"loading-spinner"}
                                    disabled
                                    className={cn(
                                      "data-[disabled]:opacity-100 justify-center ",
                                      isFetchingNextPage ? "mb-1" : "h-0",
                                    )}
                                  >
                                    {isFetchingNextPage && (
                                      <LoadingSpinner
                                        size={4}
                                        className="border-2 "
                                      />
                                    )}
                                  </SelectItem>
                                  <SelectItem
                                    value="info-message"
                                    disabled
                                    className="sticky px-4 text-xs bottom-0 flex border border-[#CBD5E1] rounded-b data-[disabled]:opacity-100 bg-[#F5F5F7]"
                                  >
                                    <div className="flex gap-x-2">
                                      <span className="flex justify-center items-center size-[18px] px-[7px] rounded-full border bg-[#CBD5E1] text-black text-center text-[0.625rem] font-medium">
                                        i
                                      </span>
                                      <p className="text-center">
                                        Select a contractor or create a
                                        contractor account for your in-house
                                        team to manage.
                                      </p>
                                    </div>
                                  </SelectItem>
                                </>
                              ) : (
                                <SelectItem value="no-contractors" disabled>
                                  No contractors found
                                </SelectItem>
                              )}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <div className="flex gap-x-2">
                      <FormField
                        control={form.control}
                        name="contractorStartDate"
                        render={({ field }) => (
                          <FormItem className="w-1/2">
                            <FormLabel>Start Date</FormLabel>
                            <Popover>
                              <PopoverTrigger asChild>
                                <FormControl>
                                  <Button
                                    variant="input"
                                    className={cn(
                                      "text-left",
                                      !field.value && "text-neutrals-G100",
                                    )}
                                  >
                                    {field.value ? (
                                      format(field.value, "dd/MM/yyyy")
                                    ) : (
                                      <span>Choose date</span>
                                    )}
                                    <CalendarIcon className="ml-auto" />
                                  </Button>
                                </FormControl>
                              </PopoverTrigger>
                              <PopoverContent
                                className="w-auto p-0"
                                align="start"
                              >
                                <PopoverClose
                                  ref={popoverCloseRef}
                                  className="hidden"
                                />
                                <Calendar
                                  selected={field.value}
                                  onSelect={(value) => {
                                    console.log(value);
                                    field.onChange(value);
                                    popoverCloseRef.current?.click();
                                  }}
                                  mode="single"
                                  disabled={() => false}
                                  endMonth={
                                    new Date(
                                      Date.now() +
                                        1000 * 60 * 60 * 24 * 365 * 10,
                                    )
                                  }
                                />
                              </PopoverContent>
                            </Popover>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="contractorEndDate"
                        render={({ field }) => (
                          <FormItem className="w-1/2">
                            <FormLabel>End Date</FormLabel>
                            <Popover>
                              <PopoverTrigger asChild>
                                <FormControl>
                                  <Button
                                    variant="input"
                                    className={cn(
                                      "text-left",
                                      !field.value && "text-neutrals-G100",
                                    )}
                                  >
                                    {field.value ? (
                                      format(field.value, "dd/MM/yyyy")
                                    ) : (
                                      <span>Choose date</span>
                                    )}
                                    <CalendarIcon className="ml-auto" />
                                  </Button>
                                </FormControl>
                              </PopoverTrigger>
                              <PopoverContent
                                className="w-auto p-0"
                                align="start"
                              >
                                <PopoverClose
                                  ref={popoverCloseRef}
                                  className="hidden"
                                />
                                <Calendar
                                  selected={field.value}
                                  onSelect={(value) => {
                                    field.onChange(value);
                                    popoverCloseRef.current?.click();
                                  }}
                                  mode="single"
                                  disabled={(date) =>
                                    startDate
                                      ? date <
                                        new Date(startDate.getTime() + 86400000)
                                      : false
                                  }
                                  endMonth={
                                    new Date(
                                      Date.now() +
                                        1000 * 60 * 60 * 24 * 365 * 10,
                                    )
                                  }
                                />
                              </PopoverContent>
                            </Popover>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    <Duration
                      value={form.watch("contractorDuration") || 0}
                      onChange={(val) => {
                        form.setValue("contractorDuration", val);
                      }}
                      startDate={form.watch("contractorStartDate")}
                      endDate={form.watch("contractorEndDate")}
                    />
                  </div>
                </div>
              )}

              <div className="space-y-4">
                <div className="flex gap-1 items-center text-neutrals-G600 text-xs font-bold">
                  <ProfileIcon className="text-neutrals-G800 size-3.5" />
                  CLIENT DETAILS
                </div>
                <div className="space-y-4 p-4 border  rounded-[8px] border-neutrals-G40">
                  <FormField
                    control={form.control}
                    name="clientName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Client name</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Name"
                            {...field}
                            className="mb-3"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="flex gap-x-2">
                    <FormField
                      control={form.control}
                      name="clientWhatsAppNo"
                      render={({ field }) => (
                        <FormItem className="base-1/2">
                          <FormLabel>Whatsapp number</FormLabel>
                          <FormControl>
                            <div className="relative">
                              <span className="absolute left-3 mt-[1px] top-1/2 transform -translate-y-1/2 text-[#49515b] text-base">
                                +91
                              </span>
                              <Input
                                type="number"
                                className="pl-12"
                                placeholder="Enter 10-digit number"
                                {...field}
                              />
                            </div>
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="location"
                      render={({ field }) => (
                        <FormItem className="base-1/2">
                          <FormLabel>Location</FormLabel>
                          <FormControl>
                            <Input placeholder="Location" {...field} />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>
              </div>
            </div>
            <Button
              className="ml-auto px-4 py-3"
              type="submit"
              loading={createProjectMutation.isPending}
            >
              Create Project
            </Button>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  );
};

export default CreateProject;
