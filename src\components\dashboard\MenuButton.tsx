import Link from "next/link";
import { usePathname } from "next/navigation";
import { createContext, useContext } from "react";
import { cn } from "@/lib/utils";
import { ComponentPropsWithoutRef } from "react";

type MenuBottonContextValue = {
  isActive: boolean;
};

const MenuButtonContext = createContext<MenuBottonContextValue>(
  {} as MenuBottonContextValue,
);

const useMenuButton = () => {
  const menuButtonContext = useContext(MenuButtonContext);

  if (!menuButtonContext) {
    throw new Error("useMenuButton should be used within <MenuButton>");
  }

  return menuButtonContext;
};

interface MenuButtonProps
  extends Omit<ComponentPropsWithoutRef<typeof Link>, "href"> {
  path: string;
}

const MenuButton = ({ path, children, ...props }: MenuButtonProps) => {
  const pathname = usePathname();
  const isActive = pathname.startsWith(path);

  return (
    <Link
      href={path}
      className={`w-full px-3 py-2 flex gap-2 rounded-[8px] ${
        isActive ? "bg-primary-blue-B40 border border-primary-blue-B50" : ""
      }`}
      {...props}
    >
      <MenuButtonContext.Provider value={{ isActive }}>
        {children}
      </MenuButtonContext.Provider>
    </Link>
  );
};

const IconWrapper = ({ children }: { children: React.ReactNode }) => {
  const { isActive } = useMenuButton();

  return (
    <div
      className={cn(
        "size-5 flex items-center justify-center shrink-0",
        isActive ? "text-primary-blue-B900" : "text-neutrals-G100",
      )}
    >
      {children}
    </div>
  );
};

MenuButton.IconWrapper = IconWrapper;

const Text = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  const { isActive } = useMenuButton();

  return (
    <div
      className={`text-sm whitespace-nowrap overflow-hidden ${
        isActive ? "text-primary-blue-B900 font-semibold" : "text-neutrals-G600"
      } ${className}`}
    >
      {children}
    </div>
  );
};

MenuButton.Text = Text;

export default MenuButton;
