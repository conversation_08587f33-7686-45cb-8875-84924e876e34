import { Status } from "@/types/Project";
import RoundBadge from "./roundBadge";
import { cn } from "@/lib/utils";

const getStatusColor = (status: Status) => {
  switch (status) {
    case "ongoing":
      return "text-ongoing";
    case "completed":
      return "text-completed";
    case "halted":
      return "text-halted";
    default:
      return "text-gray-700";
  }
};

const CompletionStatus = ({
  status,
  className,
}: {
  status: Status;
  className?: string;
}) => {
  const statusColor = getStatusColor(status);

  return (
    <div className={cn("flex items-center gap-1", className)}>
      <RoundBadge variant={status} />
      <div className={`text-sm font-medium ${statusColor}`}>{status}</div>
    </div>
  );
};

export default CompletionStatus;
