import { useQuery } from "@tanstack/react-query";

import api from "@/lib/api-client";
import { WorkType } from "@/types/Worktype";

type GetWorktype = {
  projectId: string;
  milestoneId: string;
};

const getWorktype = async ({
  projectId,
  milestoneId,
}: GetWorktype): Promise<WorkType[]> => {
  const response = await api.get(`/worktypes/${projectId}/${milestoneId}`);
  return response.data;
};

type UseGetWorktype = GetWorktype;

const useGetWorktype = (args: UseGetWorktype) => {
  return useQuery({
    queryKey: ["worktypes", args.projectId, args.milestoneId],
    queryFn: async () => await getWorktype(args),
  });
};

export default useGetWorktype;
