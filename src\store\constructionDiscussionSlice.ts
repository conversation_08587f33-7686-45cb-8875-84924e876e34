import { StateCreator } from "zustand";

import { CreatedMessage } from "@/types/Socket";

export interface ConstructionDiscussionSlice {
  constructionDiscussionMessages: { [id: string]: CreatedMessage[] };
  getConstructionDiscussionMessagesByChatId: (
    chatId: string,
  ) => CreatedMessage[];
  updateConstructionDiscussionMessages: (newMessages: {
    [id: string]: CreatedMessage[];
  }) => void;
  replaceConstructionDiscussionMessagesWithChatId: (
    chatId: string,
    newMessages: CreatedMessage[],
  ) => void;
}

export const constructionDiscussionSlice: StateCreator<
  ConstructionDiscussionSlice
> = (set, get) => ({
  constructionDiscussionMessages: {},
  getConstructionDiscussionMessagesByChatId: (chatId) => {
    return get().constructionDiscussionMessages[chatId];
  },
  updateConstructionDiscussionMessages: (newMessages) => {
    set({ constructionDiscussionMessages: newMessages });
  },

  replaceConstructionDiscussionMessagesWithChatId: (chatId, newMessages) => {
    set({
      constructionDiscussionMessages: {
        ...get().constructionDiscussionMessages,
        [chatId]: newMessages,
      },
    });
  },
});
