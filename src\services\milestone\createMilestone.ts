import { toast } from "sonner";
import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";

// NOte: Unused api

type CreateProject = {
  projectId: string;
  milestoneTemplateId?: string;
  milestoneName?: string;
  isCustom?: boolean;
  startDate?: string;
  duration?: number;
  paymentDate?: string;
};

const createMilestone = async ({
  projectId,
  ...body
}: CreateProject): Promise<{ _id: string }> => {
  const response = await api({
    url: `/milestones/${projectId}`,
    method: "POST",
    data: body,
  });
  return response.data;
};

const useCreateMilestone = (
  onSuccess?: (data: { milestoneId: string }) => void,
) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createMilestone,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["milestones"] });
      // NOTE: Here the returned _id is the milestoneId of the milestone created.
      if (onSuccess) onSuccess({ milestoneId: data._id });
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to create milestone",
      );
    },
  });
};

export default useCreateMilestone;
