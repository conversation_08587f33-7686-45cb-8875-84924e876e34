// import api from "@/lib/api-client";
// import { Organisation } from "@/types/Organisation";
// import { User } from "@/types/User";
// import { useQuery } from "@tanstack/react-query";

// const getUser = async (): Promise<{
//   data: {
//     user: User;
//     organisation: Organisation;
//   };
// }> => {
//   const response = await api.get("/auth/me");
//   return response.data;
// };

// const useGetUser = () => {
//   return useQuery({
//     queryKey: ["user"],
//     queryFn: getUser,
//     refetchOnWindowFocus: true,
//     gcTime: 0,
//   });
// };

// export default useGetUser;

//this below code is updtated because the response from auth/me is not in the correct format

import api from "@/lib/api-client";
import { User } from "@/types/User";
import { Organisation } from "@/types/Organisation";
import { useQuery, UseQueryOptions } from "@tanstack/react-query";

type UserData = {
  user: User;
  organisation: Organisation;
};

const getUser = async (): Promise<UserData> => {
  const response = await api.get("/auth/me");
  return response.data;
};

const useGetUser = (options?: Partial<UseQueryOptions<UserData>>) => {
  return useQuery({
    queryKey: ["user"],
    queryFn: getUser,
    staleTime: 0,
    ...options,
    // gcTime: 0,
  });
};

export default useGetUser;
