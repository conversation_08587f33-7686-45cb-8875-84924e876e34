import { toast } from "sonner";

import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";

const markAllNotificationAsRead = async () => {
  const response = await api({
    url: "/notifications/mark-all-read",
    method: "PUT",
  });
  return response.data;
};

const useMarkAllNotificationAsRead = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: markAllNotificationAsRead,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["notification-count"] });
      queryClient.invalidateQueries({ queryKey: ["notifications"] });
      if (onSuccess) onSuccess();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message ||
          "Failed to mark all notification as read",
      );
    },
  });
};

export default useMarkAllNotificationAsRead;
