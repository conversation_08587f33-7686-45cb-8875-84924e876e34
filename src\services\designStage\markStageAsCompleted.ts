import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import api from "@/lib/api-client";

type MarkStageAsCompleted = {
  stageId: string;
};

const markStageAsCompleted = async ({ stageId }: MarkStageAsCompleted) => {
  const response = await api({
    url: `/design-stage/mark-as-completed/${stageId}`,
    method: "PUT",
  });

  return response.data;
};

const useMarkStageAsCompleted = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: markStageAsCompleted,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["design-stage"] });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message ||
          "Failed to mark design stage as completed",
      );
    },
  });
};

export default useMarkStageAsCompleted;
