import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";

import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

import { Button } from "@/components/ui/button";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  useGenerateS3Url,
  useUploadToS3,
  useAddSiteDocument,
} from "@/services/project/sitedocs-hooks";
import useGetMilestones from "@/services/milestone/getMilestones";
import { toast } from "sonner";

import { Check, ChevronDown, Plus, X } from "lucide-react";
// import { Badge } from "@/components/ui/badge";

import CustomFileInput from "@/components/ui/fileUpload";
import { cn } from "@/lib/utils";

const DEFAULT_CATEGORIES = ["3D Model", "Rough plan", "Estimate", "Sample"];

const formSchema = z.object({
  category: z.string().min(1, ""),
  file: z.instanceof(File, { message: "" }),
  milestoneId: z.string().min(1, ""),
});

type FormData = z.infer<typeof formSchema>;

type UploadDocumentDialogProps = {
  isOpen: boolean;
  onClose: () => void;
  profileId: string;
};

const UploadDocumentDialog: React.FC<UploadDocumentDialogProps> = ({
  isOpen,
  onClose,
  profileId,
}) => {
  const [isFileReady, setIsFileReady] = useState(false);

  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
  });

  const { data: milestones } = useGetMilestones({ projectId: profileId });
  const generateS3Url = useGenerateS3Url();
  const uploadToS3 = useUploadToS3();
  const addSiteDocument = useAddSiteDocument(profileId);

  const handleClose = () => {
    form.reset();
    setIsFileReady(false);
    onClose();
  };

  const handleOpenChange = (newOpen: boolean) => {
    setOpen(newOpen);
    if (newOpen) {
      setSearchQuery("");
    }
  };

  const onSubmit = async (data: FormData) => {
    if (!isFileReady) {
      toast.error("Please upload a file first");
      return;
    }

    try {
      const signedUrl = await generateS3Url.mutateAsync({
        fileName: data.file.name,
        projectId: profileId,
      });

      await uploadToS3.mutateAsync({
        signedUrl,
        file: data.file,
      });

      const url = new URL(signedUrl);
      const storagePath = url.pathname;

      await addSiteDocument.mutateAsync({
        category: data.category,
        fileName: data.file.name,
        storagePath,
        milestoneId: data.milestoneId,
      });

      onClose();
      form.reset();
      setIsFileReady(false);
      toast.success("Document uploaded successfully");
    } catch (error: any) {
      console.error("Error uploading document:", error);
      toast.error("Failed to upload document. Please try again.");
    }
  };

  const handleFileChange = (file: File | null) => {
    if (file) {
      form.setValue("file", file);
      setIsFileReady(false);
    }
  };

  const handleFileUpload = () => {
    setIsFileReady(true);
    toast.success("File ready for upload");
  };

  const handleCreateCategory = () => {
    form.setValue("category", searchQuery);
    setOpen(false);
  };

  const filteredCategories = DEFAULT_CATEGORIES.filter((category) =>
    category.toLowerCase().includes(searchQuery.toLowerCase()),
  );

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="w-[510px] p-6">
          <DialogTitle className="mb-[30px] flex items-center justify-between">
            Upload site documents
            <DialogClose asChild>
              <Button type="button" variant="outline" onClick={handleClose}>
                <X className="h-6 w-6" />
              </Button>
            </DialogClose>
          </DialogTitle>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-Grey-Dark-Grey text-sm">
                      Select category
                    </FormLabel>
                    <Popover open={open} onOpenChange={handleOpenChange} modal>
                      <PopoverTrigger asChild>
                        <Button
                          variant="drop"
                          role="combobox"
                          aria-expanded={open}
                          className="w-full justify-between shadow-input"
                        >
                          {field.value || "Choose"}
                          <ChevronDown className="h-5 w-5 shrink-0 opacity-50" />{" "}
                        </Button>
                      </PopoverTrigger>
                      <PopoverContent className="w-[28.4rem] p-3">
                        <Command>
                          <CommandInput
                            placeholder="Search category..."
                            value={searchQuery}
                            onValueChange={setSearchQuery}
                          />
                          <CommandList className="scrollbar-hide overflow-y-auto">
                            {filteredCategories.length === 0 && (
                              <CommandEmpty>
                                <div className="flex flex-col items-center gap-y-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    className="flex items-center gap-x-2 text-primary"
                                    onClick={handleCreateCategory}
                                  >
                                    <Plus className="h-4 w-4" />
                                    Create &quot;{searchQuery}&quot;
                                  </Button>
                                </div>
                              </CommandEmpty>
                            )}
                            <CommandGroup>
                              {filteredCategories.map((category) => (
                                <CommandItem
                                  key={category}
                                  value={category}
                                  onSelect={(currentValue) => {
                                    field.onChange(currentValue);
                                    setOpen(false);
                                  }}
                                  className="flex items-center gap-x-2"
                                >
                                  {category}
                                  <Check
                                    className={cn(
                                      "ml-auto h-4 w-4",
                                      field.value === category
                                        ? "opacity-100"
                                        : "opacity-0",
                                    )}
                                  />
                                </CommandItem>
                              ))}
                            </CommandGroup>
                          </CommandList>
                        </Command>
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="file"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-Grey-Dark-Grey text-sm">
                      Upload files
                    </FormLabel>
                    <FormControl>
                      <CustomFileInput
                        onFileChange={handleFileChange}
                        onFileUpload={handleFileUpload}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="milestoneId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-Grey-Dark-Grey text-sm">
                      Applicable to milestones till
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="shadow-input">
                          <SelectValue placeholder="Select milestone" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {milestones?.map((milestone) => (
                          <SelectItem key={milestone._id} value={milestone._id}>
                            {milestone.isCustom
                              ? milestone.milestoneName
                              : milestone.milestoneTemplateId?.name}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </FormItem>
                )}
              />

              <div className="flex justify-end">
                <Button
                  type="submit"
                  className="px-4 py-3"
                  loading={generateS3Url.isPending || uploadToS3.isPending}
                  disabled={
                    !isFileReady ||
                    generateS3Url.isPending ||
                    uploadToS3.isPending
                  }
                >
                  Save
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default UploadDocumentDialog;
