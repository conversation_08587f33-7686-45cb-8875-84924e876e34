import * as React from "react";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";

const badgeVariants = cva(
  "inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",
  {
    variants: {
      variant: {
        default: "border-transparent bg-green-bg text-green-badge ",
        secondary: "border-transparent bg-[#DA9500]/10 text-[#DA9500]",
        destructive: "border-transparent bg-red-bg text-red-badge ",
        outline: "text-foreground",
        info: "bg-blue-3 text-ID-text font-medium",
      },
      size: {
        default: "h-[29px]",
        sm: "h-5 p-1 text-[10px] font-medium rounded",
        xs: "h-6 py-1 rounded",
      },
    },

    defaultVariants: {
      variant: "default",
      size: "default",
    },
  },
);

export interface BadgeProps
  extends React.HTMLAttributes<HTMLDivElement>,
    VariantProps<typeof badgeVariants> {}

function Badge({ className, variant, size, ...props }: BadgeProps) {
  return (
    <div
      className={cn(badgeVariants({ variant, size }), className)}
      {...props}
    />
  );
}

export { Badge, badgeVariants };
