import { z } from "zod";
import { checkIsStartDateLowerThanOrEqualsToEndDate } from "@/lib/utils";

export const workTypeSchema = z
  .object({
    worktypeTemplateId: z.string().optional(),
    worktypeName: z.string().optional(),
    isCustom: z.boolean().default(false),
    startDate: z.date().optional(),
    endDate: z.date().optional(),
    duration: z.number().optional(),
    totalQty: z.string().optional(),
    unit: z.string().optional(),
    optionalQty: z.object({
      totalQty: z.string().optional(),
      unit: z.string().optional(),
    }),
  })
  .refine(
    (schema) => {
      if (schema.isCustom) {
        return !!schema.worktypeName;
      }
      return !!schema.worktypeTemplateId;
    },
    {
      message: "Either worktype template or custom name is required",
      path: ["workType"],
    },
  )
  .refine(
    (data) => {
      if (!data.startDate || !data.endDate) return true;
      return checkIsStartDateLowerThanOrEqualsToEndDate(
        new Date(data.startDate),
        new Date(data.endDate),
      );
    },
    {
      message: "End date cannot be lower than start date",
      path: ["endDate"],
    },
  )
  .refine(
    (data) => {
      if (data.startDate && !data.endDate) return false;
      return true;
    },
    {
      message: "Required",
      path: ["endDate"],
    },
  )
  .refine(
    (data) => {
      if (!data.startDate && data.endDate) return false;
      return true;
    },
    {
      message: "Required",
      path: ["startDate"],
    },
  );
