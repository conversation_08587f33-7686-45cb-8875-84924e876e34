import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import api from "@/lib/api-client";

type UploadDocumentToDesignStage = {
  stageId: string;
  s3Link: string;
  isReplaced: boolean;
  filename: string;
};

const uploadDocumentToDesignStage = async (
  body: UploadDocumentToDesignStage,
) => {
  const response = await api({
    url: "/design-stage/upload-document",
    method: "PUT",
    data: body,
  });

  return response.data;
};

const useUploadDocumentToDesignStage = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: uploadDocumentToDesignStage,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["design-stage"] });
      queryClient.invalidateQueries({
        queryKey: ["design-stage-document-history"],
      });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message ||
          "Failed to upload document to design stage",
      );
    },
  });
};

export default useUploadDocumentToDesignStage;
