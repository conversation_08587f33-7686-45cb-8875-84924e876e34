import * as React from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  ResponsiveContainer,
  XAxis,
  YAxis,
  Tooltip,
  CartesianGrid,
  Cell,
} from "recharts";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
  ChartLegend,
  ChartLegendContent,
  ChartStyle,
} from "@/components/ui/chart";

// Define the data structure
interface ChartData {
  stage: string;
  budgeted: number;
  actual: number;
}

interface BudgetHoursChartProps {
  data: ChartData[];
}

const chartConfig = {
  budgeted: {
    color: "#8FBAF5",
    label: "Budgeted",
  },
  actual: {
    color: "#9ED4A1",
    label: "Actual",
  },
};

export const BudgetHoursChart = ({ data }: BudgetHoursChartProps) => {
  // Calculate exceeded stages
  const exceededStages = data?.filter(
    (item) => item?.actual > item?.budgeted,
  ).length;

  return (
    <div className="w-full border rounded-xl bg-card text-card-foreground px-6 py-4">
      <div className="">
        <h3 className="text-lg font-semibold mb-2">
          Budgeted hours vs hours spent
        </h3>
        <div className="text-sm">
          <span className="text-[red-badge] font-bold">{exceededStages}</span>
          <span className="text-[neutrals-G300]">
            {exceededStages === 1 ? " Stage" : " Stages"} exceeded the budgeted
            hours
          </span>
        </div>
      </div>
      <div className=" overflow-x-auto">
        <ChartContainer
          config={chartConfig}
          className="w-full space-y-4 h-[229px] mt-[32px] overflow-y-hidden"
        >
          <BarChart
            data={data}
            margin={{ top: 5, right: 20, left: 20, bottom: 5 }}
            barGap={10}
            barCategoryGap={20}
          >
            <CartesianGrid
              horizontal={true}
              vertical={false}
              stroke="#F6F6F6"
            />
            <XAxis
              dataKey="stage"
              axisLine={false}
              tickLine={false}
              tick={{ fill: "neutrals-G300", fontSize: 12 }}
            />
            {/* <Tooltip
              contentStyle={{
                background: "white",
                border: "none",
                borderRadius: "8px",
                boxShadow: "0px 4px 12px rgba(0, 0, 0, 0.1)",
              }}
            /> */}

            <ChartTooltip content={<ChartTooltipContent />} />
            <Bar
              dataKey="budgeted"
              fill="var(--color-budgeted)"
              radius={[4, 4, 4, 4]}
              barSize={60}
            />
            <Bar
              dataKey="actual"
              radius={[4, 4, 4, 4]}
              barSize={60}
              fillOpacity={1}
              stroke="none"
              isAnimationActive={false}
              fill="var(--color-actual)"
            >
              {data.map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={entry?.actual > entry?.budgeted ? "#E37575" : "#9ED4A1"}
                />
              ))}
            </Bar>
          </BarChart>
        </ChartContainer>
      </div>
    </div>
  );
};

export default BudgetHoursChart;
