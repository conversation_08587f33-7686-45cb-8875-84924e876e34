import React, { useEffect, useCallback, useRef } from "react";
import { Contractor } from "@/types/Contractor";
import ContractorCard from "./ContractorCard";
import Loader from "../ui/loader";

interface ContractorGridProps {
  data: Contractor[];
  isLoading: boolean;
  isFetchingNextPage: boolean;
  hasNextPage: boolean;
  fetchNextPage: () => void;
}

const ContractorGrid: React.FC<ContractorGridProps> = ({
  data,
  isLoading,
  hasNextPage,
  isFetchingNextPage,
  fetchNextPage,
}) => {
  const observerTarget = useRef<HTMLDivElement>(null);

  const handleObserver = useCallback(
    (entries: IntersectionObserverEntry[]) => {
      const [entry] = entries;
      if (entry.isIntersecting && !isFetchingNextPage && hasNextPage) {
        fetchNextPage();
      }
    },
    [isFetchingNextPage, fetchNextPage, hasNextPage],
  );

  useEffect(() => {
    const element = observerTarget.current;
    if (!element) return;

    const observer = new IntersectionObserver(handleObserver, {
      root: null,
      rootMargin: "20px",
      threshold: 0.1,
    });

    observer.observe(element);

    return () => observer.disconnect();
  }, [handleObserver]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader size={10} />
      </div>
    );
  }

  return (
    <div>
      <div className="grid grid-cols-2 gap-3">
        {data?.map((contractor: Contractor) => (
          <ContractorCard key={contractor._id} contractor={contractor} />
        ))}
      </div>
      {isFetchingNextPage && (
        <div className="text-center py-4">
          <Loader size={5} />
        </div>
      )}
      {hasNextPage && <div ref={observerTarget} className="h-10" />}
    </div>
  );
};

export default ContractorGrid;
