import { useQuery } from "@tanstack/react-query";
import api from "@/lib/api-client";
import { ProjectAnalytics } from "@/types/Project";
import { format } from "date-fns";

const getProjectAnalytics = async (
  id: string,
  date?: Date,
): Promise<ProjectAnalytics> => {
  const params = new URLSearchParams();

  if (date) {
    params.append("dateto", format(date, "yyyy-MM-dd"));
  }

  const queryString = params.toString();
  const url = `/analytics/project-analytics-architect/${id}${queryString ? `?${queryString}` : ""}`;

  const response = await api.get(url);
  return response.data;
};

const useGetProjectAnalytics = (id: string, date?: Date) => {
  return useQuery({
    queryKey: ["project-analytics", id, date?.toISOString()],
    queryFn: () => getProjectAnalytics(id, date),
    enabled: !!id,
  });
};

export default useGetProjectAnalytics;
