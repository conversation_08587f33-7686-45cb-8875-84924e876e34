"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter, useSearchParams } from "next/navigation";
import { DateRange } from "react-day-picker";
import DailyUpdate from "@/components/dailyUpdates/DailyUpdatesTable";
import useGetProjectById from "@/services/project/getProject";
import DatePickerWithRange from "@/components/ui/dateRangePicker";
import { format, parse } from "date-fns";
import Loader from "@/components/ui/loader";
import ErrorText from "@/components/ui/errortext";
import DailyUpdatesSettings from "@/components/dailyUpdates/DailyUpdatesSettings";
import useGetProjectQuantityFieldOptional from "@/services/project/getProjectQuantityFieldOptional";

const DailyUpdatePage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const id = useParams().id as string;

  const [dateRange, setDateRange] = useState<DateRange | undefined>(() => {
    const fromParam = searchParams.get("dateFrom");
    const toParam = searchParams.get("dateTo");

    if (fromParam && toParam) {
      return {
        from: parse(fromParam, "yyyy-MM-dd", new Date()),
        to: parse(toParam, "yyyy-MM-dd", new Date()),
      };
    }
    return undefined;
  });

  const [page, setPage] = useState(() => {
    return parseInt(searchParams.get("page") || "1", 10);
  });

  const { data: projectData, isLoading, error } = useGetProjectById(id);

  const {
    data: projectQuantityFieldOptional,
    isSuccess: isSuccessProjectQuantityFieldOptional,
  } = useGetProjectQuantityFieldOptional({ projectId: id });

  useEffect(() => {
    const params = new URLSearchParams(searchParams.toString());

    if (dateRange?.from) {
      params.set("dateFrom", format(dateRange.from, "yyyy-MM-dd"));
    } else {
      params.delete("dateFrom");
    }

    if (dateRange?.to) {
      params.set("dateTo", format(dateRange.to, "yyyy-MM-dd"));
    } else {
      params.delete("dateTo");
    }

    params.set("page", page.toString());

    router.replace(`/projects/${id}/daily-updates?${params.toString()}`);
  }, [dateRange, page, id, router, searchParams]);

  const handleDateRangeChange = (range: DateRange | undefined) => {
    setDateRange(range);
    setPage(1);
  };

  if (isLoading) return <Loader />;
  if (error) return <ErrorText entity="daily updates" />;

  return (
    <div className="flex flex-col gap-y-6 ">
      <div className="flex justify-between items-center">
        <div className="space-y-1.5 text-neutrals-G800">
          <h4 className="font-semibold"> Daily Update</h4>
          <p className="text-sm">Track the daily activities in your project</p>
        </div>
        <div className="flex gap-x-4 items-center">
          {isSuccessProjectQuantityFieldOptional && (
            <DailyUpdatesSettings
              value={projectQuantityFieldOptional.isQuantityFieldOptional}
              projectId={id}
            />
          )}
          <DatePickerWithRange
            onChange={handleDateRangeChange}
            defaultValue={dateRange}
          />
        </div>
      </div>

      <div className="flex-grow overflow-auto scrollbar-hide">
        {projectData && (
          <DailyUpdate projectId={id as string} dateRange={dateRange} />
        )}
      </div>
    </div>
  );
};

export default DailyUpdatePage;
