export type User = {
  _id: string;
  email: string;
  name: string;
  whatsappNo: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
};

export type Milestone = {
  _id: string;
  milestoneTemplateId: string;
  milestoneName: string;
  isCustom: boolean;
  projectId: string;
  startDate: string;
  duration: number;
  createdAt: string;
  updatedAt: string;
  __v: number;
};

export type PreferredUnit = {
  _id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
};

export type MaterialQuantity = {
  material: string;
  quantity: number;
  _id: string;
};

export type WorktypeTemplate = {
  _id: string;
  worktype: string;
  milestoneTemplateId: string;
  defaultUnit: string;
  standardUnits: string[];
  unitConversions: {
    from: string;
    to: string;
    conversionFactor: number;
  }[];
  optionalUnits: string[];
  defaultQuantityPerDayMinimum: number;
  defaultQuantityPerDayMaximum: number;
  materialQuantityPerDefaultUnit: {
    material: string;
    quantity: number;
  }[];
  skilledLabourName: string;
};

export type Worktype = {
  _id: string;
  milestoneId: string;
  projectId: string;
  worktypeTemplateId: string;
  worktypeName: string;
  isCustom: boolean;
  worktypeTemplateProjectId: string;
  defaultUnit: string;
  totalDefaultQuantity: number;
  preferredUnit: string;
  file?: string[];
  totalPreferredQuantity: number;
  optionalUnit: string;
  totalQuantityOptionalUnit: number;
  startDate: string;
  duration: number;
  materialQuantity: MaterialQuantity[];
  createdAt: string;
  updatedAt: string;
  __v: number;
};

export type milestoneTemplateId = {
  _id: string;
  name: string;
};

export type DailyUpdate = {
  _id: string;
  updateType: string;
  projectId: string;
  userId: User;
  workerCount: number;
  helperCount: number;
  milestoneId: Milestone;
  worktypeId: Worktype;
  defaultQuantity: number;
  defaultUnit: string;
  preferedQuantity: number;
  preferedUnit: PreferredUnit;
  workPercentage: number;
  productivityDifference: number;
  dailyUpdateDate: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
  worktypeTemplateId: WorktypeTemplate;
  file?: string;
  milestoneTemplateId: milestoneTemplateId;
};

export type DailyUpdatesResponse = {
  statusCode: number;
  success: boolean;
  message: string;
  data: DailyUpdate[];
  totalCount: number;
  page: number;
  limit: number;
};
