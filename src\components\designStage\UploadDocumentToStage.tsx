import { ChangeEvent, useRef } from "react";
import { toast } from "sonner";
import { Upload } from "lucide-react";

import {
  useGenerateS3Url,
  useUploadToS3,
} from "@/services/project/sitedocs-hooks";
import { Button } from "../ui/button";
import useUploadDocumentToDesignStage from "@/services/designStage/uploadDocumentToDesignStage";

type UploadDocumentToStageProps = {
  projectId: string;
  stageId: string;
};

const UploadDocumentToStage = ({
  projectId,
  stageId,
}: UploadDocumentToStageProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { mutateAsync: generateS3Url } = useGenerateS3Url();
  const { mutateAsync: uploadToS3 } = useUploadToS3();
  const { mutateAsync: addDocument } = useUploadDocumentToDesignStage(() => {
    toast.success("Document uploaded successfully!");
  });

  const handleDocumentUpload = async (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0] || null;
    if (!file) {
      return;
    }

    try {
      const signedUrl = await generateS3Url({
        fileName: file.name,
        projectId,
      });

      await uploadToS3({
        signedUrl,
        file: file,
      });

      // const url = new URL(signedUrl);
      const s3Link = signedUrl.split("?")[0];

      await addDocument({
        stageId: stageId,
        s3Link,
        isReplaced: false,
        filename: file.name,
      });
    } catch (error: any) {
      console.error("Error uploading document:", error);
      toast.error("Failed to upload document. Please try again.");
    } finally {
      event.target.value = "";
    }
  };

  return (
    <>
      <input
        ref={fileInputRef}
        type="file"
        accept=".pdf,.obj"
        onChange={handleDocumentUpload}
        className="hidden"
      />
      <Button
        onClick={() => fileInputRef.current?.click()}
        variant="link"
        className="px-3.5 gap-x-1.5 font-medium hover:no-underline"
      >
        <Upload className="size-4" /> Upload Document
      </Button>{" "}
    </>
  );
};

export default UploadDocumentToStage;
