import { toast } from "sonner";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { z } from "zod";

import api from "@/lib/api-client";
import { teamMemberSchema } from "@/schema/teamMember";

const addTeamMember = async (body: z.infer<typeof teamMemberSchema>) => {
  const response = await api({
    url: "/architect-team/add-member",
    method: "POST",
    data: body,
  });
  return response.data;
};

const useAddTeamMember = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: addTeamMember,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["team-members"] });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to add team member",
      );
    },
  });
};

export default useAddTeamMember;
