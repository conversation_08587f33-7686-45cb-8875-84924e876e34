# 🚀 Pull Request Template

## 📝 Summary  
<!-- What did you build or fix? Be descriptive but concise! -->  
<!-- Example: Added unicorn-mode to boost app sparkle levels 🌈 -->

## 🔗 Related Issues / ClickUp Tasks  
Closes #[IssueNumber]  
ClickUp Task: [CU-xxxxx](https://app.clickup.com/t/xxxxx)  
<!-- Please link any relevant bug reports, tasks, or discussions -->

## ✅ Checklist  
Before merging, please make sure all the boxes are ticked (no cheating! 😄):

- [ ] Code is linted & formatted 🧹
- [ ] Unit tests added/updated 🧪
- [ ] Feature/bugfix works on `test` 🔍
- [ ] Peer-reviewed ✅
- [ ] Validated on `stage` 🧑‍🔬
- [ ] Marked as ready for `prod` 🚢 (if applicable)

## 👀 Notes for Testers  
<!-- Anything specific to test? Edge cases, test accounts, browser steps? -->  
<!-- Example: Try breaking the unicorn mode by triple-clicking the sparkle icon -->

## 📸 Screenshots / Demos (if UI-related)  
<!-- Drag in a screenshot, screen recording, or gif. Show off that pixel-perfect alignment! -->

## 🧠 Extra Context  
<!-- Anything else the reviewer should know? Wild hacks? API weirdness? Cookie-based authentication sorcery? -->

---

✨ **Thanks for the PR! You're one step closer to glory.**
