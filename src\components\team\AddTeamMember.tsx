import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Plus } from "lucide-react";
import { toast } from "sonner";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  SheetTrigger,
} from "@/components/ui/sheet";
import { teamMemberSchema } from "@/schema/teamMember";
import TeamMemberFormFields from "./TeamMemberFormFields";
import useAddTeamMember from "@/services/teamMember/addTeamMember";
import { useSheetFormState } from "@/hooks/useSheetFormState";

const AddTeamMember = () => {
  const [open, setOpen] = useState(false);
  const form = useForm<z.infer<typeof teamMemberSchema>>({
    resolver: zodResolver(teamMemberSchema),
    defaultValues: {
      name: "",
      email: "",
      role: "",
      salary: "",
      phone: "",
      isAdmin: false,
    },
  });

  const { mutate, isPending } = useAddTeamMember(() => {
    setOpen(false);
    toast.success("Team member added successfully!");
  });

  const { handleOpenChange, handleExplicitClose } = useSheetFormState({
    form,
    setOpen,
    preserveOnImplicitClose: true,
  });

  const onSubmit = async (values: z.infer<typeof teamMemberSchema>) => {
    mutate(values);
  };

  return (
    <Sheet open={open} onOpenChange={handleOpenChange}>
      <SheetTrigger asChild>
        <Button className="gap-x-2 pl-4 pr-3">
          Add team member
          <Plus className="size-[22px] stroke-[2.5px]" />
        </Button>
      </SheetTrigger>
      <SheetContent
        className="max-w-[507px] flex flex-col gap-0"
        onExplicitClose={handleExplicitClose}
      >
        <SheetHeader>
          <SheetTitle>Add Team Member</SheetTitle>
        </SheetHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col justify-between flex-1"
          >
            <div className="space-y-3">
              <TeamMemberFormFields form={form} />
            </div>
            <Button
              className="ml-auto px-4 py-3 sticky bottom-0"
              type="submit"
              loading={isPending}
            >
              Add team member
            </Button>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  );
};

export default AddTeamMember;
