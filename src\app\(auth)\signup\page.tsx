"use client";

import React, { useEffect, useState } from "react";
import { useCreateUserWithEmailAndPassword } from "react-firebase-hooks/auth";
import { auth } from "../../firebase";
import { FirebaseError } from "firebase/app";
import Image from "next/image";
import Cookies from "js-cookie";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Form,
  FormControl,
  FormField,
  FormMessage,
} from "@/components/ui/form";
import useSendUserDetails from "@/services/auth/sendUserDetails";
import { toast } from "sonner";
import Link from "next/link";
import { Eye, EyeOff } from "lucide-react";
import { useRouter } from "next/navigation";

const getSignupErrorMessage = (errorCode: string): string => {
  switch (errorCode) {
    case "auth/email-already-in-use":
      return "This email is already registered. Please use a different email or try logging in.";
    case "auth/invalid-email":
      return "Invalid email address. Please enter a valid email.";
    case "auth/operation-not-allowed":
      return "Sign up is currently disabled. Please contact support.";
    case "auth/weak-password":
      return "Password is too weak. Please choose a stronger password.";
    case "auth/network-request-failed":
      return "Network error. Please check your internet connection and try again.";
    case "auth/too-many-requests":
      return "Too many requests. Please try again later.";
    case "auth/internal-error":
      return "An internal error occurred. Please try again later.";
    default:
      return "An unexpected error occurred. Please try again later.";
  }
};

const formSchema = z.object({
  name: z
    .string()
    .min(1, { message: "" })
    .max(100, { message: "Name is too long" }),
  email: z
    .string({ required_error: "" })
    .email({ message: "Invalid email address" }),
  organisationName: z
    .string()
    .min(1, { message: "" })
    .max(100, { message: "Organization is too long" }),
  password: z
    .string({ required_error: "" })
    .min(6, { message: "Password must be at least 6 characters long" }),
  organisationType: z.literal("architect").default("architect"),
});

const SignupPage: React.FC = () => {
  const router = useRouter();
  const [
    createUserWithEmailAndPassword,
    ,
    isPendingCreateUserWithEmailAndPassword,
    error,
  ] = useCreateUserWithEmailAndPassword(auth);

  const handleSuccess = () => {
    toast.success("User created successfully");
    router.push("/subscription-plan"); // Redirect to subscription plan after signup
  };
  const { mutate: sendUserDetails, isPending: isPendingSendUserDetails } =
    useSendUserDetails(handleSuccess);

  const isPending =
    isPendingCreateUserWithEmailAndPassword || isPendingSendUserDetails;

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      organisationName: "",
      organisationType: "architect",
    },
  });

  const [showPassword, setShowPassword] = useState(false);

  useEffect(() => {
    if (error?.code) toast.error(getSignupErrorMessage(error.code));
    // No redirects here, just show the error message
  }, [error]);

  const onSubmit = async (data: z.infer<typeof formSchema>) => {
    const { email, password } = data;
    try {
      // Only proceed if signup was successful
      const signupSuccessful = await handleCreateUserWithEmailAndPassword({
        email,
        password,
      });

      if (signupSuccessful) {
        // Directly navigate to subscription page after creating user
        router.push("/subscription-plan");

        // Send user details in the background
        sendUserDetails({
          name: data.name,
          organisationName: data.organisationName,
          organisationType: data.organisationType,
        });
      }
      // If signup failed, we stay on the signup page
    } catch (err) {
      console.error("Error creating user:", err);
    }
  };

  const handleCreateUserWithEmailAndPassword = async ({
    email,
    password,
  }: {
    email: z.infer<typeof formSchema>["email"];
    password: z.infer<typeof formSchema>["password"];
  }) => {
    try {
      const userCredential = await createUserWithEmailAndPassword(
        email,
        password,
      );
      if (userCredential) {
        const idToken = await userCredential.user.getIdToken();

        Cookies.set("token", idToken);
        // Set a flag to indicate this is a new signup
        Cookies.set("newSignup", "true");

        // Immediately redirect to subscription page
        router.push("/subscription-plan");
        return true;
      }
      return false;
    } catch (err: unknown) {
      if (err instanceof FirebaseError) {
        console.error("Error creating user:", err.message);
        // Show error message but DO NOT redirect
        toast.error(getSignupErrorMessage(err.code));
        // Return false to indicate signup failed
        return false;
      } else {
        console.error("An unexpected error occurred:", err);
        toast.error("An unexpected error occurred. Please try again later.");
        // Return false to indicate signup failed
        return false;
      }
    }
  };

  return (
    <div className="w-[491px] p-10 bg-white rounded-3xl shadow-[0px_4px_24px_0px_rgba(0,0,0,0.12)] overflow-auto scrollbar-hide">
      <div className="mb-10 flex justify-center">
        <Image
          src="/projectsmatelogo.png"
          width={163}
          height={54}
          alt="Projectsmate Logo"
          className="w-[163px] h-[54px] object-contain"
        />
      </div>

      <div className="flex flex-col gap-6">
        <div className="text-center">
          <h1 className="text-neutrals-G900 text-xl font-semibold">
            Let&apos;s get started
          </h1>
          <p className="text-neutrals-G600 text-base">
            Fill in the details below to get started.
          </p>
        </div>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col gap-5"
          >
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <div className="space-y-2">
                  <label className="text-Grey-Dark-Grey text-sm">Name</label>
                  <FormControl>
                    <Input
                      {...field}
                      className="px-3 py-2.5 bg-neutrals-G30 rounded-[6px] shadow-input border border-border-gray text-name-title text-base font-medium"
                    />
                  </FormControl>
                  <FormMessage />
                </div>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <div className="space-y-2">
                  <label className="text-Grey-Dark-Grey text-sm">Email</label>
                  <FormControl>
                    <Input
                      {...field}
                      className="px-3 py-2.5 bg-neutrals-G30 rounded-[6px] shadow-input border border-border-gray text-name-title text-base font-medium"
                    />
                  </FormControl>
                  <FormMessage />
                </div>
              )}
            />

            <FormField
              control={form.control}
              name="organisationName"
              render={({ field }) => (
                <div className="space-y-2">
                  <label className="text-Grey-Dark-Grey text-sm">
                    Organisation name
                  </label>
                  <FormControl>
                    <Input
                      {...field}
                      className="px-3 py-2.5 bg-neutrals-G30 rounded-[6px] shadow-input border border-border-gray text-name-title text-base font-medium"
                    />
                  </FormControl>
                  <FormMessage />
                </div>
              )}
            />

            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <div className="space-y-2 mb-3">
                  <label className="text-Grey-Dark-Grey text-sm">
                    Password
                  </label>
                  <div className="relative">
                    <FormControl>
                      <Input
                        type={showPassword ? "text" : "password"}
                        {...field}
                        className="px-3 py-2.5 bg-neutrals-G30 rounded-[6px] shadow-input border border-border-gray text-name-title text-base font-medium [&::-ms-reveal]:hidden [&::-webkit-inner-spin-button]:appearance-none"
                      />
                    </FormControl>
                    {field.value && (
                      <Button
                        type="button"
                        variant="ghost"
                        size="icon"
                        className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? (
                          <EyeOff className="h-4 w-4 text-neutrals-G100" />
                        ) : (
                          <Eye className="h-4 w-4 text-neutrals-G100" />
                        )}
                      </Button>
                    )}
                  </div>
                  <FormMessage />
                </div>
              )}
            />
            <div className="mt-3">
              <Button
                type="submit"
                className="w-full h-[43px] px-4 py-3 bg-primary text-white rounded-[6px] hover:bg-primary/90 mb-3"
                loading={isPending}
              >
                {isPending ? "Creating account..." : "Create Account"}
              </Button>
            </div>

            <div className="flex justify-center gap-1 text-sm">
              <span className="text-neutrals-G800 font-medium">
                Already have an account?
              </span>
              <Link
                href="/login"
                className="text-primary font-medium hover:underline"
              >
                Login
              </Link>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
};

export default SignupPage;
