import { ReactNode } from "react";
import { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import RootLayoutClient from "./RootLayoutClient";
import "./globals.css";

const inter = Inter({
  weight: ["400", "500", "600", "700"],
  subsets: ["latin"],
  display: "swap",
  // variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Projectsmate",
};

export const viewport = {
  // width: 1024,
  width: "device-width",
  initialScale: 1.0,
  userScalable: "no",
};

export default function RootLayout({ children }: { children: ReactNode }) {
  return (
    <html lang="en">
      <body className={inter.className}>
        <RootLayoutClient>{children}</RootLayoutClient>
      </body>
    </html>
  );
}
