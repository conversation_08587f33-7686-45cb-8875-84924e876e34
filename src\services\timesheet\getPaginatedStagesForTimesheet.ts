import { useEffect, useMemo } from "react";
import { useInView } from "react-intersection-observer";

import useGetStagesForTimesheet, {
  UseGetStagesForTimesheet,
} from "./getStagesForTimesheet";

export const useGetPaginatedStagesForTimesheet = (
  props: UseGetStagesForTimesheet,
) => {
  const { ref, inView } = useInView();

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, ...rest } =
    useGetStagesForTimesheet(props);

  const projects = useMemo(
    () => (data ? data.pages.flatMap((page) => page.data) : []),
    [data],
  );

  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, fetchNextPage, hasNextPage, isFetchingNextPage]);

  return { data: projects, ...rest, isFetchingNextPage, ref };
};
