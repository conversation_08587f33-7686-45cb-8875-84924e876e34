import type { Config } from "tailwindcss";
import tailwindAnimate from "tailwindcss-animate";

const config: Config = {
  darkMode: ["class"],
  content: [
    "./src/pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/components/**/*.{js,ts,jsx,tsx,mdx}",
    "./src/app/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  theme: {
    extend: {
      colors: {
        gray1: "#7A7F87",
        gray2: "#94A3B8",
        gray3: "#B9B9B9",
        gray4: "#787878",
        "feather-gray": "#E7EEF7",
        "sidebar-gray": "#A3A5B2",
        gray: "#A3A5B2",
        "blue-2": "#BBD8FF",
        "blue-3": "#E8F5FF",
        "ID-bg": "#D3E6FF",
        "ID-text": "#002761",
        "border-gray": "#CBD5E1",
        black1: "#0C203B",
        "neutrals-G10": "#FBFBFB",
        "neutrals-G20": "#F6F6F6",
        "neutrals-G30": "#EDEDED",
        "neutrals-G40": "#E2E2E2",
        "neutrals-G50": "#C7C7C7",
        "neutrals-G100": "#868686",
        "neutrals-G200": "#787878",
        "neutrals-G300": "#6B6B6B",
        "neutrals-G400": "#5F5F5F",
        "neutrals-G600": "#474747",
        "neutrals-G800": "#292929",
        "neutrals-G900": "#1E1E1E",
        "primary-blue-B30": "#EEF5FE",
        "primary-blue-B40": "#E4EEFD",
        "primary-blue-B50": "#CBDFFB",
        "primary-blue-B70": "#B4D1F9",
        "primary-blue-B80": "#A8CAF7",
        "primary-blue-B90": "#9BC2F6",
        "primary-blue-B400": "#6BA5F2",
        "primary-blue-B900": "#2F80ED",
        "primary-blue-B100": "#8FBAF5",
        "Grey-Dark-Grey": "#49505a",
        "border-gray1": "#cfcfcf",
        ongoing: "#DA9500",
        completed: "#4CAF50",
        halted: "#F35422",
        line: "#F3F8FC",
        "name-title": "#49515B",
        "name-text": "#292E35",
        "cancel-btn": "#49515B",
        "table-border": "hsla(206,100%,96%,1)",
        "green-badge": "#4CAF50",
        "red-badge": "#CC0000",
        "green-bg": "#E6FFE7",
        "red-bg": "#FFECEC",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        chart: {
          "1": "hsl(var(--chart-1))",
          "2": "hsl(var(--chart-2))",
          "3": "hsl(var(--chart-3))",
          "4": "hsl(var(--chart-4))",
          "5": "hsl(var(--chart-5))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      boxShadow: {
        card: "0px 16px 40px 8px rgba(47,128,237,0.10)",
        md: "0px 4px 60px 0px rgba(0,73,172,0.08)",
        lg: "4px 16px 59.3px 0px rgba(0, 73, 172, 0.06)",
        input: "0px 4px 150px 0px rgba(0,0,0,0.08)",
      },
      keyframes: {
        "accordion-down": {
          from: {
            height: "0",
          },
          to: {
            height: "var(--radix-accordion-content-height)",
          },
        },
        "accordion-up": {
          from: {
            height: "var(--radix-accordion-content-height)",
          },
          to: {
            height: "0",
          },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [tailwindAnimate, require("tailwind-scrollbar-hide")],
};

export default config;
