"use client";

import * as React from "react";
import * as ProgressPrimitive from "@radix-ui/react-progress";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/lib/utils";

const progressCircleVariants = cva("", {
  variants: {
    variant: {
      default: "[--progress-color:primary]",
      warning: "[--progress-color:#DA9500]",
      success: "[--progress-color:#4CAF50]",
      danger: "[--progress-color:#C00]",
    },
  },
  defaultVariants: {
    variant: "default",
  },
});

interface CircleProgressProps
  extends React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>,
    VariantProps<typeof progressCircleVariants> {
  showValue?: boolean;
}

const CircleProgress = React.forwardRef<
  React.ElementRef<typeof ProgressPrimitive.Root>,
  CircleProgressProps
>(({ className, value, variant, showValue = true, ...props }, ref) => {
  const percentage = value || 0;
  const strokeWidth = 7;
  const radius = 34;
  const circumference = 2 * Math.PI * radius;
  const progressOffset = circumference - (percentage / 100) * circumference;

  return (
    <div className="relative w-[88px] h-[88px]">
      <svg className="w-full h-full" viewBox="0 0 88 88">
        <circle
          cx="44"
          cy="44"
          r={radius}
          fill="none"
          stroke="#C7C7C7"
          strokeWidth={strokeWidth}
        />
        <circle
          cx="44"
          cy="44"
          r={radius}
          fill="none"
          stroke="var(--progress-color)"
          strokeWidth={strokeWidth}
          strokeLinecap="round"
          strokeDasharray={`${circumference} ${circumference}`}
          strokeDashoffset={progressOffset}
          transform="-rotate(-90 44 44)"
          className={cn(
            progressCircleVariants({ variant }),
            "transition-[stroke-dashoffset] duration-300",
          )}
        />
      </svg>

      {showValue && (
        <div
          className={cn(
            "absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2",
            "text-lg font-semibold",
            variant === "danger"
              ? "text-[#C00]"
              : variant === "warning"
                ? "text-[#DA9500]"
                : variant === "success"
                  ? "text-[#4CAF50]"
                  : "text-primary",
          )}
        >
          {percentage}%
        </div>
      )}
    </div>
  );
});

CircleProgress.displayName = "CircleProgress";

export { CircleProgress };
