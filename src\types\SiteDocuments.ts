export type MilestoneTemplate = {
  _id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
};

export type Milestone = {
  _id: string;
  milestoneTemplateId: MilestoneTemplate;
  projectId: string;
  startDate: string;
  duration: number;
  createdAt: string;
  updatedAt: string;
  __v: number;
};

export type SiteDocument = {
  _id: string;
  category: string;
  fileName: string;
  storagePath: string;
  milestoneId?: {
    _id: string;
    milestoneTemplateId?: string;
    milestoneName?: string;
    isCustom?: boolean;
    projectId: string;
    startDate: string;
    duration: number;
    createdAt: string;
    updatedAt: string;
    __v: number;
  };
  milestoneTemplateId?: {
    _id: string;
    name: string;
    createdAt: string;
    updatedAt: string;
    __v: number;
  };
  revisionNumber: number;
  uploadDate: string;
};

export type SiteDocumentResponse = {
  statusCode: number;
  success: boolean;
  message: string;
  data: {
    category: string;
    files: SiteDocument[];
  }[];
};

export type SiteDocumentRequest = {
  category: string;
  fileName: string;
  storagePath: string;
  milestoneId?: string;
};
