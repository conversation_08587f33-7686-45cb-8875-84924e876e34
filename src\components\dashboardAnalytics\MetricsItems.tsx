// components/dashboardAnalytics/MetricItem.tsx
import { ReactNode } from "react";
import Image from "next/image";

const MetricItem = ({
  type,
  value,
  label,
}: {
  type: "ongoing" | "halted" | "completed";
  value: number;
  label: string;
}) => {
  const iconMap: Record<typeof type, ReactNode> = {
    ongoing: (
      <Image
        src="/ongoing.svg"
        alt="Ongoing"
        width={40}
        height={40}
        className="shrink-0"
      />
    ),
    halted: (
      <Image
        src="/halted.svg"
        alt="Halted"
        width={40}
        height={40}
        className="shrink-0"
      />
    ),
    completed: (
      <Image
        src="/completed.svg"
        alt="Completed"
        width={40}
        height={40}
        className="shrink-0"
      />
    ),
  };

  return (
    <div className="flex items-center gap-4 h-[41px]">
      {iconMap[type]}
      <div>
        <p className="text-[20px] font-semibold text-neutrals-G900">{value}</p>
        <p className="text-sm text-neutrals-G600    ">{label}</p>
      </div>
    </div>
  );
};

export default MetricItem;
