import api from "@/lib/api-client";
import { ProjectData } from "@/types/Project";
import { useQuery } from "@tanstack/react-query";

const getProjectById = async (id: string): Promise<ProjectData | null> => {
  try {
    const response = await api.get<ProjectData>(`/projects/${id}`);
    if (response.data) {
      return response.data;
    }
    return null;
  } catch (error) {
    console.error(`Error fetching project with ID ${id}:`, error);
    return null;
  }
};

const useGetProjectById = (id: string) => {
  return useQuery({
    queryKey: ["project", id],
    queryFn: () => getProjectById(id),
    enabled: !!id,
  });
};

export default useGetProjectById;
