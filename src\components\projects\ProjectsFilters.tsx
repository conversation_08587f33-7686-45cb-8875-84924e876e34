import React, { useState, useCallback } from "react";

import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import Search from "@/components/icons/Search";
import { Status } from "@/types/Project";
import RoundBadge from "@/components/ui/roundBadge";

type SelectValue = Status | "all" | "";

interface ProjectsFiltersProps {
  initialSearchTerm?: string;
  initialSelect?: SelectValue;
  onFiltersChange: (searchTerm: string, select: SelectValue) => void;
}

const ProjectsFilters: React.FC<ProjectsFiltersProps> = React.memo(
  ({ initialSearchTerm = "", initialSelect = "", onFiltersChange }) => {
    const [inputSearchTerm, setInputSearchTerm] = useState(initialSearchTerm);
    const [select, setSelect] = useState<SelectValue>(initialSelect);

    const handleSearch = useCallback(() => {
      onFiltersChange(inputSearchTerm, select);
    }, [inputSearchTerm, select, onFiltersChange]);

    const handleSearchChange = useCallback(
      (e: React.ChangeEvent<HTMLInputElement>) => {
        setInputSearchTerm(e.target.value);
      },
      [],
    );

    const handleSelectChange = useCallback(
      (value: SelectValue) => {
        setSelect(value);
        onFiltersChange(inputSearchTerm, value);
      },
      [inputSearchTerm, onFiltersChange],
    );

    return (
      <div className="flex items-center gap-x-[9px] mb-6">
        <form
          onSubmit={(e) => {
            e.preventDefault();
            handleSearch();
          }}
          className="flex items-center space-x-2"
        >
          <div className="flex bg-neutrals-G40 rounded-lg overflow-hidden">
            <Input
              type="search"
              placeholder="Search by name"
              className="w-1/4 min-w-[297px] h-[2.25rem] border-none bg-inherit placeholder:text-neutrals-G400 placeholder:text-sm placeholder:font-normal"
              value={inputSearchTerm}
              onChange={handleSearchChange}
            />
            <button className="pr-3 py-2">
              <Search />
            </button>
          </div>
        </form>
        <Select value={select} onValueChange={handleSelectChange}>
          <SelectTrigger className="w-auto min-w-[12.3rem] border-neutrals-G40 h-[2.25rem] rounded-[8px] shadow-none px-3 py-2 justify-between text-neutrals-G600 [&>svg]:text-neutrals-G600 [&>svg]:opacity-100">
            <span>Filter by</span>
            <div className="w-[1px] h-full bg-neutrals-G40 mx-3.5" />
            <div className="capitalize flex items-center gap-1">
              <RoundBadge
                variant={select === "all" || select === "" ? "default" : select}
                className={`${select === "" && "hidden"}`}
              />
              {select}
            </div>
          </SelectTrigger>
          <SelectContent>
            <SelectGroup>
              <SelectItem value="all">
                <div className="flex items-center gap-1">
                  <RoundBadge variant="default" />
                  All
                </div>
              </SelectItem>
              <SelectItem value="ongoing">
                <div className="flex items-center gap-1">
                  <RoundBadge variant="ongoing" />
                  Ongoing
                </div>
              </SelectItem>
              <SelectItem value="completed">
                <div className="flex items-center gap-1">
                  <RoundBadge variant="completed" />
                  Completed
                </div>
              </SelectItem>
              <SelectItem value="halted">
                <div className="flex items-center gap-1">
                  <RoundBadge variant="halted" />
                  Halted
                </div>
              </SelectItem>
            </SelectGroup>
          </SelectContent>
        </Select>
      </div>
    );
  },
);

ProjectsFilters.displayName = "ProjectsFilters";

export default ProjectsFilters;
