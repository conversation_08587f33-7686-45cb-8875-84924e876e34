import React, { ComponentPropsWithoutRef } from "react";
import { MapPin } from "lucide-react";
import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import { Status } from "@/types/Project";
import DayOfCompletionStatusBadge from "./DayOfCompletionStatusBadge";
import StageIcon from "../icons/Stage";
import WorkersIcon from "../icons/Workers";
import Calendar from "../icons/Calendar";

// Object { graph: (7) […], ProjectData: {…}, paymentData: {…} }
// ​
// ProjectData: Object { name: "test1", projectId: "BUN 1", location: "kottayam", … }
// ​​
// delayDate: null
// ​​
// designPeriod: "25 Apr - 25 Apr"
// ​​
// designProgress: Object { percentage: null, referenceDate: "25 Apr" }
// ​​
// expectedRevenue: 3
// ​​
// location: "kottayam"
// ​​
// margin: 1
// ​​
// name: "test1"
// ​​
// projectId: "BUN 1"
// ​​
// projectType: "67348288709f02e5216025d6"
// ​​
// scheduleLabel: "NaN Days delay"
// ​​
// status: "ongoing"
// ​​
// totalArchitects: 1
// ​​
// totalHours: 6
// ​​
// totalStages: 7
// ​​
// <prototype>: Obje

const ProjectInfoCard = ({
  project,
  //  data,
  className,
  ...props
}: any) => {
  const statusColorClass = () => {
    switch (project?.status) {
      case "completed":
        return "text-[#4CAF50] bg-[#4CAF50]";
      case "halted":
        return "text-[#E37575] bg-[#E37575]";
      default:
        return "text-[#d99400] bg-[#d99400]";
    }
  };

  return (
    <div
      className={cn(
        "w-full min-w-[634px] h-[210px] px-5 py-[18px] bg-white rounded-xl border border-neutrals-G40 relative overflow-hidden",
        className,
      )}
      {...props}
    >
      <div className="absolute top-0 left-0 w-full h-[88px] bg-primary-blue-B30" />

      <div className="relative">
        <div className="flex justify-between items-start mb-6">
          <div className="space-y-2">
            <h2 className="text-xl font-semibold text-neutrals-G900">
              {project?.name}
            </h2>
            <div className="flex gap-1">
              <div className="p-1 rounded border border-neutrals-G50 bg-neutrals-G30 h-[23px] inline-flex items-center">
                <span className="text-xs text-neutrals-G600">
                  #ID {project?.projectId}
                </span>
              </div>
              <div className="p-1 flex items-center rounded border border-neutrals-G50 bg-neutrals-G30 h-[23px]">
                <MapPin className="size-3.5 mr-1" />
                <span className="text-xs text-neutrals-G600">
                  {project?.location}
                </span>
              </div>
              <div className="p-1 rounded border border-neutrals-G50 bg-neutrals-G30 h-[23px] inline-flex items-center">
                <span className="text-xs text-neutrals-G600">
                  {project?.projectType}
                </span>
              </div>
            </div>
          </div>

          {/* <div className="flex items-center gap-1">
            <div
              className={`w-3 h-3 rounded-full ${statusColorClass().split(" ")[1]}`}
            />
            <span
              className={`text-xs font-medium ${statusColorClass().split(" ")[0]}`}
            >
              {project?.status}
            </span>
          </div> */}
        </div>

        <div className="flex justify-between text-neutrals-G800 items-center mb-6">
          <div className="flex items-center gap-1">
            <span className=" inline-flex items-center gap-1">
              <StageIcon />
              <span className="text-sm font-bold">
                {project?.totalStages || 0}
              </span>
              <span className="text-sm">Stages</span>
            </span>
          </div>
          <div className="flex items-center gap-1">
            <span className="inline-flex items-center gap-1">
              <WorkersIcon />
              <span className="text-sm font-bold">
                {project?.totalArchitects || 0}
              </span>
              <span className="text-sm">Architects</span>
            </span>
          </div>
          <div className="flex items-center gap-1">
            <span className=" inline-flex items-center gap-1">
              <Calendar />
              <span className="text-sm font-bold">
                {project?.totalHours || 0}
              </span>
              <span className="text-sm"> hours</span>
            </span>
          </div>

          <div className="flex items-center gap-1">
            <span className=" inline-flex items-center gap-1">
              <Calendar />
              <span className="text-sm">{project?.designPeriod}</span>
            </span>
          </div>
        </div>

        <div className="flex justify-between items-end h-[46px]">
          <div className="space-y-2">
            <span className="text-xs text-neutrals-G600">Expected revenue</span>
            <div className="flex items-center gap-1.5">
              <span className="text-sm font-medium text-neutrals-G600">
                ₹{project?.expectedRevenue || 0}
              </span>
              <div className=" bg-[#e8f5e8] rounded px-2">
                <span className="text-[10px] font-medium text-[#4caf50]">
                  {project?.margin || 0}% Margin
                </span>
              </div>
            </div>
          </div>

          <div className="w-[280px] space-y-1">
            <div className="flex justify-between items-center">
              <span className="text-xs text-neutrals-G600">
                Design Progress
              </span>
              <div className="flex items-center gap-1.5">
                <span className="text-xs text-neutrals-G600">
                  {project?.designProgress?.referenceDate}
                </span>

                <div className="flex items-center gap-1">
                  {/* <div
                    className={`w-3 h-3 rounded-full ${statusColorClass().split(" ")[1]}`}
                  /> */}
                  <span
                    className={`text-xs font-medium ${statusColorClass().split(" ")[0]}`}
                  >
                    {project?.status}
                  </span>
                </div>
                {/* <DayOfCompletionStatusBadge
                  projectedEndDate={startDate}
                  expectedEndDate={endDate}
                  className="shrink-0"
                /> */}
              </div>
            </div>
            <Progress
              value={project?.designProgress?.percentage ?? 0}
              // value={100}
              variant={
                project?.designProgress?.percentage < 40
                  ? "danger"
                  : project?.designProgress?.percentage < 90
                    ? "warning"
                    : "success"
              }
              className="h-1"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectInfoCard;
