import React from "react";

const NotificationIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="currentColor"
    className={className}
  >
    <path
      d="M2.03011 13.0625H9.61615C9.56343 14.0795 8.90804 14.7275 8.00419 14.7275C7.09261 14.7275 6.44461 14.0795 6.38418 13.0625H5.20904C5.26947 14.5089 6.40701 15.7818 8.00419 15.7818C9.59365 15.7818 10.7312 14.5089 10.7916 13.0625H13.9705C14.6864 13.0625 15.1084 12.6935 15.1084 12.1509C15.1084 11.3975 14.3473 10.7196 13.6919 10.0491C13.1946 9.52936 13.059 8.45965 13.0063 7.5934C12.9461 4.625 12.1853 2.70382 10.1738 1.98061C9.92504 1.00154 9.11151 0.217896 8.00419 0.217896C6.88883 0.217896 6.08301 1.00154 5.82683 1.98061C3.82304 2.70382 3.05451 4.625 3.00179 7.59307C2.94169 8.45965 2.81344 9.52936 2.30879 10.0491C1.66079 10.7196 0.892578 11.3975 0.892578 12.1513C0.892578 12.6935 1.32201 13.0625 2.03011 13.0625ZM2.36151 11.9246V11.8346C2.49715 11.6087 2.94169 11.1715 3.3409 10.727C3.87576 10.1243 4.13161 9.16004 4.19976 7.68372C4.25986 4.39872 5.23926 3.34443 6.53494 2.99793C6.72329 2.95261 6.82133 2.85457 6.82872 2.66622C6.85893 1.8755 7.30347 1.32522 8.00419 1.32522C8.69719 1.32522 9.14911 1.87518 9.17194 2.66622C9.17933 2.85457 9.28476 2.95229 9.47311 2.99761C10.7614 3.34443 11.7408 4.39904 11.8086 7.68372C11.8687 9.16004 12.1249 10.1243 12.6675 10.727C13.059 11.1715 13.5109 11.6087 13.6465 11.8346V11.9246H2.36151Z"
      fill="currentColor"
    />
  </svg>
);

export default NotificationIcon;
