import React from "react";

interface ErrorTextProps {
  entity: string;
  className?: string;
}

const ErrorText: React.FC<ErrorTextProps> = ({ entity, className = "" }) => {
  return (
    <div
      className={`text-red-400 justify-center items-center font-semibold flex h-screen ${className}`}
    >
      Oops! error loading {entity}. Please try again.
    </div>
  );
};

export default ErrorText;
