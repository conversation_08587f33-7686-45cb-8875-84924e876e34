"use client";

import { useState, useEffect, useRef } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter, useSearchParams } from "next/navigation";

import MilestonesSection from "@/components/projectAnalytics/MilestoneList";
import useGetProjectAnalytics from "@/services/project/getProjectAnalytics";
import NoDataToShow from "@/components/ui/noDataToShow";
import {
  Popover,
  PopoverClose,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { CalendarIcon } from "lucide-react";
import { Calendar } from "@/components/ui/calendar";
import useGetProjectById from "@/services/project/getProject";
import useGetProjectAnalyticsTotal from "@/services/project/getProjectAnalyticsTotal";
import Loader from "@/components/ui/loader";
import ErrorText from "@/components/ui/errortext";
import FlagIcon from "@/components/icons/Flag";
import ProjectInfoCard from "@/components/projectAnalytics/DesignAnalyticsCard";
import ConstructionDetailsCard from "@/components/projectAnalytics/ConstructionAnalyticsCard";
import PendingPaymentsCard from "@/components/projectAnalytics/PendingPaymentCard";
import BudgetHoursChart from "@/components/projectAnalytics/BugetedHoursChart";
import withTeamGuard from "@/components/TeamGuard";
import useGetProjectAnalyticsComplete from "@/services/project/completeProjectAnalytics";

const ProjectAnalyticsPage = () => {
  const { id } = useParams() as { id: string };
  const router = useRouter();
  const searchParams = useSearchParams();

  const [date, setDate] = useState<Date | null>(() => {
    const dateParam = searchParams.get("dateto");
    return dateParam ? new Date(dateParam) : null;
  });

  const {
    data: projectAnalyticsTotal,
    isPending: isPendingProjectAnalyticsTotal,
    isError: isErrorProjectAnalyticsTotal,
  } = useGetProjectAnalyticsTotal(id);

  const {
    data: projectData,
    isPending: isPendingProjectData,
    isError: isErrorProjectData,
  } = useGetProjectById(id);

  const { data, isPending, isError } = useGetProjectAnalytics(
    id,
    date || undefined,
  );

  const { data: completeData } = useGetProjectAnalyticsComplete(id);

  console.log(completeData, "completeData");

  useEffect(() => {
    if (data?.endDate && !date && !searchParams.get("dateto")) {
      // const endDate = new Date(data.endDate);
      const endDate = new Date();
      setDate(endDate);

      const params = new URLSearchParams(searchParams.toString());
      params.set("dateto", format(endDate, "yyyy-MM-dd"));
      router.push(`?${params.toString()}`);
    }
  }, [data, date, router, searchParams]);

  const handleDateSelect = (newDate: Date | null) => {
    setDate(newDate);

    const params = new URLSearchParams(searchParams.toString());

    if (newDate) {
      const formattedDate = format(newDate, "yyyy-MM-dd");
      params.set("dateto", formattedDate);
    } else {
      params.delete("dateto");
    }

    router.push(`?${params.toString()}`);
  };

  const popoverCloseRef = useRef<HTMLButtonElement>(null);

  if (isPending || isPendingProjectAnalyticsTotal || isPendingProjectData)
    return <Loader />;
  if (isError || isErrorProjectAnalyticsTotal || isErrorProjectData)
    return <ErrorText entity="analytics" />;

  if (!projectData) return null;

  const chartData = completeData?.graph?.map((item: any) => ({
    stage: item?.stageName,
    budgeted: item?.budgetAllocated,
    actual: item?.usedBudget,
  }));

  const projectScope = projectData?.projectScope ?? "both";

  return (
    <div className="flex flex-col h-screen ">
      <div className="grow  overflow-auto scrollbar-hide ">
        <div className="flex gap-4 mb-6">
          {(projectScope === "both" || projectScope === "design") && (
            <ProjectInfoCard project={completeData?.ProjectData} />
          )}
          {(projectScope === "both" || projectScope === "construction") && (
            <ConstructionDetailsCard
              project={projectAnalyticsTotal}
              data={data}
              projectScope={projectScope}
              projectDetail={completeData?.ProjectData}
            />
          )}
        </div>
        {/* <div className="flex gap-4 mb-6">
          <ProjectInfoCard project={completeData?.ProjectData} />
          <ConstructionDetailsCard
            project={projectAnalyticsTotal}
            data={data}
          />
        </div> */}
        <div className="flex gap-4 mb-6">
          <PendingPaymentsCard data={completeData?.paymentData} />
        </div>

        {/* <BudgetHoursChart data={chartData} /> */}
        {(projectScope === "both" || projectScope === "design") &&
          (chartData && chartData.length > 0 ? (
            <div className="flex gap-4 mb-6 w-full">
              <BudgetHoursChart data={chartData} />
            </div>
          ) : (
            <NoDataToShow />
          ))}
        {/* {chartData && chartData.length > 0 ? (
          <div className="flex gap-4 mb-6 w-full">
            <BudgetHoursChart data={chartData} />
          </div>
        ) : (
          <NoDataToShow />
        )} */}

        <div>
          <div className="flex justify-between items-center mb-2">
            <h3 className="text-name-title inline-flex items-center gap-1">
              <FlagIcon />
              <span className="text-neutrals-G800 font-semibold">
                {data.milestones.length}
              </span>{" "}
              {data.milestones.length === 1 ? "Milestone" : "Milestones"}
            </h3>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="input"
                  size="sm"
                  className={cn(
                    " text-neutrals-G600 font-normal w-auto border-primary-blue-B900/40 text-sm",
                    !date && "text-muted-foreground",
                  )}
                >
                  <CalendarIcon className="size-5 mr-1.5 text-primary" />
                  {date ? format(date, "PPP") : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <PopoverClose ref={popoverCloseRef} className="hidden" />

                <Calendar
                  selected={date || undefined}
                  onSelect={(value) => {
                    popoverCloseRef.current?.click();
                    setDate(value);
                    handleDateSelect(value);
                  }}
                  mode="single"
                  showOutsideDays={false}
                  endMonth={
                    new Date(Date.now() + 1000 * 60 * 60 * 24 * 365 * 10)
                  }
                  required={true}
                />
              </PopoverContent>
            </Popover>
          </div>
          {data.milestones.length === 0 ? (
            <NoDataToShow />
          ) : (
            <MilestonesSection milestones={data.milestones} />
          )}
        </div>
      </div>
    </div>
  );
};

export default withTeamGuard(ProjectAnalyticsPage);
