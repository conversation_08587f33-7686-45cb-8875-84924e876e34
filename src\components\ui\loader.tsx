import { cn } from "@/lib/utils";
import React from "react";

type Props = {
  size?: number;
  className?: string;
};

export const LoadingSpinner: React.FC<Props> = ({ size = 10, className }) => {
  const rem = 0.25;

  return (
    <div
      style={{ height: `${size * rem}rem`, width: `${size * rem}rem` }}
      className={cn(
        "rounded-full  border-4 border-primary border-t-blue-200 animate-spin",
        className,
      )}
    />
  );
};

const Loader: React.FC<Props> = ({ size = 10, className }) => {
  return (
    <div className={cn("flex justify-center items-center h-screen", className)}>
      <LoadingSpinner size={size} />
    </div>
  );
};

export default Loader;
