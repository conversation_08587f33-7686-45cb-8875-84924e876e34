"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import PageHeader from "@/components/layout/pageHeader/PageHeader";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { SearchIcon } from "lucide-react";
import ContractorGrid from "@/components/contractor/ContractorGrid";
import { CreateContractor } from "@/components/contractor/CreateContractor";
import useContractors from "@/services/contractor/getContractors";
import NoDataToShow from "@/components/ui/noDataToShow";
import Loader from "@/components/ui/loader";
import ErrorText from "@/components/ui/errortext";
import withTeamGuard from "@/components/TeamGuard";

const ITEMS_PER_PAGE = 10;

const ContractorPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [inputSearchTerm, setInputSearchTerm] = useState(
    searchParams.get("search") || "",
  );
  const [searchTerm, setSearchTerm] = useState(
    searchParams.get("search") || "",
  );

  const {
    contractors,
    isLoading,
    isError,
    isFetchingNextPage,
    fetchNextPage,
    hasNextPage,
  } = useContractors({
    search: searchTerm,
    limit: ITEMS_PER_PAGE,
  });

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setSearchTerm(inputSearchTerm);

    const params = new URLSearchParams(searchParams.toString());
    if (inputSearchTerm) {
      params.set("search", inputSearchTerm);
    } else {
      params.delete("search");
    }

    router.push(`/contractors?${params.toString()}`);
  };

  useEffect(() => {
    const urlSearchTerm = searchParams.get("search") || "";
    setInputSearchTerm(urlSearchTerm);
    setSearchTerm(urlSearchTerm);
  }, [searchParams]);

  if (isLoading) return <Loader />;
  if (isError) return <ErrorText entity="contractors" />;

  return (
    <div className="flex flex-col h-screen">
      <PageHeader>
        <div className="flex justify-between items-center w-full">
          <div className="flex items-center gap-4">
            <PageHeader.Heading>Contractors</PageHeader.Heading>
            <form onSubmit={handleSearch} className="flex gap-2">
              <Input
                placeholder="Search contractors..."
                className="w-[300px] h-10"
                value={inputSearchTerm}
                onChange={(e) => setInputSearchTerm(e.target.value)}
              />
              <Button type="submit" className="h-10 w-10">
                <SearchIcon />
              </Button>
            </form>
          </div>

          <CreateContractor />
        </div>
      </PageHeader>

      <div className="grow p-10">
        {contractors?.length === 0 ? (
          <NoDataToShow />
        ) : (
          <ContractorGrid
            data={contractors}
            isLoading={isLoading}
            isFetchingNextPage={isFetchingNextPage}
            hasNextPage={hasNextPage}
            fetchNextPage={fetchNextPage}
          />
        )}
      </div>
    </div>
  );
};

export default withTeamGuard(ContractorPage);
