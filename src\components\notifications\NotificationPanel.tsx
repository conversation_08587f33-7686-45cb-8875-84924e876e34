import { useEffect } from "react";
import { X } from "lucide-react";
import { useInView } from "react-intersection-observer";

import { Card } from "@/components/ui/card";
import NotificationCard from "./NotificationCard";
import { useNotificationPanelContext } from "./NotificationpanelWrapper";
import useGetNotifications from "@/services/notification/getNotifications";
import NoDataToShow from "../ui/noDataToShow";
import Loader, { LoadingSpinner } from "../ui/loader";
import { cn } from "@/lib/utils";
import useMarkAllNotificationAsRead from "@/services/notification/markAllNotificationAsRead";
import { Button } from "../ui/button";

const NotificationPanel = () => {
  const { onClose } = useNotificationPanelContext();

  const { data, isPending, fetchNextPage, hasNextPage, isFetchingNextPage } =
    useGetNotifications();

  const { mutate: markAllAsRead, isPending: isPendingMarkAllAsRead } =
    useMarkAllNotificationAsRead();

  const { ref, inView } = useInView();

  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, fetchNextPage, hasNextPage, isFetchingNextPage]);

  return (
    <Card className="w-[540px] h-screen overflow-y-auto scrollbar-hide py-6 bg-white flex flex-col justify-start items-center gap-8">
      <div className="flex flex-col items-center w-full flex-1">
        <div className="flex justify-between items-center mb-6 w-full px-6">
          <div className="flex items-center gap-x-2">
            <button className="relative cursor-pointer" onClick={onClose}>
              <X className="size-5 stroke-[2.5]" />
            </button>
            <h5 className="text-neutrals-G900 font-semibold text-xl">
              Notifications
            </h5>
          </div>
          <Button
            onClick={() => markAllAsRead()}
            loading={isPendingMarkAllAsRead}
            variant="ghost"
            className="text-neutrals-G600 px-4 font-normal h-auto hover:bg-inherit"
          >
            Mark all as read
          </Button>
        </div>
        {isPending ? (
          <LoadingSpinner size={8} className="mt-6" />
        ) : data.length === 0 ? (
          <div className="w-[540px] py-6 bg-white flex flex-col justify-center h-screen items-center">
            <NoDataToShow />
          </div>
        ) : (
          <div className="space-y-[1px]">
            {data.map((notification) => (
              <NotificationCard
                key={notification._id}
                notification={notification}
              />
            ))}
            <div
              ref={ref}
              className={cn(
                "w-full flex items-center justify-center",
                isFetchingNextPage ? " h-20" : "h-0",
              )}
            >
              {isFetchingNextPage && <Loader size={10} />}
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

export default NotificationPanel;
