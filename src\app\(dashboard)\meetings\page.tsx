"use client";

import PageHeader from "@/components/layout/pageHeader/PageHeader";
import MeetingDetails from "@/components/meetings/Meetingdetails";
import withTeamGuard from "@/components/TeamGuard";

const MeetingsPage = () => {
  return (
    <div className="flex flex-col gap-y-6">
      <div className="flex flex-col ">
        <PageHeader>
          <div>
            <PageHeader.Heading>Meetings</PageHeader.Heading>
            <PageHeader.Description>
              Manage all your meetings{" "}
            </PageHeader.Description>
          </div>
        </PageHeader>
      </div>
      <div className=" px-5 overflow-auto scrollbar-hide">
        <MeetingDetails />
      </div>
    </div>
  );
};

export default withTeamGuard(MeetingsPage);
