import { ComponentPropsWithoutRef } from "react";
import { MapPin } from "lucide-react";
import { format } from "date-fns";

import { Progress } from "@/components/ui/progress";
import { cn } from "@/lib/utils";
import Users from "../icons/Users";
import Productivity from "../icons/Productivity";
import Calendar from "../icons/Calendar";
import DayOfCompletionStatusBadge from "../projectAnalytics/DayOfCompletionStatusBadge";
import { DashboardAnalytics } from "@/types/DashboardAnalytics";

type ProjectCardProps = ComponentPropsWithoutRef<"div"> & {
  project: DashboardAnalytics["projects"][0];
};

const ProjectCard = ({ project }: ProjectCardProps) => {
  return (
    <div
      className={cn(
        "p-6 rounded-xl border border-neutrals-G40 bg-white flex flex-col gap-y-7",
      )}
    >
      <div className="space-y-1">
        <h6 className="text-neutrals-G900 font-semibold text-xl">
          {project.projectName}
        </h6>
        <div className="flex gap-x-1 items-start text-xs text-neutrals-G600">
          <div className="px-1 h-[23px] flex items-center rounded border border-neutrals-G50 bg-neutrals-G30">
            #ID {project.projectId}
          </div>
          <div className="px-1 h-[23px]  items-center rounded border border-neutrals-G50 bg-neutrals-G30 flex gap-x-1">
            <MapPin className="size-3.5" /> {project.location}
          </div>
        </div>
      </div>
      <div className="space-y-5">
        <div className="grid grid-cols-2 gap-2.5 justify-between text-neutrals-G800 text-sm">
          <div className="flex gap-x-1 items-center">
            <Users className="text-neutrals-G100" />
            <p>
              <strong className="font-bold"> {project.noOfWorker}</strong>{" "}
              workers
            </p>
          </div>
          <div className="flex gap-x-1 items-center justify-end">
            <Productivity />
            <p>
              <strong className="font-bold">
                {project.workerProductivity?.toFixed(2)}%
              </strong>{" "}
              Productivity
            </p>
          </div>
          <div className="flex gap-x-1 items-center">
            <Calendar />
            <p>
              {format(new Date(project.startDate), "dd MMM")} -{" "}
              {format(new Date(project.endDate), "dd MMM")}
            </p>
          </div>
        </div>
        <div className="space-y-1">
          <div className="flex justify-between items-center text-neutrals-G600 text-xs">
            <p>Completed on</p>
            <div className="flex gap-x-1.5 items-center">
              <p> {format(new Date(project.projectedEndDate), "dd MMM")}</p>
              <DayOfCompletionStatusBadge
                projectedEndDate={project.projectedEndDate}
                expectedEndDate={project.endDate}
                className="shrink-0"
              />
            </div>
          </div>
          <Progress
            value={project.progress}
            variant={
              project.progress < 40
                ? "danger"
                : project.progress < 90
                  ? "warning"
                  : "success"
            }
            className="h-1"
          />
        </div>
      </div>
    </div>
  );
};

export default ProjectCard;
