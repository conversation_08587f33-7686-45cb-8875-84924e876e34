import { useState } from "react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";

import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alertDialog";
import { Button } from "@/components/ui/button";
import DeleteIcon from "@/components/icons/Delete";
import useDeleteChecklist from "@/services/qualityChecklist/deleteChecklist";

type DeleteChecklistProps = { projectId: string; checklistId: string };

const DeleteChecklist = ({ projectId, checklistId }: DeleteChecklistProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const router = useRouter();

  const { mutate, isPending } = useDeleteChecklist(() => {
    router.push(`/projects/${projectId}/quality-checklist`);
    toast.success("Checklist deleted successfully");
    setIsOpen(false);
  });

  const handleDeleteChecklist = () => {
    mutate({ projectId, qualityChecklistId: checklistId });
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger asChild>
        <Button variant="destructive" className="gap-x-2 pl-4 pr-3 h-[34px]">
          Delete Checklist <DeleteIcon className="text-white" />
        </Button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Checklist?</AlertDialogTitle>
          <AlertDialogDescription>
            Doing this will remove the all the data within this checklist{" "}
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button loading={isPending} onClick={handleDeleteChecklist}>
              Yes, Delete
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DeleteChecklist;
