"use client";

import { useState } from "react";
import { DateRange } from "react-day-picker";
import { format, subDays } from "date-fns";

import PageHeader from "@/components/layout/pageHeader/PageHeader";
import TimesheetCard from "@/components/timesheets/TimesheetCard";
import DateRangePicker from "@/components/ui/dateRangePicker";
import AddTimesheet from "@/components/timesheets/AddTimesheet";
import useGetTimesheet from "@/services/timesheet/getTimesheet";
import ErrorText from "@/components/ui/errortext";
import NoDataToShow from "@/components/ui/noDataToShow";

const defaultRange = {
  to: subDays(new Date(), 1),
  from: subDays(new Date(), 8),
};

const TimesheetsPage = () => {
  const [dateRange, setDateRange] = useState<DateRange | undefined>(
    defaultRange,
  );

  const { data, isSuccess, isError } = useGetTimesheet({
    startDate: dateRange?.from
      ? format(dateRange.from, "yyyy-MM-dd")
      : format(defaultRange.from, "yyyy-MM-dd"),
    endDate: dateRange?.to
      ? format(dateRange.to, "yyyy-MM-dd")
      : format(defaultRange.to, "yyyy-MM-dd"),
  });

  const handleDateRangeChange = (range: DateRange | undefined) => {
    setDateRange(range);
  };

  if (isError) return <ErrorText entity="timesheets" />;

  return (
    <div className="flex flex-col h-screen">
      <PageHeader>
        <div>
          <PageHeader.Heading>Timesheets</PageHeader.Heading>
          <PageHeader.Description>Manage your team </PageHeader.Description>
        </div>
        <AddTimesheet />
      </PageHeader>
      <div className="flex flex-1 overflow-auto p-5 flex-col gap-y-8 scrollbar-hide">
        <div className="space-y-4">
          <div className="flex justify-between items-start text-neutrals-G300 text-sm">
            <p>
              Total{" "}
              <span className="font-bold">{data?.current[0]?.totalHours}</span>{" "}
              hours
            </p>
            <p>Today, {format(new Date(), "dd MMM yyyy")}</p>
          </div>
          <div className="grid grid-cols-3 gap-1">
            {isSuccess &&
              data.current.map((timesheet) =>
                timesheet.entries.map((entry) => (
                  <TimesheetCard current={true} key={entry._id} data={entry} />
                )),
              )}
          </div>
        </div>
        <div className="space-y-6">
          <div className="flex justify-between items-start">
            <div className="space-y-1">
              <h4 className="text-neutrals-G900 font-semibold">History</h4>
              <p className="text-neutrals-G300 text-xs">
                Previous timesheet entry will be listed here
              </p>
            </div>
            <DateRangePicker
              onChange={handleDateRangeChange}
              defaultValue={dateRange}
            />
          </div>
          {isSuccess &&
            (data.history.length === 0 ? (
              <div className="justify-center align-center flex">
                <NoDataToShow />
              </div>
            ) : (
              data.history.map((timesheet) => (
                <div key={timesheet._id} className="space-y-4">
                  <div className="flex justify-between items-start text-neutrals-G300 text-sm">
                    <p>
                      Total{" "}
                      <span className="font-bold">{timesheet.totalHours}</span>{" "}
                      {timesheet.totalHours === 1 ? "hour" : "hours"}
                    </p>
                    <p>{format(timesheet._id, "dd MMM yyyy")}</p>
                  </div>
                  <div className="grid grid-cols-3 gap-1">
                    {timesheet.entries.map((entry) => (
                      <TimesheetCard key={entry._id} data={entry} />
                    ))}
                  </div>
                </div>
              ))
            ))}
        </div>
      </div>
    </div>
  );
};

export default TimesheetsPage;
