import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "../ui/button";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@/components/ui/table";
// import MonthPicker from "./MonthPicker";
import WorklogMonthPicker from "./WorklogMonthPicker";
import { useMemo } from "react";
import useGetTeamMembersByMonth from "@/services/teamMember/getTeamMemberWorklog";
import { useSearchParams } from "next/navigation";
import Loader2 from "../ui/Loader2";

const Worklog = ({ member }: any) => {
  const searchParams = useSearchParams();
  const monthParam = searchParams.get("month") || "";

  const currentYear = new Date().getFullYear();
  const currentMonth = new Date()
    .toLocaleString("default", { month: "long" })
    .toLowerCase();

  const monthNameToNumber = (monthName: string) => {
    const monthMap: Record<string, string> = {
      january: "01",
      february: "02",
      march: "03",
      april: "04",
      may: "05",
      june: "06",
      july: "07",
      august: "08",
      september: "09",
      october: "10",
      november: "11",
      december: "12",
    };
    return monthMap[monthName];
  };

  // const formattedMonth =
  //   monthParam !== "" ? `${currentYear}-${monthNameToNumber(monthParam)}` : "";

  const formattedMonth =
    monthParam !== ""
      ? `${currentYear}-${monthNameToNumber(monthParam)}`
      : `${currentYear}-${monthNameToNumber(currentMonth)}`;

  const { data: worklogData, isPending } =
    useGetTeamMembersByMonth(formattedMonth);

  const currentMember = useMemo(() => {
    return worklogData?.find((m: any) => m._id === member._id);
  }, [worklogData, member]);

  const workLogs = currentMember?.workLogs || [];

  console.log(workLogs, "workingdata");
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="bg-primary-blue-B40 h-[31px] px-3 text-xs border border-primary-blue-B80 font-medium text-primary-blue-B900 w-[103px]"
        >
          View Work log
        </Button>
      </DialogTrigger>
      <DialogContent
        isCloseIconVisible
        className="text-neutrals-G900 gap-y-8 sm:max-w-[751px]"
      >
        <div>
          <DialogHeader className="space-y-1 mb-2">
            <DialogTitle>Are you absolutely sure?</DialogTitle>
            <DialogDescription className="capitalize">
              {member?.role}
            </DialogDescription>
          </DialogHeader>
          <div className="flex items-center justify-between mb-2">
            <p className="text-sm">
              Total hours worked:{" "}
              <span className="font-semibold">
                {" "}
                {workLogs?.reduce(
                  (total: number, log: any) => total + log.hour,
                  0,
                )}
              </span>
            </p>
            <WorklogMonthPicker />
          </div>
          <div className="h-0.5 bg-primary-blue-B40" />
        </div>
        <div>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="basis-1/3">Project</TableHead>
                <TableHead className="basis-1/3">Stage</TableHead>
                <TableHead className="basis-1/3">Hours</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isPending ? (
                <TableRow>
                  <TableCell colSpan={3} className="text-center py-4">
                    <Loader2 size={1} />
                  </TableCell>
                </TableRow>
              ) : workLogs.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={3}
                    className="text-center py-4 text-sm text-gray-500"
                  >
                    No worklogs data found
                  </TableCell>
                </TableRow>
              ) : (
                workLogs?.map((log: any, index: number) => (
                  <TableRow
                    key={log?._id || index}
                    className="hover:bg-gray-50"
                  >
                    <TableCell>{log?.projectName || "N/A"}</TableCell>
                    <TableCell>{log?.stageName || "N/A"}</TableCell>
                    <TableCell>{log?.hour}</TableCell>
                  </TableRow>
                ))
              )}
              {/* {hasNextPage && (
                <TableRow ref={ref}>
                  <TableCell colSpan={3} className="text-center p-4">
                    {isLoading ? <Loader size={5} /> : "Load more..."}
                  </TableCell>
                </TableRow>
              )} */}
            </TableBody>
          </Table>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default Worklog;
