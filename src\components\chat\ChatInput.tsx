"use client";

import {
  ChangeEvent,
  ComponentProps,
  useRef,
  useState,
  useCallback,
} from "react";
import { toast } from "sonner";
import Image from "next/image";
import { XIcon } from "lucide-react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";

import { cn } from "@/lib/utils";
import ImageIcon from "../icons/Image";
import Play2 from "../icons/Play2";
import { chatSchema } from "@/schema/chat";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage,
} from "@/components/ui/form";
import {
  useGenerateS3Url,
  useUploadToS3,
} from "@/services/project/sitedocs-hooks";
import { Textarea } from "../ui/textarea";

type ChatInputProps = ComponentProps<"form"> & {
  chatId: string;
  onFormSubmit: (message: z.infer<typeof chatSchema>) => void;
};

const ChatInput = ({
  chatId,
  onFormSubmit,
  className,
  ...props
}: ChatInputProps) => {
  const fileInputRef = useRef<HTMLInputElement | null>(null);

  const [file, setFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);

  const form = useForm<z.infer<typeof chatSchema>>({
    resolver: zodResolver(chatSchema),
    defaultValues: {
      groupId: chatId,
      message: "",
    },
  });

  const removeImage = useCallback(() => {
    if (imagePreview) {
      URL.revokeObjectURL(imagePreview);
    }

    setImagePreview(null);
    setFile(null);
    if (fileInputRef.current) fileInputRef.current.value = "";
  }, [imagePreview]);

  const handleImageChange = (e: ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (!file) return;

    if (!file.type.startsWith("image/")) {
      toast.error("Please select an image file");
      return;
    }

    const preview = URL.createObjectURL(file);
    setImagePreview(preview);
    setFile(file);
  };

  const { mutateAsync: generateS3Url } = useGenerateS3Url();
  const { mutateAsync: uploadToS3 } = useUploadToS3();

  const handleFileUpload = async (file: File) => {
    try {
      const signedUrl = await generateS3Url({
        fileName: file.name,
        projectId: chatId,
      });

      await uploadToS3({
        signedUrl,
        file: file,
      });

      const s3Link = signedUrl.split("?")[0];

      return s3Link;
    } catch (error: any) {
      console.error("Error uploading file:", error);
      toast.error("Failed to upload file. Please try again.");
    }
  };

  const onSubmit = async (values: z.infer<typeof chatSchema>) => {
    if (!values.message.trim() && !file) {
      // toast.error("Message cannot be empty unless an image is uploaded.");
      return;
    }

    let s3Link;

    // if an image is selected
    if (file) {
      s3Link = await handleFileUpload(file);
    }

    onFormSubmit({ ...values, image: s3Link });

    form.reset();
    removeImage();
  };

  // const onSubmit = async (values: z.infer<typeof chatSchema>) => {
  //   let s3Link;

  //   if (file) {
  //     s3Link = await handleFileUpload(file);
  //   }

  //   onFormSubmit({ ...values, image: s3Link });

  //   form.reset();
  //   removeImage();
  // };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className={cn(
          "pt-3 px-5 pb-4 bg-white flex items-center justify-between gap-x-6 sticky bottom-0",
          className,
        )}
        {...props}
      >
        <div className="flex items-center">
          {imagePreview ? (
            <div className="relative">
              <Image
                src={imagePreview}
                width={24}
                height={24}
                alt="Preview"
                className="object-cover rounded-lg border border-zinc-700 min-h-6"
              />
              <button
                onClick={removeImage}
                className="absolute -top-1.5 -right-1.5 size-4 bg-primary-blue-B900 rounded-full bg-base-300
              flex items-center justify-center"
                type="button"
              >
                <XIcon className="size-3 text-white" />
              </button>
            </div>
          ) : (
            <>
              <input
                type="file"
                accept="image/*"
                className="hidden"
                ref={fileInputRef}
                onChange={handleImageChange}
              />
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
              >
                <ImageIcon />
              </button>
            </>
          )}
        </div>

        <FormField
          control={form.control}
          name="message"
          render={({ field }) => (
            <FormItem className="flex-1 bg-neutrals-G30">
              <FormControl>
                <Textarea
                  {...field}
                  className="bg-inherit resize-none scrollbar-hide min-h-0 h-[43px] max-h-[62px]"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <button>
          <Play2 />
        </button>
      </form>
    </Form>
  );
};

export default ChatInput;
