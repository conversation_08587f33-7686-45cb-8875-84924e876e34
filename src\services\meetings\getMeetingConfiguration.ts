import api from "@/lib/api-client";
import { useQuery } from "@tanstack/react-query";
import { MeetingConfiguration } from "@/types/Meetings";

const getMeetingConfiguration = async (): Promise<MeetingConfiguration> => {
  const response = await api.get("/meetings/meeting-configuration");

  return response.data;
};

const useGetMeetingConfiguration = () => {
  return useQuery({
    queryKey: ["meeting-configuration"],
    queryFn: getMeetingConfiguration,
    refetchOnWindowFocus: true,
  });
};

export default useGetMeetingConfiguration;
