import { queryClient } from "@/app/RootLayoutClient";
import Axios from "axios";
import Cookies from "js-cookie";
import { getToken } from "./utils";

const api = Axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_URL,
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
    "x-org-type": "architect",
    "ngrok-skip-browser-warning": "any",
  },
});

api.interceptors.request.use(async (config) => {
  const token = await getToken();
  if (token) {
    Cookies.set("token", token);
    config.headers.authorization = `Bearer ${token}`;
  }

  return config;
});

api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  async (error) => {
    if (error.response.status === 401) {
      Cookies.remove("token");
      queryClient.setQueryData(["user"], null);

      console.error("error: ", error);
    }
    return await Promise.reject(error);
  },
);

export default api;
