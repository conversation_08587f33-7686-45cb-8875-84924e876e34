import { Fragment, useEffect, useState } from "react";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { toast } from "sonner";

import { <PERSON>alog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import EditIcon from "../icons/Edit";
import {
  Form,
  FormControl,
  FormField,
  FormMessage,
} from "@/components/ui/form";
import { productivityDatabaseSchema } from "@/schema/productivityDatabase";
import { ProductivityDatabase } from "@/types/ProductivityDatabase";
import useUpdateCorrespondingUnits from "@/services/productivityDatabase/updateCorrespondingUnits";
import useUpdateProductivityDatabase from "@/services/productivityDatabase/updateProductivityDatabase";
import { Input } from "../ui/input";
import { X } from "lucide-react";

type ModifyQuantityProps = {
  projectId: string;
  milestone: { _id: string; name: string };
  worktype: ProductivityDatabase["worktypes"][0];
};

// function formatNumber(value: number | string) {
//   console.log("CB", value);
//   if (value === "" || value === undefined || value === null) return "NA";
//   const num = Number(value);
//   return Number.isInteger(num) ? num : num.toFixed(2);
// }

const ModifyQuantity = ({
  projectId,
  milestone,
  worktype,
}: ModifyQuantityProps) => {
  const [isOpen, setIsOpen] = useState(false);
  // console.log("id", worktype.standardUnits);

  const form = useForm<z.infer<typeof productivityDatabaseSchema>>({
    resolver: zodResolver(productivityDatabaseSchema),
    defaultValues: {
      standardUnits: worktype.standardUnits.map((unit) => ({
        unit: unit.unit._id,
        minQuantity: unit.minQuantity.toString(),
        maxQuantity: unit.maxQuantity.toString(),
      })),
      optionalUnits: worktype.optionalUnits.map((unit) => ({
        unit: unit.unit._id,
        minQuantity: unit.minQuantity.toString(),
        maxQuantity: unit.maxQuantity.toString(),
      })),
    },
    mode: "onChange",
  });

  const onError = () => {
    form.reset();
  };
  const handleClose = () => {
    // setIsOpen(false);
    // // form.reset();
    onOpenChange(false);
  };

  const {
    mutate: updateCorrespondingUnits,
    isPending: isPendingUpdateCorrespondingUnits,
  } = useUpdateCorrespondingUnits((data, variables) => {
    const fieldsToUpdate = data.filter(
      (unit) => unit.unit._id !== variables.unit,
    );

    const updateFields = () => {
      fieldsToUpdate.forEach((unit) => {
        const quantityType =
          variables.type === "min" ? "minQuantity" : "maxQuantity";

        form.setValue(
          `standardUnits.${data.indexOf(unit)}.${quantityType}`,
          unit[quantityType].toString(),
        );
      });
    };

    updateFields();
  }, onError);

  const {
    mutate: updateProductivityDatabase,
    isPending: isPendingUpdateProductivityDatabase,
    isSuccess,
  } = useUpdateProductivityDatabase(() => {
    onOpenChange(false);
    toast.success("Productivity database updated successfully");
  });

  useEffect(() => {
    if (!isOpen) {
      form.reset();
    }
  }, [form, isOpen]);
  const onOpenChange = (open: boolean) => {
    // if (!open) {
    //   form.reset();
    // }
    setIsOpen(open);
  };

  const onSubmit = (data: z.infer<typeof productivityDatabaseSchema>) => {
    updateProductivityDatabase({
      projectId,
      worktypeId: worktype._id,
      ...data,
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogTrigger>
        <EditIcon />
      </DialogTrigger>
      <DialogContent className="w-[510px] p-6 rounded-xl">
        <div className="flex flex-col gap-[30px]">
          <div className="flex flex-col gap-2">
            <div className="flex justify-between items-center">
              <h2 className="text-xl font-semibold text-neutrals-G900">
                Modify Quantity
              </h2>
              <Button type="button" variant="outline" onClick={handleClose}>
                <X className="h-6 w-6" />
              </Button>
            </div>
            <p className="text-sm text-neutrals-G600">
              Set the amount of work an individual has to do each day.
            </p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <p className="text-sm text-neutrals-G600 mb-2">Milestone</p>
                  <p className="text-base font-medium text-neutrals-G900">
                    {milestone.name}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-neutrals-G600 mb-2">Work type</p>
                  <p className="text-base font-medium text-neutrals-G900">
                    {worktype.name}
                  </p>
                </div>
              </div>

              <div className="space-y-5">
                <div>
                  <h3 className="text-sm font-medium text-neutrals-G900 mb-[20px]">
                    Standard unit
                  </h3>
                  <div className="grid grid-cols-2 gap-3">
                    <p className="text-sm text-Grey-Dark-Grey mb-2">Min</p>
                    <p className="text-sm text-Grey-Dark-Grey mb-2">Max</p>
                  </div>

                  <div
                    className={`grid  ${worktype.standardUnits.length === 1 ? "grid-rows-1" : "grid-rows-2"} grid-flow-col gap-3 items-start`}
                  >
                    {worktype.standardUnits.map((unit, index) => (
                      <div key={unit.unit._id} className="space-y-1">
                        <FormField
                          control={form.control}
                          name={`standardUnits.${index}.minQuantity`}
                          render={({ field }) => (
                            <FormControl className="flex items-center px-3 py-2.5 rounded-md border border-border-gray">
                              <div>
                                <Input
                                  type="number"
                                  {...field}
                                  className="border-none shadow-none p-0 h-auto"
                                  onChange={(e) => {
                                    const value = e.currentTarget.value;
                                    field.onChange(value);
                                    if (value) {
                                      updateCorrespondingUnits({
                                        projectId,
                                        worktypeId: worktype._id,
                                        value,
                                        unit: unit.unit._id,
                                        type: "min",
                                      });
                                    }
                                  }}
                                />
                                <span className="text-neutrals-G100 text-base font-medium ml-2 whitespace-nowrap">
                                  {unit.unit.name}
                                </span>
                              </div>
                            </FormControl>
                          )}
                        />
                      </div>
                    ))}

                    {worktype.standardUnits.map((unit, index) => (
                      <FormField
                        key={unit.unit._id}
                        control={form.control}
                        name={`standardUnits.${index}.maxQuantity`}
                        render={({ field }) => (
                          <div>
                            <FormControl>
                              <div className="flex items-center px-3 py-2.5 rounded-md border border-border-gray">
                                <Input
                                  type="number"
                                  {...field}
                                  className="border-none shadow-none p-0 h-auto"
                                  onChange={(e) => {
                                    const value = e.currentTarget.value;
                                    field.onChange(value);
                                    if (value) {
                                      updateCorrespondingUnits({
                                        projectId,
                                        worktypeId: worktype._id,
                                        value,
                                        unit: unit.unit._id,
                                        type: "max",
                                      });
                                    }
                                  }}
                                />
                                <span className="text-neutrals-G100 text-base font-medium ml-2 whitespace-nowrap">
                                  {unit.unit.name}
                                </span>
                              </div>
                            </FormControl>
                            <FormMessage />
                          </div>
                        )}
                      />
                    ))}
                  </div>
                </div>
              </div>

              {worktype.optionalUnits.length > 0 && (
                <div className="space-y-5">
                  <div className="space-y-2">
                    <div className="flex items-center gap-1">
                      <h3 className="text-sm font-medium text-neutrals-G900">
                        Optional unit
                      </h3>
                    </div>
                    <p className="text-sm text-Grey-Dark-Grey">
                      Specify a custom unit for tracking progress. For example,
                      update by walls completed or bricks used, allowing
                      flexibility in reporting.
                    </p>
                  </div>
                  <div className="flex gap-2">
                    {worktype.optionalUnits.map((unit, index) => (
                      <Fragment key={unit.unit._id}>
                        <div className="flex-1">
                          <p className="text-sm text-Grey-Dark-Grey mb-2">
                            Min
                          </p>
                          <FormField
                            control={form.control}
                            name={`optionalUnits.${index}.minQuantity`}
                            render={({ field }) => (
                              <FormControl>
                                <div className="space-y-2">
                                  <div className="flex items-center px-3 py-2.5 rounded-md border border-border-gray ">
                                    <Input
                                      type="number"
                                      {...field}
                                      className="border-none shadow-none p-0 h-auto"
                                    />
                                    <span className="text-neutrals-G100 text-base font-medium ml-2 whitespace-nowrap">
                                      {unit.unit.name}
                                    </span>
                                  </div>
                                  <FormMessage />
                                </div>
                              </FormControl>
                            )}
                          />
                        </div>
                        <div className="flex-1">
                          <p className="text-sm text-Grey-Dark-Grey mb-2">
                            Max
                          </p>
                          <FormField
                            control={form.control}
                            name={`optionalUnits.${index}.maxQuantity`}
                            render={({ field }) => (
                              <div>
                                <FormControl>
                                  <div className="flex items-center px-3 py-2.5 rounded-md border border-border-gray">
                                    <Input
                                      type="number"
                                      {...field}
                                      className="border-none shadow-none p-0 h-auto"
                                    />
                                    <span className="text-neutrals-G100 text-base font-medium ml-2 whitespace-nowrap">
                                      {unit.unit.name}
                                    </span>
                                  </div>
                                </FormControl>
                                <FormMessage />
                              </div>
                            )}
                          />
                        </div>
                      </Fragment>
                    ))}
                  </div>
                </div>
              )}

              <div className="flex justify-end ">
                <Button
                  type="submit"
                  className=" px-4 py-3"
                  loading={
                    isPendingUpdateCorrespondingUnits ||
                    isPendingUpdateProductivityDatabase
                  }
                >
                  Save
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default ModifyQuantity;
