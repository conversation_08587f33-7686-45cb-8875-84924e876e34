import { StateCreator } from "zustand";

import { CreatedMessage } from "@/types/Socket";

export interface DesignStageSlice {
  messages: { [id: string]: CreatedMessage[] };
  getMessagesByChatId: (chatId: string) => CreatedMessage[];
  updateMessages: (newMessages: { [id: string]: CreatedMessage[] }) => void;
  replaceMessagesWithChatId: (
    chatId: string,
    newMessages: CreatedMessage[],
  ) => void;
}

export const designStageSlice: StateCreator<DesignStageSlice> = (set, get) => ({
  messages: {},
  getMessagesByChatId: (chatId) => {
    return get().messages[chatId];
  },
  updateMessages: (newMessages) => {
    set({ messages: newMessages });
  },

  replaceMessagesWithChatId: (chatId, newMessages) => {
    set({ messages: { ...get().messages, [chatId]: newMessages } });
  },
});
