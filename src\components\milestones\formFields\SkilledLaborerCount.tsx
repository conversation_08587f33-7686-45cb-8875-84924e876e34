import { useParams } from "next/navigation";
import { useWatch } from "react-hook-form";
import { toast } from "sonner";

import useGetLabourCount from "@/services/milestone/getLabourCount";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

type SkilledLabourCountProps = {
  fieldIndex: number;
};

const SkilledLaborerCount = ({ fieldIndex }: SkilledLabourCountProps) => {
  const { id } = useParams() as { id: string };

  const getFieldName = (name: string) => {
    return fieldIndex !== undefined ? `worktypes.${fieldIndex}.${name}` : name;
  };

  const [worktypeTemplateId, duration, quantity, unit] = useWatch({
    name: [
      getFieldName("worktypeTemplateId"),
      getFieldName("duration"),
      getFieldName("totalQty"),
      getFieldName("unit"),
    ],
  });

  const isFetchEnabled =
    !!duration && !!quantity && !!unit && !!worktypeTemplateId && !!id;

  const { data, isError } = useGetLabourCount(
    {
      projectId: id,
      worktypeTemplateId,
      duration,
      quantity,
      unit,
    },
    {
      enabled: isFetchEnabled,
    },
  );

  if (isError) {
    toast.error("Error fetching labour count");
  }

  return (
    <>
      <div className="basis-1/2">
        <Label className="text-neutrals-G100 text-xs">MIN</Label>
        <div className="relative">
          <Input value={data?.minLabourCount} disabled className="pr-[46px]" />
          <span className="absolute top-1/2 right-3 -translate-y-1/2 text-neutrals-G100 font-medium">
            no.s
          </span>
        </div>
      </div>
      <div className="basis-1/2">
        <Label className="text-neutrals-G100 text-xs">MAX</Label>
        <div className="relative">
          <Input value={data?.maxLabourCount} disabled />
          <span className="absolute top-1/2 right-3 -translate-y-1/2 text-neutrals-G100 font-medium">
            no.s
          </span>
        </div>
      </div>
    </>
  );
};

export default SkilledLaborerCount;
