export type WorkType = {
  _id: string;
  worktype: string;
  milestoneTemplateId: string;
  defaultUnit: string;
  standardUnits: string[];
  unitConversions: Array<{
    from: string;
    to: string;
    conversionFactor: number;
  }>;
  optionalUnits: string[];
  defaultQuantityPerDayMinimum: number;
  defaultQuantityPerDayMaximum: number;
  materialQuantityPerDefaultUnit: Array<{
    material: string;
    quantity: number;
  }>;
  skilledLabourName: string;
};

export type Milestone = {
  _id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
};

export type ChecklistItem = {
  item: string;
  isTemplate: boolean;
  answer: string;
  remarks: string;
  _id: string;
};

export type QualityChecklistData = {
  _id: string;
  milestoneTemplateId: Milestone;
  worktypeTemplateId: WorkType;
  name: string;
  quanlityCheckListId: string;
  qualityCheckListTemplateId: string;
  projectId: string;
  checklist: ChecklistItem[];
  status: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
  milestoneId: string;
};

export type QualityChecklist = {
  _id: string;
  milestone: Milestone;
  data: QualityChecklistData[];
};
