import React from "react";
import { cn } from "@/lib/utils";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";

interface SidebarAccordionProps {
  title: string;
  icon?: React.ReactNode;
  children: React.ReactNode;
  className?: string;
}

const SidebarAccordion = ({
  title,
  icon,
  children,
  className,
}: SidebarAccordionProps) => {
  return (
    <Accordion type="single" collapsible className={cn("w-full", className)}>
      <AccordionItem value={title} className="border-none">
        <AccordionTrigger className="hover:no-underline py-2 px-3">
          <div className="flex items-center gap-2">
            {icon}
            <span className="text-sm font-normal">{title}</span>
          </div>
        </AccordionTrigger>
        <AccordionContent className="pt-1 pb-1">
          <div className="flex flex-col gap-1">{children}</div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

export default SidebarAccordion;
