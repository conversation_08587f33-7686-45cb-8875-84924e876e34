"use client";

import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

type DeletePaymentScheduleInput = {
  scheduleId: string;
};

export const useDeletePaymentSchedule = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ scheduleId }: DeletePaymentScheduleInput) => {
      const res = await api.delete(`/payment-schedule/delete/${scheduleId}`);
      return res.data;
    },
    onSuccess: () => {
      toast.success("Deleted payment schedule successfully");
      queryClient.invalidateQueries({
        queryKey: ["paymentSchedules"],
      });
    },
    onError: () => {
      toast.error("Failed to delete payment schedule");
    },
  });
};
