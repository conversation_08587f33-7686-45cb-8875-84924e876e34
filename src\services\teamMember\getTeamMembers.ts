import { useInfiniteQuery } from "@tanstack/react-query";
import api from "@/lib/api-client";
import { TeamMember } from "@/types/TeamMember";

type UseGetNotificationsParams = {
  search?: string;
  limit?: number;
};

type GetTeamMembers = UseGetNotificationsParams & {
  pageParam?: number;
};

const getTeamMembers = async ({
  search,
  pageParam,
  limit,
}: GetTeamMembers): Promise<{
  data: TeamMember[];
  totalCount: number;
  limit: number;
}> => {
  return await api.get(
    `/architect-team/get-members?page=${pageParam}&limit=${limit}&search=${search}`,
  );
};

const useGetTeamMembers = ({
  search,
  limit = 10,
}: UseGetNotificationsParams) => {
  return useInfiniteQuery({
    queryKey: ["team-members", search, limit],
    queryFn: ({ pageParam = 1 }) =>
      getTeamMembers({ search, pageParam, limit }),
    getNextPageParam: (lastPage, allPages) => {
      const nextPage = allPages.length + 1;
      return lastPage.totalCount > allPages.length * lastPage.limit
        ? nextPage
        : undefined;
    },
    initialPageParam: 1,
  });
};

export default useGetTeamMembers;
