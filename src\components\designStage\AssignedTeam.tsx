import Eye from "../icons/Eye";
import { AvatarGroup, AvatarMembers } from "../team/AvatarGroup";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Table,
  TableHeader,
  TableBody,
  TableHead,
  TableRow,
  TableCell,
} from "@/components/ui/table";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import ProfileIcon from "../icons/Profile";
import { AssignedMember } from "@/types/DesignStage";

type AssignedTeamProps = {
  team: AssignedMember[];
};

const AssignedTeam = ({ team }: AssignedTeamProps) => {
  return (
    <Dialog>
      <DialogTrigger>
        <AvatarGroup className="relative items-end">
          <AvatarMembers
            members={team.map(({ name, profileUrl }) => ({
              src: profileUrl,
              alt: name,
            }))}
          />
          <Eye />
        </AvatarGroup>
      </DialogTrigger>
      <DialogContent
        isCloseIconVisible
        className="text-neutrals-G900 gap-y-6 sm:max-w-[35.42%] p-0"
      >
        <DialogHeader className="space-y-1 mb-2 px-6 pt-6">
          <DialogTitle>Assigned Team</DialogTitle>
        </DialogHeader>
        <div className="px-2.5 pb-2.5">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="basis-1/3">Architects</TableHead>
                <TableHead className="basis-1/3">Role</TableHead>
                <TableHead className="basis-1/3">Hours</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {team.map((member) => (
                <TableRow key={member._id} className="hover:bg-gray-50">
                  <TableCell className="flex items-center space-y-0 gap-x-2">
                    <Avatar className="size-7 outline outline-[#F8FCFA] outline-[1.5px]">
                      <AvatarImage
                        src={member.profileUrl || undefined}
                        alt={member.name}
                      />
                      <AvatarFallback>
                        <ProfileIcon className="size-3/4" />
                      </AvatarFallback>
                    </Avatar>
                    <span>{member.name}</span>
                  </TableCell>
                  <TableCell>{member.role}</TableCell>
                  <TableCell>{member.hours}</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AssignedTeam;
