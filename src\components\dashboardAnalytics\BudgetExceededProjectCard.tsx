import React from "react";
import { MapPin } from "lucide-react";
import { cn, formatDateForDueDate, formatDateRange } from "@/lib/utils";
import Users from "../icons/Users";
import Calendar from "../icons/Calendar";
import StageIcon from "../icons/Stage";

type Project = {
  projectName: string;
  id: string;
  location: string;
  type: string;
  stages: number;
  architects: number;
  dateRange: string;
  dueDate: string;
  isDelayed: boolean;
  delayDays?: number;
};

// Placeholder data until API is ready
const PLACEHOLDER_DATA = [
  {
    id: "010111",
    projectName: "Swimming Pool",
    location: "Ernakulam",
    type: "Commercial",
    stages: 15,
    architects: 15,
    dateRange: "11 Sep - 15 Oct",
    dueDate: "15 Oct",
    isDelayed: true,
    delayDays: 4,
  },
  {
    id: "010112",
    projectName: "Swimming Pool",
    location: "Ernakulam",
    type: "Commercial",
    stages: 15,
    architects: 15,
    dateRange: "11 Sep - 15 Oct",
    dueDate: "15 Oct",
    isDelayed: false,
  },
  {
    id: "010112",
    projectName: "Swimming Pool",
    location: "Ernakulam",
    type: "Commercial",
    stages: 15,
    architects: 15,
    dateRange: "11 Sep - 15 Oct",
    dueDate: "15 Oct",
    isDelayed: false,
  },
];

const BudgetExceededCard = ({ project }: { project: any }) => {
  return (
    <div className="px-5 py-[18px] rounded-xl border border-neutrals-G40 bg-white flex flex-col gap-6">
      <div className="space-y-2">
        <h6 className="text-neutrals-G900 font-semibold text-xl">
          {project?.name}
        </h6>
        <div className="flex gap-1 flex-wrap">
          <div className="px-1 py-1 flex items-center rounded border border-neutrals-G50 bg-neutrals-G30 text-xs text-neutrals-G600">
            #ID {project?.projectId}
          </div>
          <div className="px-1 py-1 flex items-center rounded border border-neutrals-G50 bg-neutrals-G30 text-xs text-neutrals-G600 gap-1">
            <MapPin className="size-3.5" /> {project?.location}
          </div>
          <div className="px-1 py-1 flex items-center rounded border border-neutrals-G50 bg-neutrals-G30 text-xs text-neutrals-G600">
            {project?.projectType || ""}
          </div>
        </div>
      </div>

      <div className="flex justify-between items-center">
        <div className="flex items-center gap-1 text-sm text-neutrals-G800">
          <StageIcon className="text-neutrals-G100 size-5" />
          <span className="font-bold">{project?.stageCount}</span> Stages
        </div>
        <div className="flex items-center gap-1 text-sm text-neutrals-G800">
          <Users className="text-neutrals-G100 size-5" />
          <span className="font-bold">{project?.workerCount}</span> Architects
        </div>
        <div className="flex items-center gap-1 text-sm text-neutrals-G800">
          <Calendar className="text-neutrals-G100 size-5" />
          <span>{formatDateRange(project?.startDate, project?.endDate)}</span>
        </div>
      </div>

      <div className="space-y-1">
        <div className="flex justify-between items-center">
          <div className="text-xs text-neutrals-G600">Design Progress</div>
          <div className="flex items-center gap-1.5">
            <span className="text-xs text-neutrals-G600">
              {formatDateForDueDate(project?.endDate)}
            </span>
            <div
              className={cn(
                "px-1 py-0.5 rounded text-[10px] font-medium",
                project?.delayDays > 0
                  ? "bg-yellow-50 text-yellow-600"
                  : "bg-green-50 text-green-600",
              )}
            >
              {project?.delayDays > 0
                ? `${project?.delayDays} DAYS BEHIND`
                : "ON TIME"}
            </div>
          </div>
        </div>
        <div
          className={cn(
            "w-full h-1 rounded-full",
            project.delayDays > 0 ? "bg-yellow-500" : "bg-green-500",
          )}
        />
      </div>
    </div>
  );
};

const BudgetExceededContent = ({ data }: any) => {
  return (
    <div className="grid grid-cols-2  gap-4">
      {data?.map((project: any, index: number) => (
        <BudgetExceededCard key={index} project={project} />
      ))}
    </div>
  );
};

export default BudgetExceededContent;
