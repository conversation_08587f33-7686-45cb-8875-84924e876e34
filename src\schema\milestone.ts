import { z } from "zod";
import { checkIsStartDateLowerThanOrEqualsToEndDate } from "@/lib/utils";
import { workTypeSchema } from "./workType";

export const milestoneSchema = z
  .object({
    milestoneTemplateId: z.string().optional(),
    milestoneName: z.string().optional(),
    isCustom: z.boolean().default(false),
    startDate: z.date().optional(),
    endDate: z.date().optional(),
    duration: z.number().nullable().optional(),
    worktypes: z.array(workTypeSchema).optional(),
    // paymentDate: z.date({ required_error: "" }),
  })
  .refine(
    (schema) => {
      if (schema.isCustom) {
        return !!schema.milestoneName;
      }
      return !!schema.milestoneTemplateId;
    },
    {
      message: "",
      path: ["milestoneTemplateId"],
    },
  )
  .refine(
    (data) => {
      if (data.endDate) {
        return !!data.startDate;
      }
      return true;
    },
    {
      message: "Start date is required when end date is provided",
      path: ["startDate"],
    },
  )
  .refine(
    (data) => {
      if (data.startDate) {
        return !!data.endDate;
      }
      return true;
    },
    {
      message: "End date is required when start date is provided",
      path: ["endDate"],
    },
  )
  .refine(
    (data) => {
      if (!data.startDate || !data.endDate) {
        return true;
      }
      return checkIsStartDateLowerThanOrEqualsToEndDate(
        new Date(data.startDate),
        new Date(data.endDate),
      );
    },
    {
      message: "End date cannot be lower than start date",
      path: ["endDate"],
    },
  )
  .superRefine((data, ctx) => {
    if (!data.worktypes || !data.startDate || !data.endDate) {
      return true;
    }

    data.worktypes.forEach((worktype, index) => {
      if (worktype.startDate && worktype.startDate < data.startDate!) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message:
            "Worktype start date cannot be earlier than milestone start date",
          path: ["worktypes", index, "startDate"],
        });
      }

      if (worktype.endDate && worktype.endDate > data.endDate!) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: "Worktype end date cannot be later than milestone end date",
          path: ["worktypes", index, "endDate"],
        });
      }
    });
  });
// .refine(
//   (data) => {
//     if (!data.worktypes || !data.startDate || !data.endDate) {
//       return true;
//     }

//     return data.worktypes.every((worktype) =>
//       checkWorktypeDatesWithinMilestone(
//         worktype.startDate,
//         worktype.endDate,
//         data.startDate,
//         data.endDate,
//       ),
//     );
//   },
//   {
//     message: "Worktype dates must be within milestone date range",
//     path: ["worktypes"],
//   },
// );
