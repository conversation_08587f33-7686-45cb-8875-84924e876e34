"use client";

import { useParams } from "next/navigation";

import AddNewChecklist from "@/components/qualityChecklist/AddNewChecklist";
import QualityChecklistList from "@/components/qualityChecklist/QualityChecklistList";

const QualityChecklistPage = () => {
  const { id } = useParams() as { id: string };

  return (
    <div className="flex flex-col gap-y-6">
      <div className="flex justify-between items-center">
        <div className="space-y-1.5 text-neutrals-G800">
          <h4 className="font-semibold">Quality Checklist</h4>
          <p className="text-sm">A description about checklist</p>
        </div>
        <AddNewChecklist projectId={id} />
      </div>
      <div className="flex-1 space-y-2">
        <QualityChecklistList />
      </div>
    </div>
  );
};

export default QualityChecklistPage;
