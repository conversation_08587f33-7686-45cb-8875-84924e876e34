import React from "react";

const ProductivityTrackerIcon: React.FC<{ className?: string }> = ({
  className,
}) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
    className={className}
  >
    <g clipPath="url(#clip0_261_365)">
      <path
        d="M2.8125 11.5509C3.6675 10.5609 4.81987 9.5356 5.90625 9.76285C7.2945 10.0535 7.59675 12.1861 8.4975 12.1336C9.7755 12.0586 10.1674 7.71048 11.5714 7.65348C12.1099 7.63173 12.2955 8.26135 12.9375 8.27635C13.8735 8.2981 14.6636 6.98635 15.1875 5.86548"
        stroke="currentColor"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9 17.0625C13.4528 17.0625 17.0625 13.4528 17.0625 9C17.0625 4.5472 13.4528 0.9375 9 0.9375C4.5472 0.9375 0.9375 4.5472 0.9375 9C0.9375 13.4528 4.5472 17.0625 9 17.0625Z"
        stroke="currentColor"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_261_365">
        <rect width="18" height="18" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export default ProductivityTrackerIcon;
