import React, { useState } from "react";
import {
  <PERSON>ert<PERSON><PERSON>og,
  AlertDialog<PERSON><PERSON><PERSON>,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alertDialog";
import DeleteIcon from "../icons/Delete";
import { useDeleteSiteDocument } from "../../services/project/sitedocs-hooks";

type DeleteDocumentDialogProps = {
  isOpen: boolean;
  onClose: () => void;
  documentId: string;
  profileId: string;
  fileName?: string;
  category?: string;
};

const DeleteDocumentDialog: React.FC<DeleteDocumentDialogProps> = ({
  documentId,
  profileId,
  fileName = "Untitled",
  category = "document",
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const { mutate: deleteSiteDocument, isPending } =
    useDeleteSiteDocument(profileId);

  const handleDelete = () => {
    deleteSiteDocument(documentId, {
      onSuccess: () => {
        setIsOpen(false);
      },
    });
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger>
        <DeleteIcon />
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Remove file?</AlertDialogTitle>
          <AlertDialogDescription>
            Doing this will remove the file{" "}
            <span className="text-neutrals-G900 font-medium ">{fileName}</span>{" "}
            from {category}.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction onClick={handleDelete} disabled={isPending}>
            Yes, Remove
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DeleteDocumentDialog;
