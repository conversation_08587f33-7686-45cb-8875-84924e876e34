import React from "react";
import Image from "next/image";

const ErrorPage404Image = () => {
  return (
    <div className="">
      <div className="bg-white sticky top-0 z-50 shrink-0 px-10 h-16 flex justify-between items-center  shadow-custom"></div>
      <div className="flex flex-col items-center justify-center overflow-auto scrollbar-hide bg-white  p-24">
        <div className="w-[400px] flex flex-col items-center justify-start gap-7  ">
          <Image
            src="/404.svg"
            alt="404 Error"
            width={400}
            height={225}
            className="w-[317px] h-[225px] object-contain"
          />
          <div className="w-full flex flex-col items-center justify-start  gap-2">
            <h1 className="w-full text-center text-neutrals-G900 text-2xl font-medium">
              Uh-oh! Page Not Found
            </h1>
            <p className="w-full text-center">
              <span className="text-name-title text-base font-medium">
                It seems this page is missing.{" "}
              </span>
              <a href="/" className="text-primary text-base font-bold">
                Go to home
              </a>
              <span className="text-name-title text-base font-medium">
                {" "}
                instead
              </span>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ErrorPage404Image;
