"use client";

import { useState, useEffect, useCallback } from "react";
import { useParams, useSearchParams } from "next/navigation";
import { z } from "zod";
import Chat from "@/components/chat/Chat";
import Loader from "@/components/ui/loader";
import ErrorText from "@/components/ui/errortext";
import { chatSchema } from "@/schema/chat";
import { socket } from "@/lib/socket";
import useGetUserWithToken from "@/services/mobile/getUserWithToken";
import useGetMessagesWithToken from "@/services/mobile/getMessagesWithToken";

const ConstructionDiscussionsMobile = () => {
  const params = useParams();
  const searchParams = useSearchParams();
  const groupId = params.groupId as string;
  const token = searchParams.get("token");

  const { data: userInfo, isPending: isPendingUserInfo } = useGetUserWithToken(
    token ?? "",
  );
  const { data: msgData, isPending: isPendingMsgData } =
    useGetMessagesWithToken(groupId ?? "", token ?? "");

  const [messages, setMessages] = useState<any[]>([]);
  const [isSocketConnected, setIsSocketConnected] = useState(false);

  useEffect(() => {
    if (!groupId) return;
    if (!msgData?.data?.messages) return;

    console.log("Fetched initial messages:", msgData.data.messages);
    setMessages(msgData.data.messages);
  }, [groupId, msgData]);

  useEffect(() => {
    if (!token) return;

    console.log("Connecting socket...");
    socket.auth = { token };
    socket.connect();

    const onConnect = () => {
      console.log("Socket successfully connected!");
      setIsSocketConnected(true);
    };

    const onDisconnect = () => {
      console.log("Socket disconnected");
      setIsSocketConnected(false);
    };

    const handleNewMessage = (newMessage: any) => {
      console.log("New message received from socket:", newMessage);

      if (newMessage.groupId !== groupId) return;

      setMessages((prev) => [...prev, newMessage]);
    };
    socket.on("connect", onConnect);
    socket.on("connect_error", (error) => console.log(error, "socket error"));
    socket.on("disconnect", onDisconnect);
    socket.on("newMessage", handleNewMessage);

    return () => {
      socket.off("connect", onConnect);
      socket.off("disconnect", onDisconnect);
      socket.off("newMessage", handleNewMessage);
      socket.disconnect();
      console.log("Socket disconnected in cleanup");
    };
  }, [groupId, token]);

  const handleSubmit = useCallback(
    (values: z.infer<typeof chatSchema>) => {
      console.log("handleSubmit triggered");

      if (!userInfo?.data) {
        console.error("No user found");
        return;
      }

      if (!groupId) {
        console.error("No groupId found");
        return;
      }

      if (!isSocketConnected) {
        console.error("Socket is NOT connected yet. Please wait.");
        return;
      }

      const newMessage: any = {
        senderId: userInfo.data?._id,
        text: values.message,
        imageUrl: values?.image || "",
        timestamp: new Date().toISOString(),
        readBy: "",
        sender: { name: userInfo.data?.name },
        groupId: groupId,
      };

      console.log("Sending new message:", newMessage);

      socket.emit(
        "createMessage",
        {
          ...values,
          groupId,
        },
        // (response: any) => {
        //   console.log("Server response to createMessage:", response);
        // },
      );

      setMessages((prev) => [...prev, newMessage]);
    },
    [userInfo, groupId, isSocketConnected],
  );

  if (!groupId) {
    console.error("Construction discussion group id not found");
    return <ErrorText entity="construction discussion group id" />;
  }

  if (isPendingUserInfo || isPendingMsgData) {
    return <Loader />;
  }

  console.log("Rendered messages:", messages);

  return (
    <div className="flex h-full flex-col gap-y-3 px-4">
      {/* <div className="flex items-center py-3.5 border-b border-neutrals-G40">
        <div className="space-y-1 text-neutrals-G900">
          <h4 className="font-semibold">Construction Discussions</h4>
          <p className="text-xs text-neutrals-G300">
            Stay updated with the latest discussions
          </p>
        </div>
      </div> */}

      <Chat
        chatId={groupId}
        messages={messages}
        className="flex-1 overflow-y-auto scrollbar-hide border border-neutrals-G40 rounded-xl bg-gradient-to-b from-white to-[#F3F8FF] min-h-[85vh] max-h-[85vh]"
      >
        <Chat.FloatingDate />
        <Chat.Messages user={userInfo?.data} />
        <Chat.Input
          onFormSubmit={handleSubmit}
          className="border border-neutrals-G40 rounded-xl"
        />
      </Chat>
    </div>
  );
};

export default ConstructionDiscussionsMobile;
