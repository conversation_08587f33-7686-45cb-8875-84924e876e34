"use client";
import * as React from "react";
import { cn } from "@/lib/utils";

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export type InputProps = React.InputHTMLAttributes<HTMLInputElement>;

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        // disable 'e' on number inputs
        onKeyDown={(event) => {
          if (type === "number" && event.key === "e") event.preventDefault();
        }}
        // disable number scroll wheel change
        onWheel={(e) => {
          if (type === "number") e.currentTarget.blur();
        }}
        className={cn(
          "flex h-10 w-full rounded-lg border border-input bg-background px-3 py-2.5 text-neutrals-G800 text-base font-medium shadow-input ring-offset-background placeholder:text-neutrals-G100  file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground focus-visible:outline-none disabled:cursor-not-allowed  disabled:text-neutrals-G100",
          className,
        )}
        ref={ref}
        {...props}
      />
    );
  },
);

Input.displayName = "Input";

export { Input };
