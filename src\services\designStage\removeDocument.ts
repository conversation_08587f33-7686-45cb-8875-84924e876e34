import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import api from "@/lib/api-client";

type RemoveDocument = {
  stageId: string;
  documentId: string;
  isFromHistory?: string;
};

const removeDocument = async ({
  stageId,
  documentId,
  isFromHistory,
}: RemoveDocument) => {
  const response = await api({
    url: `/design-stage/remove-document/${stageId}/${documentId}`,
    method: "DELETE",
    params: isFromHistory ? { isFromHistory } : undefined,
  });

  return response.data;
};

const useRemoveDocument = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: removeDocument,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["design-stage"] });
      queryClient.invalidateQueries({
        queryKey: ["design-stage-document-history"],
      });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message ||
          "Failed to remove document from design stage",
      );
    },
  });
};

export default useRemoveDocument;
