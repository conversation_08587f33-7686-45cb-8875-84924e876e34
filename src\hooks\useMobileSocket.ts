"use client";

import { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { socket } from "@/lib/socket";

export const useMobileSocket = () => {
  const searchParams = useSearchParams();
  const token = searchParams.get("token");

  useEffect(() => {
    function onConnect() {
      console.log("socket connected");
    }

    function onDisconnect() {
      console.log("socket disconnected");
    }

    function onConnectionError(err: Error) {
      console.error("socket connection failed", err.message);
    }

    socket.on("connect", onConnect);
    socket.on("disconnect", onDisconnect);
    socket.on("connect_error", onConnectionError);

    return () => {
      socket.off("connect", onConnect);
      socket.off("disconnect", onDisconnect);
      socket.off("connect_error", onConnectionError);
    };
  }, []);

  const connectSocket = () => {
    if (token) {
      (socket.auth as { token: string }).token = token;
      socket.connect();
    } else {
      console.error("No token found in URL for socket authentication");
    }
  };

  const disconnectSocket = () => {
    socket.disconnect();
  };

  return { connectSocket, disconnectSocket };
};
