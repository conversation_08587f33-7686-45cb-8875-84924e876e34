import { EllipsisVertical, History, MinusCircleIcon } from "lucide-react";
import { ChangeEvent, useRef, useState } from "react";
import { toast } from "sonner";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdownMenu";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogAction,
  AlertDialogCancel,
} from "@/components/ui/alertDialog";
import { Button } from "../ui/button";
import Replace from "../icons/Replace";
import DocumentHistory from "./DocumentHistory";
import useRemoveDocument from "@/services/designStage/removeDocument";
import { Document } from "@/types/DesignStage";
import {
  useGenerateS3Url,
  useUploadToS3,
} from "@/services/project/sitedocs-hooks";
import useReplaceDocumentToDesignStage from "@/services/designStage/replaceDocumentInDesignStage";

type DocumentActionDropdownMenuProps = {
  projectId: string;
  stageId: string;
  document: Document;
};

const DocumentActionDropdownMenu = ({
  projectId,
  stageId,
  document,
}: DocumentActionDropdownMenuProps) => {
  const [open, setOpen] = useState(false);

  const fileInputRef = useRef<HTMLInputElement>(null);

  const [isDocumntHistoryOpen, setIsDocumentHistoryOpen] = useState(false);
  const [isDeleteTeamMemberOpen, setIsDeleteTeamMemberOpen] = useState(false);

  const { mutate, isPending } = useRemoveDocument(() => {
    toast.success("Document removed successfully!");
    setIsDeleteTeamMemberOpen(false);
  });

  const { mutateAsync: generateS3Url } = useGenerateS3Url();
  const { mutateAsync: uploadToS3 } = useUploadToS3();
  const { mutateAsync: replaceDocument } = useReplaceDocumentToDesignStage(
    () => {
      toast.success("Document uploaded successfully!");
    },
  );

  const handleDocumnetHistoryClick = () => {
    setIsDocumentHistoryOpen(true);
    setOpen(false);
  };

  const handleRemove = () => {
    mutate({ stageId, documentId: document._id });
  };

  const handleReplace = async (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0] || null;
    if (!file) {
      return;
    }

    try {
      const signedUrl = await generateS3Url({
        fileName: file.name,
        projectId,
      });

      await uploadToS3({
        signedUrl,
        file: file,
      });

      const s3Link = signedUrl.split("?")[0];

      await replaceDocument({
        stageId: stageId,
        s3Link,
        documentId: document._id,
        filename: file.name,
      });
    } catch (error: any) {
      console.error("Error uploading document:", error);
      toast.error("Failed to upload document. Please try again.");
    } finally {
      event.target.value = "";
    }
  };

  return (
    <>
      <DropdownMenu open={open} onOpenChange={setOpen}>
        <DropdownMenuTrigger>
          <EllipsisVertical className="size-4" />
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuItem
            onClick={handleRemove}
            className="gap-x-1 [&_svg]:size-3.5"
          >
            <MinusCircleIcon />
            Remove
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={() => {
              fileInputRef.current?.click();
            }}
            className="gap-x-1 [&_svg]:size-3.5"
          >
            <Replace />
            Replace
          </DropdownMenuItem>
          <DropdownMenuItem
            onClick={handleDocumnetHistoryClick}
            className="gap-x-1 [&_svg]:size-3.5"
          >
            <History />
            Document History
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
      <DocumentHistory
        stageId={stageId}
        open={isDocumntHistoryOpen}
        onOpenChange={setIsDocumentHistoryOpen}
      />
      <AlertDialog
        open={isDeleteTeamMemberOpen}
        onOpenChange={setIsDeleteTeamMemberOpen}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              Do you wish to delete this team member?
            </AlertDialogDescription>
          </AlertDialogHeader>

          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction asChild>
              <Button onClick={() => console.log("first")} loading={isPending}>
                Delete
              </Button>
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
      <input
        ref={fileInputRef}
        type="file"
        accept=".pdf,.obj"
        onChange={handleReplace}
        className="hidden"
      />
    </>
  );
};

export default DocumentActionDropdownMenu;
