"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
// import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
// import { Plus } from "lucide-react";
import { toast } from "sonner";

import {
  She<PERSON>,
  <PERSON><PERSON><PERSON>onte<PERSON>,
  She<PERSON><PERSON>eader,
  Sheet<PERSON>itle,
} from "@/components/ui/sheet";
import { Button } from "../ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "../ui/input";
import { Textarea } from "../ui/textarea";
import { useGetPaginatedProjects } from "../../services/timesheet/getPaginatedProjects";
import { LoadingSpinner } from "../ui/loader";
import { cn } from "@/lib/utils";
// import { useGetPaginatedStagesForTimesheet } from "@/services/timesheet/getPaginatedStagesForTimesheet";
// import useAddTimesheet from "@/services/timesheet/addTimesheet";
import useUpdateTimesheet from "@/services/timesheet/editTimesheet";
import DeleteTime from "../icons/DeleteTime";
import useDeleteTimesheet from "@/services/timesheet/deleteTimeSheet";
import { useSheetFormState } from "@/hooks/useSheetFormState";

const timesheetSchema = z.object({
  projectId: z.string().optional(),
  stageId: z.string().optional(),
  hour: z.coerce.number({ message: "" }).min(0, { message: "" }),
  // description: z.string().min(1, { message: "" }),
  description: z.string().optional(),
});

const EditTimesheet = ({ open, setOpen, data }: any) => {
  console.log(data, "get time");

  const form = useForm<z.infer<typeof timesheetSchema>>({
    resolver: zodResolver(timesheetSchema),
    defaultValues: {
      projectId: data?.projectId,
      stageId: data?.stageId,
      hour: data?.hour,
      description: data?.description || "",
    },
  });

  //   const projectId = form.watch("projectId");

  const {
    data: projects,
    isPending: isPendingProjects,
    isFetchingNextPage: isFetchingNextPageProjects,
    ref: refProjects,
  } = useGetPaginatedProjects();

  const { mutate, isPending } = useUpdateTimesheet(() => {
    setOpen(false);
    toast.success("Timesheet added successfully!");
  });

  const { handleOpenChange, handleExplicitClose } = useSheetFormState({
    form,
    setOpen,
    preserveOnImplicitClose: true,
  });

  const onSubmit = async (values: z.infer<typeof timesheetSchema>) => {
    // mutate(values);
    const payload = {
      hour: values.hour,
      description: values.description || "",
    };

    mutate({ id: data._id, body: payload });
  };

  const { mutate: deleteTimesheet, isPending: isDeleting } = useDeleteTimesheet(
    () => {
      setOpen(false);
    },
  );

  return (
    <Sheet open={open} onOpenChange={handleOpenChange}>
      {/* <SheetTrigger asChild></SheetTrigger> */}
      <SheetContent
        className="max-w-[507px] flex flex-col gap-0"
        onExplicitClose={handleExplicitClose}
      >
        <SheetHeader>
          <SheetTitle>Edit Timesheet</SheetTitle>
        </SheetHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-1 flex-col gap-y-[37px] justify-between"
          >
            <div className="flex flex-1 flex-col gap-y-3">
              <FormField
                control={form.control}
                name="projectId"
                disabled
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Project</FormLabel>
                    <Select
                      disabled
                      onValueChange={field.onChange}
                      value={field.value || ""}
                    >
                      {" "}
                      <FormControl>
                        <SelectTrigger
                          loading={isPendingProjects}
                          className="data-[placeholder]:text-name-title text-name-title font-medium"
                        >
                          <SelectValue placeholder="Select">
                            {
                              projects.find(
                                (project) => project._id === field.value,
                              )?.name
                            }
                          </SelectValue>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {projects.length > 0 ? (
                          <>
                            {projects.map((project) => (
                              <SelectItem key={project._id} value={project._id}>
                                {project.name}
                              </SelectItem>
                            ))}
                            <SelectItem
                              ref={refProjects}
                              value={"none"}
                              disabled
                              className={cn(
                                "data-[disabled]:opacity-100 justify-center ",
                                isFetchingNextPageProjects ? "mb-1" : "h-0",
                              )}
                            >
                              {isFetchingNextPageProjects && (
                                <LoadingSpinner
                                  size={4}
                                  className="border-2 "
                                />
                              )}
                            </SelectItem>
                          </>
                        ) : (
                          <SelectItem value="none" disabled>
                            No projects found
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>

                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="stageId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Design Stage</FormLabel>
                    <FormControl>
                      <Input disabled value={data?.stageName || "N/A"} />
                    </FormControl>
                    {/* <Select
                      disabled
                      onValueChange={field.onChange}
                      value={field.value || ""}
                    >
                      <FormControl>
                        <SelectTrigger loading={isPendingStages}>
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent
                        hideScrollDownButton
                        position="popper"
                        className="max-h-52 max-w-[--radix-select-trigger-width]"
                      >
                        {stages.length > 0 ? (
                          <>
                            {stages.map((stage) => (
                              <SelectItem key={stage._id} value={stage._id}>
                                {stage.stageName}
                              </SelectItem>
                            ))}
                            <SelectItem
                              ref={refStages}
                              value={"none"}
                              disabled
                              className={cn(
                                "data-[disabled]:opacity-100 justify-center ",
                                isFetchingNextPageStages ? "mb-1" : "h-0",
                              )}
                            >
                              {isFetchingNextPageStages && (
                                <LoadingSpinner
                                  size={4}
                                  className="border-2 "
                                />
                              )}
                            </SelectItem>
                          </>
                        ) : (
                          <SelectItem value="none" disabled>
                            No stages found
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select> */}
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="hour"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Work hour</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem className="flex-1 mt-1 flex gap-y-2 flex-col">
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea className="resize-none flex-1" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="flex items-center justify-between">
              <Button
                type="button"
                disabled={isDeleting}
                onClick={() => deleteTimesheet(data?._id)}
                className="text-[#CC0000] text-[14px] font-[600] flex items-center gap-[8px] bg-white rounded-[8px] border border-[#CC0000] px-[16px] hover:bg-red-200"
              >
                <DeleteTime />
                {isDeleting ? "Deleting..." : "Delete"}
              </Button>
              <Button className="ml-auto px-4 py-3" loading={isPending}>
                Log work
              </Button>
            </div>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  );
};

export default EditTimesheet;
