import { toast } from "sonner";

import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";

type MarkNotificationAsRead = {
  notificationId: string;
};

const markNotificationAsRead = async ({
  notificationId,
}: MarkNotificationAsRead) => {
  const response = await api({
    url: `/notifications/${notificationId}`,
    method: "PUT",
  });
  return response.data;
};

const useMarkNotificationAsRead = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: markNotificationAsRead,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["notification-count"] });
      queryClient.invalidateQueries({ queryKey: ["notifications"] });
      if (onSuccess) onSuccess();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to mark notification as read",
      );
    },
  });
};

export default useMarkNotificationAsRead;
