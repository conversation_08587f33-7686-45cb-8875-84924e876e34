import React from "react";
import Link from "next/link";
import { MapPin } from "lucide-react";
import Image from "next/image";

import { ProjectData } from "@/types/Project";
import ProfileIcon from "../icons/Profile";
import Whatsapp from "../icons/Whatsapp";
import CompletionStatus from "../ui/completionStatus";
import { getIsAdminFromCookies, getIsTeamMemberFromCookies } from "@/lib/utils";

type ProjectCardProps = ProjectData;

const ProjectCard: React.FC<ProjectCardProps> = ({
  _id,
  projectId,
  status,
  name,
  location,
  clientName,
  clientWhatsAppNo,
  contractorOrg,
  contractorOwner,
  projectScope,
}) => {
  const isTeam = getIsTeamMemberFromCookies();
  const isAdmin = getIsAdminFromCookies();

  const href =
    !isTeam || isAdmin
      ? `/projects/${_id}/analytics`
      : `/projects/${_id}/design-stage`;

  const renderProjectScopeIcons = () => {
    if (!projectScope) return null;

    const scope = projectScope.toLowerCase();
    const iconSize = 20;
    const iconClass = "object-contain";

    if (scope === "both") {
      return (
        <div className="flex gap-1">
          <Image
            src="/design.png"
            alt="Design"
            width={iconSize}
            height={iconSize}
            className={iconClass}
          />
          <Image
            src="/construction.png"
            alt="Construction"
            width={iconSize}
            height={iconSize}
            className={iconClass}
          />
        </div>
      );
    } else if (scope === "design") {
      return (
        <Image
          src="/design.png"
          alt="Design"
          width={iconSize}
          height={iconSize}
          className={iconClass}
        />
      );
    } else if (scope === "construction") {
      return (
        <Image
          src="/construction.png"
          alt="Construction"
          width={iconSize}
          height={iconSize}
          className={iconClass}
        />
      );
    }

    return null;
  };

  return (
    <Link href={href} passHref>
      <div className="cursor-pointer bg-white rounded-xl flex flex-col justify-start overflow-hidden border border-l-neutrals-G40">
        <div className="bg-primary-blue-B30  px-6 pt-5  pb-[23px] flex justify-between items-start gap-3 ">
          <div className="space-y-1 flex-1">
            <h3 className="text-neutrals-G900 text-xl font-semibold">{name}</h3>
            <div className="flex gap-1 text-neutrals-G600 text-xs">
              <div className="p-1 rounded border border-neutrals-G50 bg-neutrals-G30">
                #ID {projectId}
              </div>
              <div className="p-1 flex rounded border border-neutrals-G50 bg-neutrals-G30">
                <MapPin className="size-3.5 mr-1" />
                {location}
              </div>
            </div>
          </div>
          <div className="flex flex-col items-end gap-2">
            {renderProjectScopeIcons()}
            <CompletionStatus status={status} />
          </div>
        </div>
        <div className="px-6 pt-2 pb-5 grid grid-cols-2 gap-x-5 ">
          <div className="space-y-2">
            <p className="text-[0.625rem] text-neutrals-G600">CLIENT</p>
            <div className="space-y-1.5 text-sm font-medium">
              <div className="flex gap-1">
                <ProfileIcon className="text-neutrals-G100" />
                {clientName}
              </div>
              <div className="flex gap-1">
                <Whatsapp />
                {clientWhatsAppNo}
              </div>
            </div>
          </div>
          {/* <div className="space-y-2">
            <p className="text-[0.625rem] text-neutrals-G600">CONTRACTOR</p>
            <div className="space-y-1.5 text-sm font-medium">
              <div className="flex gap-1">
                <ProfileIcon className="text-neutrals-G100" />
                {contractorOrg?.name || "__"}
              </div>
              <div className="flex gap-1">
                <Whatsapp />
                {contractorOwner?.phone || "__"}
              </div>
            </div>
          </div> */}
        </div>
      </div>
    </Link>
  );
};

export default ProjectCard;
