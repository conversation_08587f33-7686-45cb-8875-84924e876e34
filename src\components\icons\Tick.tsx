function Tick({ className }: { className?: string }) {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="16"
      height="16"
      fill="none"
      viewBox="0 0 16 16"
      className={className}
    >
      <g clipPath="url(#clip0_1640_3109)">
        <path
          fill="#4CAF50"
          d="M8 0c.734 0 1.443.094 2.125.281a7.564 7.564 0 011.906.813c.589.354 1.128.77 1.617 1.25.49.479.91 1.018 1.258 1.617a8.055 8.055 0 01.813 6.164 7.563 7.563 0 01-.813 1.906 8.76 8.76 0 01-1.25 1.617c-.479.49-1.018.91-1.617 1.258a8.055 8.055 0 01-6.164.813 7.568 7.568 0 01-1.906-.813 8.761 8.761 0 01-1.617-1.25 7.615 7.615 0 01-1.258-1.617A8.057 8.057 0 01.28 5.875a7.565 7.565 0 01.813-1.906 8.766 8.766 0 011.25-1.617A7.616 7.616 0 013.96 1.094 8.057 8.057 0 018.001 0zm4.711 5.352l-1.063-1.063L6.5 9.438 4.352 7.288 3.289 8.352l3.211 3.21 6.211-6.21z"
        ></path>
      </g>
      <defs>
        <clipPath id="clip0_1640_3109">
          <path fill="#fff" d="M0 0H16V16H0z"></path>
        </clipPath>
      </defs>
    </svg>
  );
}

export default Tick;
