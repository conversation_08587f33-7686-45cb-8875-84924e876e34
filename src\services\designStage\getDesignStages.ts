import { useQuery } from "@tanstack/react-query";

import api from "@/lib/api-client";
import { DesignStage } from "@/types/DesignStage";

export type GetDesignStages = {
  projectId: string;
  search?: string;
};

const getDesignStages = async ({
  projectId,
  search,
}: GetDesignStages): Promise<DesignStage[]> => {
  const response = await api.get(
    `/design-stage/get-stages/${projectId}?search=${search}`,
  );
  return response.data;
};

type UseGetDesignStages = GetDesignStages;

const useGetDesignStages = ({ projectId, search }: UseGetDesignStages) => {
  return useQuery({
    queryKey: ["design-stage", projectId, search],
    queryFn: async () => getDesignStages({ projectId, search }),
    enabled: !!projectId,
  });
};

export default useGetDesignStages;
