import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { Plus } from "lucide-react";

import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormItem,
  FormField,
  FormMessage,
  FormLabel,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { checklistSchema } from "@/schema/checklist";
import useGetMilestonesTemplate from "@/services/milestone/getMilestonesTemplate";
import useGetWorktypeTemplate from "@/services/milestone/getWorktypeTemplate";
import useGetQualityChecklistTemplates from "@/services/qualityChecklist/getQualityChecklistTemplates";
import useAddQualityChecklist from "@/services/qualityChecklist/addQualityChecklist";

type AddNewChecklistProps = {
  projectId: string;
};

const AddNewChecklist = ({ projectId }: AddNewChecklistProps) => {
  const [isAddNewChecklistOpen, setIsAddNewChecklistOpen] = useState(false);

  const form = useForm<z.infer<typeof checklistSchema>>({
    resolver: zodResolver(checklistSchema),
  });

  const milestoneTemplateId = form.watch("milestoneTemplateId");
  const worktypeId = form.watch("workType");

  const { data: milestonesTemplates, isPending: isPendingMilestonesTemplates } =
    useGetMilestonesTemplate();

  const { data: workTypeTemplate, isFetching: isPendingWorktypeTemplate } =
    useGetWorktypeTemplate({
      projectId,
      milestoneTemplateId,
    });

  const { data: checklistTemplate, isFetching: isPendingChecklistTemplates } =
    useGetQualityChecklistTemplates({
      worktypeId,
    });

  const onOpenChange = (open: boolean) => {
    if (!open) {
      form.reset();
    }
    setIsAddNewChecklistOpen(open);
  };

  const onSuccess = () => {
    onOpenChange(false);
    toast.success("Quality checklist created successfully");
  };

  const { mutate, isPending } = useAddQualityChecklist(onSuccess);

  const onSubmit = (data: z.infer<typeof checklistSchema>) => {
    mutate({ projectId, qualityCheckListTemplateId: data.checklist });
  };

  return (
    <Dialog open={isAddNewChecklistOpen} onOpenChange={onOpenChange}>
      <DialogTrigger asChild>
        <Button className="pl-4 py-2 pr-3 gap-x-2">
          Add Checklist <Plus className="size-[22px] stroke-[2.5px]" />
        </Button>
      </DialogTrigger>
      <DialogContent
        isCloseIconVisible
        className="gap-y-[30px] sm:max-w-[510px]"
      >
        <DialogTitle>Add a new checklist</DialogTitle>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col gap-y-6"
          >
            <div className="flex flex-col gap-y-3">
              <FormField
                control={form.control}
                name="milestoneTemplateId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Milestone</FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={field.onChange}
                        defaultValue={field.value}
                      >
                        <FormControl>
                          <SelectTrigger loading={isPendingMilestonesTemplates}>
                            <SelectValue placeholder="Choose" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {milestonesTemplates?.length === 0 ? (
                            <SelectItem disabled value="disabled">
                              No Items
                            </SelectItem>
                          ) : (
                            milestonesTemplates?.map(({ _id, name }) => (
                              <SelectItem key={_id} value={_id}>
                                {name}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="workType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Work type</FormLabel>
                    <FormControl>
                      <Select
                        onValueChange={field.onChange}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger loading={isPendingWorktypeTemplate}>
                            <SelectValue placeholder="Choose" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {workTypeTemplate?.length === 0 ? (
                            <SelectItem disabled value="disabled">
                              No items
                            </SelectItem>
                          ) : (
                            workTypeTemplate?.map((workType) => (
                              <SelectItem
                                key={workType.worktypeTemplateId._id}
                                value={workType.worktypeTemplateId._id}
                              >
                                {workType.worktypeTemplateId.worktype}
                              </SelectItem>
                            ))
                          )}
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="checklist"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Checklist</FormLabel>
                    <Select onValueChange={field.onChange} value={field.value}>
                      <FormControl>
                        <SelectTrigger loading={isPendingChecklistTemplates}>
                          <SelectValue placeholder="Choose" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {checklistTemplate?.length === 0 ? (
                          <SelectItem disabled value="disabled">
                            No items
                          </SelectItem>
                        ) : (
                          checklistTemplate?.map((checklist) => (
                            <SelectItem
                              key={checklist._id}
                              value={checklist._id}
                            >
                              {checklist.name}
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Button loading={isPending} type="submit" className="ml-auto px-4">
              Save
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AddNewChecklist;
