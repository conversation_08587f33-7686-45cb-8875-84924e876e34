import axios from "axios";
import { useQuery, UseQueryOptions } from "@tanstack/react-query";
import { CreatedMessage } from "@/types/Socket";

const getMessagesWithToken = async (
  groupId: string,
  token: string,
): Promise<any> => {
  const response = await axios.get(
    `${process.env.NEXT_PUBLIC_API_URL}/chat/get-messages/${groupId}`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    },
  );
  return response.data;
};

const useGetMessagesWithToken = (groupId: string, token: string) => {
  return useQuery({
    queryKey: ["messages", groupId, token],
    queryFn: () => getMessagesWithToken(groupId, token),
    enabled: !!groupId && !!token,
    staleTime: 0,
  });
};

export default useGetMessagesWithToken;
