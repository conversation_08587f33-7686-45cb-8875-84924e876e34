import api from "@/lib/api-client";
import { useQuery } from "@tanstack/react-query";

const getProjectAnalytics = async (id: string): Promise<any> => {
  try {
    const response = await api.get<any>(`/projects/${id}/analytics`);
    if (response.data) {
      return response.data;
    }
    return null;
  } catch (error) {
    console.error(`Error fetching analytics for project ID ${id}:`, error);
    return null;
  }
};

const useGetProjectAnalyticsComplete = (id: string) => {
  return useQuery({
    queryKey: ["project-analytics-complete", id],
    queryFn: () => getProjectAnalytics(id),
    enabled: !!id,
  });
};

export default useGetProjectAnalyticsComplete;
