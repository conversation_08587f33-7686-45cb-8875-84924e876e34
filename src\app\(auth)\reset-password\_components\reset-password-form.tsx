"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Image from "next/image";
import { useForm } from "react-hook-form";
import { useSearchParams, useRouter } from "next/navigation";
import { confirmPasswordReset } from "firebase/auth";
import { FirebaseError } from "firebase/app";
import { auth } from "../../../firebase";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Form,
  FormField,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Eye, EyeOff } from "lucide-react";
import { toast } from "sonner";

const resetPasswordSchema = z.object({
  newPassword: z.string().min(6, "New password must be at least 6 characters"),
  confirmPassword: z
    .string()
    .min(6, "Confirm password must be at least 6 characters"),
});

interface ResetPasswordForm {
  newPassword: string;
  confirmPassword: string;
}

export function ResetPasswordForm() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const oobCode = searchParams.get("oobCode");

  const form = useForm<ResetPasswordForm>({
    resolver: zodResolver(resetPasswordSchema),
    defaultValues: { newPassword: "", confirmPassword: "" },
  });

  const onSubmit = async (data: ResetPasswordForm) => {
    if (data.newPassword !== data.confirmPassword) {
      setErrorMessage("Passwords do not match. Please try again.");
      return;
    }
    try {
      if (!oobCode) {
        setErrorMessage("Invalid or missing password reset code.");
        return;
      }
      await confirmPasswordReset(auth, oobCode, data.newPassword);
      setErrorMessage(null);
      toast.success("Password reset successful. Please login again.");
      router.push("/login");
    } catch (error: unknown) {
      console.error(error);
      if (error instanceof FirebaseError) {
        if (error.code === "auth/expired-action-code") {
          setErrorMessage(
            "The reset link has expired. Please request a new one.",
          );
        } else {
          setErrorMessage("Failed to reset password. Please try again.");
        }
      } else {
        setErrorMessage("An unexpected error occurred. Please try again.");
      }
    }
  };

  return (
    <>
      <div className="hidden md:flex w-[491px] h-[683px] p-10 bg-white rounded-3xl shadow-[0px_4px_24px_0px_rgba(0,0,0,0.12)] flex-col justify-start items-center gap-10">
        <Image
          src="/projectsmatelogo.png"
          width={163}
          height={54}
          alt="Projectsmate Logo"
          className="w-[163px] h-[53px] object-contain"
        />

        <div className="self-stretch pt-[60px] flex-col justify-center items-center gap-8 flex">
          <div className="self-stretch flex-col justify-start items-start gap-6 flex">
            <div className="self-stretch flex-col justify-start items-start  flex">
              <h1 className="self-stretch text-center text-neutrals-G900 text-xl font-semibold   ">
                Create a New Password
              </h1>
              <p className="self-stretch text-center text-neutrals-G600 text-base font-normal   ">
                Choose a strong password to keep your account secure.
              </p>
            </div>

            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="flex flex-col gap-6 w-full"
              >
                <FormField
                  control={form.control}
                  name="newPassword"
                  render={({ field }) => (
                    <div className="self-stretch flex-col justify-start items-start gap-2 flex">
                      <label className="self-stretch text-Grey-Dark-Grey text-sm font-normal">
                        Enter New Password
                      </label>
                      <div className="relative w-full">
                        <FormControl>
                          <Input
                            type={showNewPassword ? "text" : "password"}
                            className="px-3 py-2.5 bg-neutrals-G30 rounded-[6px] shadow-input border text-name-title border-border-gray h-[44px] [&::-ms-reveal]:hidden [&::-webkit-inner-spin-button]:appearance-none"
                            {...field}
                          />
                        </FormControl>
                        {field.value && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                            onClick={() => setShowNewPassword(!showNewPassword)}
                          >
                            {showNewPassword ? (
                              <EyeOff className="h-4 w-4 text-neutrals-G100" />
                            ) : (
                              <Eye className="h-4 w-4 text-neutrals-G100" />
                            )}
                          </Button>
                        )}
                      </div>
                      <FormMessage />
                    </div>
                  )}
                />

                <FormField
                  control={form.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <div className="self-stretch flex-col justify-start items-start gap-2 flex">
                      <label className="self-stretch text-Grey-Dark-Grey text-sm font-normal">
                        Confirm New Password
                      </label>
                      <div className="relative w-full">
                        <FormControl>
                          <Input
                            type={showConfirmPassword ? "text" : "password"}
                            className="px-3 py-2.5 bg-neutrals-G30 rounded-[6px] shadow-input border text-name-title border-border-gray h-[44px] [&::-ms-reveal]:hidden [&::-webkit-inner-spin-button]:appearance-none"
                            {...field}
                          />
                        </FormControl>
                        {field.value && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                            onClick={() =>
                              setShowConfirmPassword(!showConfirmPassword)
                            }
                          >
                            {showConfirmPassword ? (
                              <EyeOff className="h-4 w-4 text-neutrals-G100" />
                            ) : (
                              <Eye className="h-4 w-4 text-neutrals-G100" />
                            )}
                          </Button>
                        )}
                      </div>
                      <FormMessage />
                    </div>
                  )}
                />

                {errorMessage && (
                  <div className="text-red-500 text-center">{errorMessage}</div>
                )}

                <Button
                  type="submit"
                  className="w-full px-4 py-3 bg-primary  rounded-[6px] text-white text-base font-medium h-[43px] mt-2 "
                >
                  Save & Proceed to login
                </Button>
              </form>
            </Form>
          </div>
        </div>
      </div>

      {/* Mobile View */}
      <div className="md:hidden fixed inset-0 bg-transparent">
        <div className="fixed inset-0 z-0">
          <Image
            src="/projectsmateMobileBackground.png"
            alt="Background"
            fill
            className="object-cover"
            priority
          />
          <div className="fixed top-0 left-0 right-0 h-[45vh] z-10 flex items-center justify-center">
            <Image
              src="/projectsmateRevLogo.png"
              alt="Projectsmate Logo"
              width={200}
              height={40}
              className="object-contain"
            />
          </div>
        </div>

        <div className="absolute bottom-0 left-0 right-0 min-h-[55vh] max-h-[80vh] overflow-y-auto bg-white rounded-t-[32px] shadow-lg">
          {/* Mobile form content */}
          <div className="px-8 py-10 space-y-6">
            <div className="space-y-2">
              <h1 className="text-[#002660] text-[28px] font-bold font-poppins">
                Create a New Password
              </h1>
              <p className="text-sidebar-gray text-base font-medium font-poppins">
                Choose a strong password to keep your account secure.
              </p>
            </div>

            <Form {...form}>
              <form onSubmit={form.handleSubmit(onSubmit)}>
                <FormField
                  control={form.control}
                  name="newPassword"
                  render={({ field }) => (
                    <div className="relative">
                      <FormControl>
                        <Input
                          type={showNewPassword ? "text" : "password"}
                          placeholder="Enter New Password"
                          className="h-12 pr-10 border-[#E7EEF7] rounded-md mb-3 [&::-ms-reveal]:hidden [&::-webkit-inner-spin-button]:appearance-none"
                          {...field}
                        />
                      </FormControl>
                      {field.value && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                          onClick={() => setShowNewPassword(!showNewPassword)}
                        >
                          {showNewPassword ? (
                            <EyeOff className="h-4 w-4 text-neutrals-G100" />
                          ) : (
                            <Eye className="h-4 w-4 text-neutrals-G100" />
                          )}
                        </Button>
                      )}
                    </div>
                  )}
                />

                <FormField
                  control={form.control}
                  name="confirmPassword"
                  render={({ field }) => (
                    <div className="relative">
                      <FormControl>
                        <Input
                          type={showConfirmPassword ? "text" : "password"}
                          placeholder="Confirm New Password"
                          className="h-12 pr-10 border-[#E7EEF7] rounded-md mb-10 [&::-ms-reveal]:hidden [&::-webkit-inner-spin-button]:appearance-none"
                          {...field}
                        />
                      </FormControl>
                      {field.value && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                          onClick={() =>
                            setShowConfirmPassword(!showConfirmPassword)
                          }
                        >
                          {showConfirmPassword ? (
                            <EyeOff className="h-4 w-4 text-neutrals-G100" />
                          ) : (
                            <Eye className="h-4 w-4 text-neutrals-G100" />
                          )}
                        </Button>
                      )}
                    </div>
                  )}
                />

                {errorMessage && (
                  <div className="mb-4 text-red-500 text-center">
                    {errorMessage}
                  </div>
                )}

                <Button variant="default" type="submit" className="w-full">
                  Create password
                </Button>
              </form>
            </Form>
          </div>
        </div>
      </div>
    </>
  );
}
