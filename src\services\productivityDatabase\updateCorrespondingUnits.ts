import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";

import api from "@/lib/api-client";
import { ProductivityDatabase } from "@/types/ProductivityDatabase";

type UpdateCorrespondingUnits = {
  projectId: string;
  worktypeId: string;
  value: string;
  unit: string;
  type: "min" | "max";
};

const updateCorrespondingUnits = async ({
  projectId,
  worktypeId,
  ...params
}: UpdateCorrespondingUnits): Promise<
  ProductivityDatabase["worktypes"][0]["standardUnits"]
> => {
  const response = await api({
    url: `/productivity-tracker/${projectId}/${worktypeId}/unit-conversion`,
    method: "GET",
    params,
  });
  return response.data;
};

const useUpdateCorrespondingUnits = (
  onSuccess?: (
    data: ProductivityDatabase["worktypes"][0]["standardUnits"],
    variables: UpdateCorrespondingUnits,
  ) => void,
  onError?: () => void,
) => {
  return useMutation({
    mutationFn: updateCorrespondingUnits,
    onSuccess,
    onError: (error: any) => {
      onError?.();
      toast.error(error?.response?.data?.message || "Failed to fetch units");
    },
  });
};

export default useUpdateCorrespondingUnits;
