"use client";

import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";

type UpdatePaymentScheduleInput = {
  scheduleId: string;
  scheduleName?: string;
  date?: string;
  stageId?: string;
  invoice?: string | null;
};

export const useUpdatePaymentSchedule = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({
      scheduleId,
      ...payload
    }: UpdatePaymentScheduleInput) => {
      const res = await api.put(
        `/payment-schedule/update/${scheduleId}`,
        payload,
      );
      return res.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["paymentSchedules"],
      });
    },
  });
};
