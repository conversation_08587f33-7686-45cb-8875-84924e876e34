import Image from "next/image";
import React from "react";

const PendingPaymentsCard = ({ data }: any) => {
  return (
    <div className="w-full min-w-[1056px] h-[89px] px-6 py-5 bg-white rounded-xl border border-neutrals-G40 justify-start items-start gap-4 inline-flex overflow-hidden">
      <div className="grow shrink basis-0 h-[49px] justify-between items-center flex">
        <div className="flex-col justify-start items-start gap-2 inline-flex">
          <div className="text-neutrals-G800 text-xl font-semibold">
            Pending payments
          </div>
          <div className="text-neutrals-G300 text-sm font-normal">
            These are pending actions in the payment schedules
          </div>
        </div>
        <div className=" w-[511px] justify-start items-start flex">
          <div className="w-[238px] h-[45px] justify-start items-center gap-3.5 flex">
            <div data-svg-wrapper>
              <Image
                src="/client_payments.svg"
                alt="Client Payments"
                width={45}
                height={45}
              />
            </div>
            <div className=" flex-col justify-start items-start gap-1 inline-flex">
              <div className="text-neutrals-G600 text-sm font-normal leading-[21px]">
                Client payments
              </div>
              <div className="justify-start items-center gap-1.5 inline-flex">
                <div className="text-neutrals-G600 text-base font-medium">
                  {data?.clientPayments || 0}
                </div>
              </div>
            </div>
          </div>
          <div className="h-[45px] justify-start items-center gap-3.5 flex">
            <div data-svg-wrapper>
              <Image
                src="/paymenttobeverified.svg"
                alt="Payments to be Verified"
                width={45}
                height={45}
              />
            </div>
            <div className="flex-col justify-start items-start gap-1 inline-flex">
              <div className="text-neutrals-G600 text-sm font-normal leading-[21px]">
                Payments to be verified
              </div>
              <div className="justify-start items-center gap-1.5 inline-flex">
                <div className="text-neutrals-G600 text-base font-medium">
                  {data?.paymentsToVerify || 0}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PendingPaymentsCard;
