import { toast } from "sonner";

import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";

type DeleteChecklist = {
  projectId: string;
  qualityChecklistId: string;
};

const deleteChecklist = async ({
  projectId,
  qualityChecklistId,
}: DeleteChecklist) => {
  const response = await api({
    url: `/quality-checklist/${projectId}/${qualityChecklistId}`,
    method: "DELETE",
  });
  return response.data;
};

const useDeleteChecklist = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteChecklist,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["quality-checklist"] });
      if (onSuccess) onSuccess();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to delete checklist",
      );
    },
  });
};

export default useDeleteChecklist;
