"use client";

import Image from "next/image";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { useEffect } from "react";
import Cookies from "js-cookie";

// Define subscription plans
const subscriptionPlans = [
  // ... your plan definitions
];

function SubscriptionPlan() {
  const router = useRouter();

  useEffect(() => {
    // Clear the newSignup flag when this page loads
    Cookies.remove("newSignup");
  }, []);

  const handleSelectPlan = (planName: string) => {
    // Here you would typically handle the plan selection
    // For now, just redirect to dashboard
    router.push("/dashboard");
  };

  return (
    <div className="flex flex-col items-center justify-center">
      <div className="flex flex-col gap-y-10 items-center justify-center mb-[52px]">
        <Image
          src="/projectsmatelogo.png"
          alt="Projectsmate Logo"
          width={163}
          height={54}
          className="object-contain"
        />
        <div className="space-y-2 text-center">
          <h3 className="text-neutrals-G900 font-semibold text-xl">
            Select a plan to continue
          </h3>
          <p className="text-neutrals-G600">
            Choose your preferred plan according to your requirement
          </p>
        </div>
      </div>
      <div className="flex items-start gap-x-6">
        {Array.from({ length: 3 }).map((_, index) => (
          <div
            key={index}
            className="shadow-[0px_4px_24px_0px_rgba(0,0,0,0.12)] rounded-3xl p-6 bg-white space-y-8 w-[368px]"
          >
            <div className="space-y-6">
              <div className="flex flex-col gap-y-8 items-start">
                <div className="bg-primary-blue-B40 rounded-[8px] py-1.5 px-2 font-medium text-sm text-neutrals-G900">
                  Free plan
                </div>
                <h4 className="text-neutrals-G900 font-bold text-2xl">$0</h4>
              </div>
              <p className="text-neutrals-G800 font-light">
                Access to all premium features including advanced analytics,{" "}
              </p>
              <div className="space-y-3 text-neutrals-G800">
                <p className="text-xl font-semibold">Plan Features:</p>
                <ul className="list-disc list-inside font-light">
                  <li>Unlimited projects</li>
                  <li>Unlimited projects</li>
                  <li>Unlimited projects</li>
                </ul>
              </div>
            </div>
            <Button
              className="w-full"
              onClick={() => handleSelectPlan("Free plan")}
            >
              Select Plan
            </Button>
          </div>
        ))}
      </div>
    </div>
  );
}

export default SubscriptionPlan;
