// import * as React from "react";
// import { DayPicker } from "react-day-picker";

// import { cn } from "@/lib/utils";
// import { buttonVariants } from "@/components/ui/button";

// export type CalendarProps = React.ComponentProps<typeof DayPicker>;

// function Calendar({
//   className,
//   classNames,
//   showOutsideDays = true,
//   ...props
// }: CalendarProps) {
//   return (
//     <DayPicker
//       showOutsideDays={showOutsideDays}
//       className={cn(
//         "p-3 bg-[#1E1C29]  border-[0.5px] border-[#2D2C35] rounded-[6px] shadow-lg",
//         className,
//       )}
//       captionLayout="dropdown-years"
//       classNames={{
//         months:
//           "flex flex-col sm:flex-row space-y-4 sm:gap-2 sm:pr-2 sm:space-y-4 text-white",
//         month: "  text-white",
//         caption_label: "text-sm hidden",
//         years_dropdown: "bg-[#1E1C29] px-2 py-1 cursor-pointer mb-2 max-h-10 ",
//         root: "",

//         nav: "space-x-1 flex items-center text-white",
//         weekdays: " grid grid-cols-7",
//         button_next:
//           "absolute  text-white right-4 top-4 bg-gray-600 rounded-[6px] p-1 hover:bg-white",
//         button_previous:
//           "absolute  text-white left-4 top-4 bg-gray-600 rounded-[6px] p-1 hover:bg-white",
//         month_caption: " pl-12",
//         day: cn(
//           buttonVariants({ variant: "ghost" }),
//           "h-9 w-9 p-0 font-normal aria-selected:opacity-100  ",
//         ),
//         day_button: "w-full h-full",
//         range_start: "text-[#18181B] bg-[#FAFAFA]",
//         range_end: " text-[#18181B] bg-[#FAFAFA]",
//         selected: "",
//         outside:
//           "day-outside text-muted-foreground opacity-50 text-white aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",
//         range_middle: "aria-selected:bg-[#27272A] aria-selected:text-white",
//         disabled: "text-muted-foreground opacity-50",
//         ...classNames,
//       }}
//       {...props}
//     />
//   );
// }
// Calendar.displayName = "Calendar";

// export { Calendar };

import * as React from "react";
import { DayPicker } from "react-day-picker";

import { cn } from "@/lib/utils";
import { buttonVariants } from "@/components/ui/button";

export type CalendarProps = React.ComponentProps<typeof DayPicker>;

function Calendar({
  className,
  classNames,
  showOutsideDays = true,
  ...props
}: CalendarProps) {
  return (
    <DayPicker
      showOutsideDays={showOutsideDays}
      className={cn(
        "p-3 bg-[#ffffff]  border-[0.5px] border-text-name-title rounded-[6px] ",
        className,
      )}
      captionLayout="dropdown-years"
      classNames={{
        months:
          "flex flex-col sm:flex-row space-y-4 sm:gap-2 sm:pr-2 sm:space-y-4 text-name-title",
        month: "text-name-title",
        caption_label: "text-sm hidden",
        years_dropdown: "bg-[#ffffff] px-2 py-1 cursor-pointer mb-2 max-h-10 ",
        root: "",

        nav: "space-x-1 flex items-center text-white",
        weekdays: " grid grid-cols-7",
        button_next:
          "absolute  text-white right-4 top-4 bg-white rounded-[6px] p-1 hover:bg-primary/20",
        button_previous:
          "absolute  text-white left-4 top-4 bg-white rounded-[6px] p-1 hover:bg-primary/20",
        month_caption: " pl-12",
        day: cn(
          buttonVariants({ variant: "ghost" }),
          "h-9 w-9 p-0 font-normal aria-selected:opacity-100 hover:bg-primary/20 ",
        ),
        day_button: "w-full h-full",
        range_start: "text-name-title bg-primary/20",
        range_end: "text-name-title bg-primary/20",
        selected: " text-name-title bg-primary/20",
        outside:
          "day-outside text-muted-foreground opacity-50 text-white aria-selected:bg-accent/50 aria-selected:text-muted-foreground aria-selected:opacity-30",
        range_middle:
          "aria-selected:bg-primary/20 aria-selected:text-name-title",
        disabled: "text-muted-foreground opacity-50",
        ...classNames,
      }}
      {...props}
    />
  );
}
Calendar.displayName = "Calendar";

export { Calendar };
