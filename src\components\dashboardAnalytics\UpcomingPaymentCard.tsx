import React from "react";
import { Card } from "@/components/ui/card";
import DarkFileIcon from "../icons/DarkFileIcon";
import NoDataToShow from "../ui/noDataToShow";

const PaymentCard = ({ payment }: { payment: any }) => {
  return (
    <Card className="w-full  min-w-[800px]  p-6 mb-4 border border-[#D7D7D7] rounded-xl  overflow-hidden">
      <div className="flex items-center justify-between w-full">
        <div className="flex-1 min-w-[140px] max-w-[200px] px-2 flex flex-col gap-2">
          <div className="text-xs text-neutrals-G600">Project name</div>
          <div className="text-sm font-semibold text-neutrals-G900">
            {payment?.projectName}
          </div>
        </div>

        <div className="flex-1 min-w-[140px] max-w-[200px] px-2 flex flex-col gap-2">
          <div className="text-xs text-neutrals-G600">Schedule name</div>
          <div className="text-sm font-semibold text-neutrals-G900">
            {payment?.scheduleName}
          </div>
        </div>

        <div className="flex-1 min-w-[140px] max-w-[200px] px-2 flex flex-col gap-2">
          <div className="text-xs text-neutrals-G600">Assigned stage</div>
          <div className="text-sm font-semibold text-neutrals-G900">
            {payment?.stageName}
          </div>
        </div>

        <div className="flex-1 min-w-[140px] max-w-[200px] px-2 flex flex-col gap-2">
          <div className="text-xs text-neutrals-G600">Documents uploaded</div>
          <div className="flex items-center gap-1 text-sm text-neutrals-G900">
            {payment?.documentsUploaded}
            <DarkFileIcon className="w-3.5 h-3.5" />
          </div>
        </div>

        <div className="flex-1 min-w-[140px] max-w-[200px] px-2 flex flex-col gap-2">
          <div className="text-xs text-neutrals-G600">Date</div>
          <div className="text-sm font-semibold text-neutrals-G900">
            {payment?.date}
          </div>
        </div>
      </div>
    </Card>
  );
};

const UpcomingPaymentsCard = ({ data }: any) => {
  return (
    <div className="w-full overflow-x-auto ">
      <div className="min-w-[800px]">
        {data?.length === 0 ? (
          // <NoDataToShow />
          <NoDataToShow className="mt-24" />
        ) : (
          data?.map((payment: any, index: number) => (
            <PaymentCard key={index} payment={payment} />
          ))
        )}
      </div>
    </div>
  );
};

export default UpcomingPaymentsCard;
