import { z } from "zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import api from "@/lib/api-client";
import { designStageSchema } from "@/schema/designStage";

type AddDesignStage = Omit<
  z.infer<typeof designStageSchema>,
  "startDate" | "endDate"
> & {
  startDate: string;
};

const addDesignStage = async (body: AddDesignStage) => {
  const response = await api({
    url: "/design-stage/add",
    method: "POST",
    data: body,
  });

  return response.data;
};

const useAddDesignStage = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: addDesignStage,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["design-stage"] });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to add design stage",
      );
    },
  });
};

export default useAddDesignStage;
