import { useMutation, useQueryClient } from "@tanstack/react-query";
import api from "@/lib/api-client";

interface UpdateProfileData {
  name: string;
  whatsappNo?: string;
  organisationName: string;
  tagline?: string;
  phone?: string;
  website?: string;
  location?: string;
  profileImage?: string | null;
}

const updateProfile = async (data: UpdateProfileData) => {
  const response = await api.put("/profile", data);
  return response.data;
};

const useUpdateProfile = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateProfile,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["user"] });
    },
  });
};

export default useUpdateProfile;
