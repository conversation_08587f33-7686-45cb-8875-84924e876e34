import { create } from "zustand";

import { designStageSlice, DesignStageSlice } from "./designStageSlice";
import {
  designDiscussionSlice,
  DesignDiscussionSlice,
} from "./designDiscussionSlice";
import {
  ConstructionDiscussionSlice,
  constructionDiscussionSlice,
} from "./constructionDiscussionSlice";

export const useChatStore = create<
  DesignStageSlice & DesignDiscussionSlice & ConstructionDiscussionSlice
>()((...a) => ({
  ...designStageSlice(...a),
  ...designDiscussionSlice(...a),
  ...constructionDiscussionSlice(...a),
}));
