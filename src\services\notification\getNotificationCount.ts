import { useQuery } from "@tanstack/react-query";

import api from "@/lib/api-client";
import { NotificationCount } from "@/types/Notifications";

// Unused function in redesigned app, might delete later.

const getNotitificationCount = async (): Promise<NotificationCount> => {
  const response = await api.get("/notifications/count");
  return response.data;
};

const useGetNotificationCount = () => {
  return useQuery({
    queryKey: ["notification-count"],
    queryFn: getNotitificationCount,
  });
};

export default useGetNotificationCount;
