import { cn } from "@/lib/utils";

const PageHeader = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <div
      className={cn(
        "bg-white sticky top-0 z-50 shrink-0 px-6 py-3.5 flex justify-between items-center border-b border-neutrals-G40",
        className,
      )}
    >
      {children}
    </div>
  );
};

const Heading = ({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) => {
  return (
    <h1 className={cn("text-neutrals-G900 font-semibold", className)}>
      {children}
    </h1>
  );
};
PageHeader.Heading = Heading;

const Description = ({ children }: { children: React.ReactNode }) => {
  return <p className="text-neutrals-G300 text-xs">{children}</p>;
};
PageHeader.Description = Description;

export default PageHeader;
