import { useState } from "react";

import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import EditWorkTypeForm from "./EditWorkTypeForm";
import { WorkType } from "@/types/Worktype";
import Edit from "@/components/icons/Edit";

type EditWorkTypeProps = {
  projectId: string;
  milestoneTemplateId: string;
  workType: WorkType;
};

const EditWorkType = ({
  projectId,
  workType,
  milestoneTemplateId,
}: EditWorkTypeProps) => {
  const [isEditWorkTypeDialogOpen, setIsEditWorkTypeDialogOpen] =
    useState(false);

  return (
    <>
      <button onClick={() => setIsEditWorkTypeDialogOpen(true)}>
        <Edit />
      </button>

      <Dialog
        open={isEditWorkTypeDialogOpen}
        onOpenChange={setIsEditWorkTypeDialogOpen}
      >
        <DialogContent isCloseIconVisible className="gap-y-10">
          <DialogTitle>Edit work type</DialogTitle>
          <EditWorkTypeForm
            projectId={projectId}
            workType={workType}
            milestoneTemplateId={milestoneTemplateId}
            onSuccess={() => setIsEditWorkTypeDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};

export default EditWorkType;
