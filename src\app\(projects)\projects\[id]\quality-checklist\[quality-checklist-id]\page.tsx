"use client";

import { useParams } from "next/navigation";
import Link from "next/link";
import { ChevronLeft } from "lucide-react";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import DeleteChecklist from "@/components/qualityChecklist/DeleteChecklist";
import AddNewItemToChecklist from "@/components/qualityChecklist/AddNewItemToChecklist";
import DeleteItemFromChecklist from "@/components/qualityChecklist/DeleteItemFromChecklist";
import useGetQualityChecklistById from "@/services/qualityChecklist/getQualityChecklistbyId";
import NoDataToShow from "@/components/ui/noDataToShow";
import Loader from "@/components/ui/loader";
import ErrorText from "@/components/ui/errortext";
import DeleteIcon from "@/components/icons/Delete";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTrigger,
} from "@/components/ui/dialog";
import Caution from "@/components/icons/Caution";

const QualityChecklistPage = () => {
  const params = useParams();
  const projectId = params.id as string;
  const checklistId = params["quality-checklist-id"] as string;

  const { data, isLoading, isError } = useGetQualityChecklistById(
    projectId,
    checklistId,
  );

  if (isLoading) return <Loader />;

  if (isError) return <ErrorText entity="quality checklist" />;

  if (!data)
    return (
      <div className="flex justify-center">
        <NoDataToShow />
      </div>
    );

  return (
    <div className="flex flex-col gap-y-6">
      <div className="flex items-center justify-between">
        <div className="space-y-2 text-neutrals-G800">
          <Link
            href={`/projects/${projectId}/quality-checklist`}
            className="text-sm flex items-center gap-x-1"
          >
            <ChevronLeft className="size-5" />
            {data.worktypeTemplateId.worktype}
          </Link>
          <h4 className="font-semibold text-xl">{data.name}</h4>
        </div>
        <DeleteChecklist projectId={projectId} checklistId={checklistId} />
      </div>

      <div className="flex-1">
        <div className="p-3 border border-border-gray1 rounded-xl">
          <Table>
            <TableHeader>
              <TableRow className="*:px-3.5 *:py-3 *:h-auto">
                <TableHead>SL</TableHead>
                <TableHead>Questions</TableHead>
                <TableHead>Answer</TableHead>
                <TableHead>Remarks</TableHead>
                <TableHead></TableHead>
              </TableRow>
            </TableHeader>

            <TableBody>
              {data.checklist.map((item, index) => (
                <TableRow
                  key={item._id}
                  className="*:px-3.5 *:py-3.5 *:font-medium"
                >
                  <TableCell>{index + 1}</TableCell>
                  <TableCell className="w-[40%] align-top">
                    {item.item}
                  </TableCell>
                  <TableCell>
                    {/* NOTE: "NA" is a valid value that is used in the table cell, so we cannot use default empty cell message "NA" here */}
                    {item.answer === ""
                      ? "--"
                      : item.answer === "na"
                        ? "N/A"
                        : item.answer}
                  </TableCell>
                  <TableCell className="w-[40%] align-top">
                    {item.remarks}
                  </TableCell>
                  <TableCell className="text-right">
                    {!item.isTemplate ? (
                      <DeleteItemFromChecklist
                        projectId={projectId}
                        checklistId={checklistId}
                        id={item._id}
                      />
                    ) : (
                      <Dialog>
                        <DialogTrigger asChild>
                          <button>
                            <DeleteIcon className="fill-neutrals-G600" />
                          </button>
                        </DialogTrigger>
                        <DialogContent
                          isCloseIconVisible={true}
                          className="text-center"
                        >
                          <DialogHeader className="items-center">
                            <Caution />
                          </DialogHeader>
                          <div className="space-y-3">
                            <h2 className="text-neutrals-G900 text-2xl font-medium">
                              Unable to Delete Checklist
                            </h2>
                            <p className="text-name-title">
                              This checklist cannot be deleted because it was
                              created by the system and not by you.
                            </p>
                          </div>
                        </DialogContent>
                      </Dialog>
                    )}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          <AddNewItemToChecklist
            projectId={projectId}
            qualityChecklistId={checklistId}
          />
        </div>
      </div>
    </div>
  );
};

export default QualityChecklistPage;
