import { toast } from "sonner";

import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";

const addItemToChecklist = async ({
  projectId,
  qualityChecklistId,
  question,
}: {
  projectId: string;
  qualityChecklistId: string;
  question: string;
}) => {
  const response = await api({
    url: `/quality-checklist/${projectId}/${qualityChecklistId}/question`,
    method: "POST",
    data: { question },
  });
  return response.data;
};

const useAddItemToChecklist = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: addItemToChecklist,
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: [
          "quality-checklist-by-id",
          variables.projectId,
          variables.qualityChecklistId,
        ],
      });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message ||
          "Failed to add item to quality checklist",
      );
    },
  });
};

export default useAddItemToChecklist;
