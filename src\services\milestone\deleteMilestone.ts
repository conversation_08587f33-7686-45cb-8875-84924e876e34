import { toast } from "sonner";

import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";

type DeleteMilestone = {
  projectId: string;
  milestoneId: string;
};

const deleteMilestone = async ({ projectId, milestoneId }: DeleteMilestone) => {
  const response = await api({
    url: `/milestones/${projectId}/${milestoneId}`,
    method: "DELETE",
  });
  return response.data;
};

const useDeleteMilestone = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: deleteMilestone,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["milestones"] });
      if (onSuccess) onSuccess();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to delete milestone",
      );
    },
  });
};

export default useDeleteMilestone;
