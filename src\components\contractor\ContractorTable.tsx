import React, { useEffect, useCallback, useRef } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { EditContractor } from "./EditContractor";
import { DeleteContractor } from "./DeleteContractor";
import { Contractor } from "@/types/Contractor";
import Loader from "../ui/loader";
interface ContractorsTableProps {
  data: Contractor[];
  isLoading: boolean;
  isFetchingNextPage: boolean;
  hasNextPage: boolean;
  fetchNextPage: () => void;
}

const ContractorsTable: React.FC<ContractorsTableProps> = ({
  data,
  isLoading,
  hasNextPage,
  isFetchingNextPage,
  fetchNextPage,
}) => {
  const observerTarget = useRef<HTMLDivElement>(null);

  const handleObserver = useCallback(
    (entries: IntersectionObserverEntry[]) => {
      const [entry] = entries;
      if (entry.isIntersecting && !isFetchingNextPage && hasNextPage) {
        fetchNextPage();
      }
    },
    [isFetchingNextPage, fetchNextPage, hasNextPage],
  );

  useEffect(() => {
    const element = observerTarget.current;
    if (!element) return;

    const observer = new IntersectionObserver(handleObserver, {
      root: null,
      rootMargin: "20px",
      threshold: 0.1,
    });

    observer.observe(element);

    return () => observer.disconnect();
  }, [handleObserver]);

  if (isLoading && !isFetchingNextPage) {
    return <Loader />;
  }

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[158px]">Contractor ID</TableHead>
            <TableHead className="w-[252px]">Contractor name</TableHead>
            <TableHead className="w-[252px]">Contractor Email</TableHead>
            <TableHead className="w-[335px]">Org name</TableHead>
            {/* <TableHead className="w-[99px] text-center">Actions</TableHead> */}
          </TableRow>
        </TableHeader>
        <TableBody>
          {data?.map((contractor: Contractor) => (
            <TableRow key={contractor._id}>
              <TableCell>{contractor.orgId}</TableCell>
              <TableCell>{contractor.contractorName}</TableCell>
              <TableCell>{contractor.email}</TableCell>
              <TableCell>{contractor.organisationName}</TableCell>
              {/* <TableCell className="bg-[#E8F5FF]">
                <div className="flex justify-center gap-3 ">
                  <EditContractor contractor={contractor} />
                  <DeleteContractor contractorId={contractor._id} />
                </div>
              </TableCell> */}
            </TableRow>
          ))}
        </TableBody>
      </Table>
      {isFetchingNextPage && (
        <div className="text-center py-4">
          <Loader size={5} />
        </div>
      )}
      {hasNextPage && <div ref={observerTarget} className="h-10" />}
    </div>
  );
};

export default ContractorsTable;
