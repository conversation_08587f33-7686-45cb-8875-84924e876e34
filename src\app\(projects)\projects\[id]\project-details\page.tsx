"use client";

import { useCallback, useState } from "react";
import { useParams } from "next/navigation";
import { useMutationState } from "@tanstack/react-query";

import ProjectDetailsForm from "@/components/dashboard/ProjectDetails";
import useGetProjectById from "@/services/project/getProject";
import Loader from "@/components/ui/loader";
import ErrorText from "@/components/ui/errortext";
import { Button } from "@/components/ui/button";
import { UPDATE_PROJECT_MUTATION_KEY } from "@/services/project/updateProject";

const ProjectDetailsPage = () => {
  const { id } = useParams();
  const {
    data: projectData,
    isLoading,
    error,
  } = useGetProjectById(id as string);

  const [mutationStatus] = useMutationState({
    filters: { mutationKey: UPDATE_PROJECT_MUTATION_KEY, status: "pending" },
    select: (mutation) => mutation.state.status,
  });

  const [isButtonDisabled, setIsButtonDisabled] = useState(true);

  const handleFormStateChange = useCallback((isDirty: boolean) => {
    setIsButtonDisabled(!isDirty);
  }, []);

  if (isLoading) return <Loader />;
  if (error) return <ErrorText entity="project details" />;

  return (
    <div className="flex flex-col gap-y-6">
      <div className="flex justify-between items-center">
        <div className="space-y-1.5 text-neutrals-G800">
          <h4 className="font-semibold">Project Settings</h4>
          <p className="text-sm">Manage your project details and settings</p>
        </div>
        <Button
          form="projectDetailsForm"
          disabled={isButtonDisabled}
          loading={mutationStatus === "pending"}
          type="submit"
          className="px-4 min-w-[124px]"
        >
          Save changes
        </Button>
      </div>
      <div className="flex-1">
        {projectData && (
          <ProjectDetailsForm
            projectData={projectData}
            onFormStateChange={handleFormStateChange}
          />
        )}
      </div>
    </div>
  );
};

export default ProjectDetailsPage;
