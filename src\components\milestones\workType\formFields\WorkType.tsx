// eslint-disable-next-line @typescript-eslint/ban-ts-comment
// @ts-nocheck

// TODO: remove eslint disable and fix type issue
import { Check, ChevronDown, Plus } from "lucide-react";
import { differenceInDays, format } from "date-fns";
import { UseFormReturn } from "react-hook-form";
import { z } from "zod";
import { useParams } from "next/navigation";
import { useEffect, useRef, useState } from "react";

import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Popover,
  PopoverClose,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { cn } from "@/lib/utils";
import { Calendar } from "@/components/ui/calendar";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Button } from "@/components/ui/button";
import useGetWorktypeTemplate from "@/services/milestone/getWorktypeTemplate";
import { WorkTypeUnit } from "@/types/Worktype";
import SkilledLaborerCount from "../../formFields/SkilledLaborerCount";
import Duration from "../../formFields/Duration";
import useGetUnits from "@/services/units/getUnits";
import CalendarIcon from "@/components/icons/Calendar";
import { milestoneSchema } from "@/schema/milestone";
import { workTypeSchema } from "@/schema/workType";

type BaseWorkTypeFormFieldProps = {
  milestoneTemplateId: string;
  isEdit?: boolean;
};

type MilestoneWorkTypeFormFieldProps = BaseWorkTypeFormFieldProps & {
  form: UseFormReturn<z.infer<typeof milestoneSchema>>;
  fieldIndex: number;
};

type SingleWorkTypeFormFieldProps = BaseWorkTypeFormFieldProps & {
  form: UseFormReturn<z.infer<typeof workTypeSchema>>;
  fieldIndex?: never;
};

type WorkTypeFormFieldProps =
  | MilestoneWorkTypeFormFieldProps
  | SingleWorkTypeFormFieldProps;

const WorkTypeFormField = ({
  form,
  milestoneTemplateId,
  fieldIndex,
  isEdit = false,
}: WorkTypeFormFieldProps) => {
  const { id } = useParams() as { id: string };
  const { data: workTypeTemplate, isPending: isPendingWorktypeTemplate } =
    useGetWorktypeTemplate({
      projectId: id,
      milestoneTemplateId,
    });

  const { data: commonUnits, isPending: isPendingCommonUnits } = useGetUnits();

  const popoverCloseRef = useRef<HTMLButtonElement>(null);
  const [open, setOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");

  const getFieldName = (name: string) => {
    return fieldIndex !== undefined ? `worktypes.${fieldIndex}.${name}` : name;
  };

  const selectedTemplate = workTypeTemplate?.find(
    (template) =>
      template._id === form.watch(getFieldName("worktypeTemplateId")),
  );

  const [units, setUnits] = useState<WorkTypeUnit[]>([]);
  const [optionalUnits, setOptionalUnits] = useState<WorkTypeUnit[]>([]);

  const handleCreateNewWorktype = () => {
    form.setValue(getFieldName("isCustom"), true);
    form.setValue(getFieldName("worktypeName"), searchQuery);
    form.setValue(getFieldName("worktypeTemplateId"), "");
    setOpen(false);
  };

  const worktypeTemplateId = form.watch(getFieldName("worktypeTemplateId"));
  const startDate = form.watch(getFieldName("startDate"));
  const endDate = form.watch(getFieldName("endDate"));
  const isCustomWorkType = form.watch(getFieldName("isCustom"));
  const workTypeName = form.watch(getFieldName("worktypeName"));

  useEffect(() => {
    if (startDate && endDate) {
      const days = differenceInDays(endDate, startDate);
      if (days > 0) {
        form.setValue(getFieldName("duration"), days);
      }
    }
  }, [endDate, fieldIndex, form, startDate]);

  useEffect(() => {
    const updateQtyUnitsOptions = (workTypeId: string) => {
      if (workTypeId) {
        const workType = workTypeTemplate?.find(
          (workType) => workType._id === workTypeId,
        );

        if (!workType) {
          // toast.error("Work type not found");
          return;
        }

        setUnits(workType.worktypeTemplateId.standardUnits);
        setOptionalUnits(workType.worktypeTemplateId.optionalUnits);
      }
    };

    if (worktypeTemplateId) {
      updateQtyUnitsOptions(worktypeTemplateId);
    }
  }, [worktypeTemplateId, workTypeTemplate]);

  const workTypeDisplayName = isCustomWorkType
    ? workTypeName
    : selectedTemplate?.worktypeTemplateId.worktype;

  return (
    <>
      <FormField
        control={form.control}
        name={getFieldName("worktypeTemplateId")}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Work type</FormLabel>
            <FormControl>
              <Popover open={open} onOpenChange={setOpen} modal>
                <PopoverTrigger asChild>
                  <Button
                    variant="input"
                    role="combobox"
                    aria-expanded={open}
                    className="w-full justify-between"
                    disabled={isEdit}
                  >
                    {workTypeDisplayName || (
                      <span className="text-neutrals-G100 font-semibold">
                        Choose
                      </span>
                    )}

                    <ChevronDown className="ml-2 size-5 shrink-0 text-gray" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-[28.6rem] max-w-[--radix-popover-trigger-width]">
                  <Command>
                    <CommandInput
                      placeholder="Search worktype..."
                      value={searchQuery}
                      onValueChange={setSearchQuery}
                    />
                    <CommandList>
                      <CommandEmpty className="pt-3">
                        <div className="flex flex-col items-center gap-y-2">
                          <Button
                            variant="outline"
                            size="sm"
                            className="flex items-center w-full justify-start gap-x-2 text-neutrals-G600 font-normal"
                            onClick={handleCreateNewWorktype}
                            disabled={!searchQuery.trim()}
                          >
                            <Plus className="size-[18px] text-primary-blue-B900" />
                            {searchQuery.trim() ? (
                              <>
                                Create{" "}
                                <span className="text-primary-blue-B900">
                                  `{searchQuery}`
                                </span>
                              </>
                            ) : (
                              "Type to create a new worktype"
                            )}
                          </Button>
                        </div>
                      </CommandEmpty>
                      <CommandGroup>
                        {workTypeTemplate
                          ?.filter(({ worktypeTemplateId }) =>
                            worktypeTemplateId.worktype
                              .toLowerCase()
                              .includes(searchQuery.toLowerCase()),
                          )
                          .map(({ _id, worktypeTemplateId }, index) => (
                            <CommandItem
                              key={_id}
                              value={worktypeTemplateId.worktype}
                              onSelect={() => {
                                form.setValue(getFieldName("isCustom"), false);
                                field.onChange(_id);
                                setOpen(false);
                              }}
                              className="p-3"
                            >
                              {++index}. {worktypeTemplateId.worktype}
                              <Check
                                className={cn(
                                  "ml-auto h-4 w-4",
                                  field.value === _id
                                    ? "opacity-100"
                                    : "opacity-0",
                                )}
                              />
                            </CommandItem>
                          ))}
                      </CommandGroup>
                    </CommandList>
                  </Command>
                </PopoverContent>
              </Popover>
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <div className="flex gap-x-2">
        <FormField
          control={form.control}
          name={getFieldName("startDate")}
          render={({ field }) => (
            <FormItem className="basis-1/2">
              <FormLabel>Start Date</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="input"
                      className={cn(
                        "text-left",
                        !field.value && "text-neutrals-G100",
                      )}
                    >
                      {field.value ? (
                        format(field.value, "dd/MM/yyyy")
                      ) : (
                        <span>Choose date</span>
                      )}
                      <CalendarIcon className="ml-auto" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <PopoverClose ref={popoverCloseRef} className="hidden" />
                  <Calendar
                    selected={field.value}
                    onSelect={(value) => {
                      popoverCloseRef.current?.click();
                      field.onChange(value);
                    }}
                    mode="single"
                    showOutsideDays={false}
                    endMonth={
                      new Date(Date.now() + 1000 * 60 * 60 * 24 * 365 * 10)
                    }
                  />
                </PopoverContent>
              </Popover>

              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name={getFieldName("endDate")}
          render={({ field }) => (
            <FormItem className="basis-1/2">
              <FormLabel>End Date</FormLabel>
              <Popover>
                <PopoverTrigger asChild>
                  <FormControl>
                    <Button
                      variant="input"
                      className={cn(
                        "text-left",
                        !field.value && "text-neutrals-G100",
                      )}
                    >
                      {field.value ? (
                        format(field.value, "dd/MM/yyyy")
                      ) : (
                        <span>Choose date</span>
                      )}
                      <CalendarIcon className="ml-auto" />
                    </Button>
                  </FormControl>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <PopoverClose ref={popoverCloseRef} className="hidden" />
                  <Calendar
                    selected={field.value}
                    onSelect={(value) => {
                      popoverCloseRef.current?.click();
                      field.onChange(value);
                    }}
                    mode="single"
                    showOutsideDays={false}
                    endMonth={
                      new Date(Date.now() + 1000 * 60 * 60 * 24 * 365 * 10)
                    }
                  />
                </PopoverContent>
              </Popover>

              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      <Duration
        value={form.watch(getFieldName("duration")) || 0}
        onChange={(val) => form.setValue(getFieldName("duration"), val)}
        startDate={form.watch(getFieldName("startDate"))}
        endDate={form.watch(getFieldName("endDate"))}
      />

      <div className="flex gap-x-2">
        <FormField
          control={form.control}
          name={getFieldName("totalQty")}
          render={({ field }) => (
            <FormItem className="basis-1/2">
              <FormLabel>Total quantity of work</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  placeholder="Enter quantity"
                  min="0"
                  maxLength={10}
                  onInput={(e) => {
                    const input = e.currentTarget;
                    if (input.value.length > 10) {
                      input.value = input.value.slice(0, 10);
                    }
                    field.onChange(input.value);
                  }}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name={getFieldName("unit")}
          render={({ field }) => (
            <FormItem className="basis-1/2">
              <FormLabel>Unit</FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} value={field.value}>
                  <FormControl>
                    <SelectTrigger
                      loading={
                        form.watch(getFieldName("isCustom"))
                          ? isPendingCommonUnits
                          : isPendingWorktypeTemplate
                      }
                    >
                      <SelectValue placeholder="Kg" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {form.watch(getFieldName("isCustom")) ? (
                      commonUnits?.map((unit) => (
                        <SelectItem key={unit._id} value={unit._id}>
                          {unit.name}
                        </SelectItem>
                      ))
                    ) : units?.length === 0 ? (
                      <SelectItem disabled value="disabled">
                        Select work type
                      </SelectItem>
                    ) : (
                      units?.map((unit) => (
                        <SelectItem key={unit._id} value={unit._id}>
                          {unit.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      <div className="flex gap-x-2">
        <FormField
          control={form.control}
          name={getFieldName("optionalQty.totalQty")}
          render={({ field }) => (
            <FormItem className="basis-1/2">
              <FormLabel>Optional quantity</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  placeholder="Enter quantity"
                  min="0"
                  maxLength={10}
                  onInput={(e) => {
                    const input = e.currentTarget;
                    if (input.value.length > 10) {
                      input.value = input.value.slice(0, 10);
                    }
                    field.onChange(input.value);
                  }}
                  {...field}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          control={form.control}
          name={getFieldName("optionalQty.unit")}
          render={({ field }) => (
            <FormItem className="basis-1/2">
              <FormLabel>Unit</FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} value={field.value}>
                  <FormControl>
                    <SelectTrigger
                      loading={
                        form.watch(getFieldName("isCustom"))
                          ? isPendingCommonUnits
                          : isPendingWorktypeTemplate
                      }
                    >
                      <SelectValue placeholder="Kg" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {form.watch(getFieldName("isCustom")) ? (
                      commonUnits?.map((unit) => (
                        <SelectItem key={unit._id} value={unit._id}>
                          {unit.name}
                        </SelectItem>
                      ))
                    ) : optionalUnits?.length === 0 ? (
                      <SelectItem disabled value="disabled">
                        Select work type
                      </SelectItem>
                    ) : (
                      optionalUnits?.map((unit) => (
                        <SelectItem key={unit._id} value={unit._id}>
                          {unit.name}
                        </SelectItem>
                      ))
                    )}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
      <div className="flex flex-col gap-y-2">
        <Label>Skilled laborer count</Label>
        <div className="flex gap-x-2">
          <SkilledLaborerCount fieldIndex={fieldIndex} />
        </div>
      </div>
    </>
  );
};

export default WorkTypeFormField;
