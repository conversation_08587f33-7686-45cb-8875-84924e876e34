/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "github.com",
        port: "",
        //   pathname: '/account123/**',
        //   search: '',
      },
      {
        protocol: "https",
        hostname: process.env.NEXT_PUBLIC_IMG_HOSTNAME,
        port: "",
      },
      {
        protocol: "https",
        hostname: "projectsmate-testing.s3.ap-south-1.amazonaws.com",
      },
    ],
  },
};

export default nextConfig;
