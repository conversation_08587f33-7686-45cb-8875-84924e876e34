"use client";

import { But<PERSON> } from "@/components/ui/button";
import { useState } from "react";
import { toast } from "sonner";

import {
  AlertDialog,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogAction,
  AlertDialogCancel,
} from "@/components/ui/alertDialog";
import DeleteIcon from "../icons/Delete";
import { useDeletePaymentSchedule } from "@/services/paymentSchedules/deletePaymentSchedule";

type DeleteScheduleProps = {
  scheduleId: string;
};

const DeleteSchedule = ({ scheduleId }: DeleteScheduleProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const { mutate, isPending } = useDeletePaymentSchedule();

  const handleDelete = () => {
    mutate(
      { scheduleId },
      {
        onSuccess: () => {
          setIsOpen(false);
          toast.success("Payment schedule deleted successfully.");
        },
        onError: () => {
          toast.error("Failed to delete payment schedule. Please try again.");
        },
      },
    );
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger>
        <DeleteIcon />
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Payment Schedule?</AlertDialogTitle>
          <AlertDialogDescription>
            Doing this will remove this payment schedule from the project.
          </AlertDialogDescription>
        </AlertDialogHeader>

        <AlertDialogFooter>
          <AlertDialogCancel disabled={isPending}>Cancel</AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button onClick={handleDelete} disabled={isPending}>
              {isPending ? "Deleting..." : "Yes, Delete"}
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DeleteSchedule;
