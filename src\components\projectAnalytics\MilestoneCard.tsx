import React from "react";
import { format } from "date-fns";

import { Badge } from "@/components/ui/badge";
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { ProjectAnalytics } from "@/types/Project";
import DayOfCompletionStatusBadge, {
  checkStatus,
  getStatusResult,
} from "./DayOfCompletionStatusBadge";
import WorkersIcon from "../icons/Workers";
import { cn } from "@/lib/utils";

type MilestoneCardProps = {
  milestone: ProjectAnalytics["milestones"][number];
};

const MilestoneCard = ({ milestone }: MilestoneCardProps) => {
  return (
    <Card className="mb-6">
      <CardHeader className="flex flex-col justify-between gap-2">
        <div className="flex items-center justify-between">
          <CardTitle>{milestone.name}</CardTitle>
          <div className="flex items-center gap-2">
            <div
              className={`w-2 h-2 rounded-full ${
                milestone.progress < 40
                  ? "bg-[#C00]"
                  : milestone.progress < 90
                    ? "bg-[#DA9500]"
                    : "bg-[#4CAF50]"
              }`}
            />
            <span
              className={cn(
                "text-sm font-semibold",
                milestone.progress < 40
                  ? "text-[#C00]"
                  : milestone.progress < 90
                    ? "text-[#DA9500]"
                    : "text-[#4CAF50]",
              )}
            >
              {milestone.progress?.toFixed(2)}% Completed
            </span>
          </div>
        </div>

        <p className="text-sm font-semibold text-name-title flex gap-1 items-center mt-2">
          <WorkersIcon />
          {milestone.noOfWorkers}{" "}
          <span className="font-normal">
            {milestone.noOfWorkers === 1 ? "Worker" : "Workers"}
          </span>
        </p>

        <div className="flex items-center justify-between text-name-title">
          <div className="flex gap-4">
            <div className="flex gap-2">
              <span className="text-sm">Start date:</span>
              <span className="text-sm font-semibold">
                {format(new Date(milestone.startDate), "dd/MM/yyyy")}
              </span>
            </div>
            <div className="h-[1.3125rem] w-[0.125rem] bg-blue-3"></div>
            <div className="flex gap-2">
              <span className="text-sm ">End date:</span>
              <span className="text-sm font-semibold">
                {format(new Date(milestone.endDate), "dd/MM/yyyy")}
              </span>
            </div>
          </div>

          <div className="flex gap-2 text-right">
            <span className="text-sm">Projected end date:</span>
            <span className="text-sm font-semibold">
              {format(new Date(milestone.projectedEndDate), "dd/MM/yyyy")}
            </span>
            <DayOfCompletionStatusBadge
              projectedEndDate={milestone.projectedEndDate}
              expectedEndDate={milestone.endDate}
            />
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <div className="grid grid-cols-5 gap-4 text-xs font-medium text-name-title">
          <div>Work Types</div>
          <div>No. of workers</div>
          <div>Productivity (%)</div>
          <div>Total work done (%)</div>
          <div>Status</div>
        </div>
        <div className="mt-4 space-y-2 text-neutrals-G900 text-xs">
          {milestone.workTypes.map((workType) => (
            <div
              key={crypto.randomUUID()}
              className="grid grid-cols-5 gap-4 items-center text-name-title text-sm font-medium"
            >
              <div className="text-neutrals-G900">{workType.name}</div>
              <div>{workType.noOfWorkers}</div>
              <div>{workType.productivity?.toFixed(2)}%</div>
              <div>{workType.totalWorkDone?.toFixed(2)}%</div>
              <div>
                <Badge
                  variant={workType.status >= 0 ? "default" : "destructive"}
                >
                  {getStatusResult(
                    workType.status,
                    checkStatus(workType.status),
                  )}
                </Badge>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default MilestoneCard;
