import { useQuery } from "@tanstack/react-query";
import api from "@/lib/api-client";
import { QualityChecklistData } from "@/types/QualityChecklist";

const getQualityChecklistById = async (
  projectId: string,
  checklistId: string,
): Promise<QualityChecklistData> => {
  try {
    const response = await api.get(
      `/quality-checklist/${projectId}/${checklistId}`,
    );

    if (!response || !response.data) {
      throw new Error("No data returned from API");
    }

    return response.data;
  } catch (error) {
    console.error("Detailed Error in API Call:", error);
    throw error;
  }
};
const useGetQualityChecklistById = (projectId: string, checklistId: string) => {
  return useQuery({
    queryKey: ["quality-checklist-by-id", projectId, checklistId],
    queryFn: () => getQualityChecklistById(projectId, checklistId),
    enabled: !!projectId && !!checklistId,
    retry: 1,
  });
};

export default useGetQualityChecklistById;
