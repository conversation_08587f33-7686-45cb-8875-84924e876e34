import api from "@/lib/api-client";
import { useQuery } from "@tanstack/react-query";

const getDesignProjectAlerts = async () => {
  const response = await api.get("/dashboard-alerts/getDesignProject");
  return response.data;
};

const useDesignProjectAlerts = () => {
  return useQuery({
    queryKey: ["design-project-analytics"],
    queryFn: getDesignProjectAlerts,
  });
};

export default useDesignProjectAlerts;
