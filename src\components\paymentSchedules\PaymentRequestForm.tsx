"use client";
import { ComponentProps, useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";

import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../ui/form";
import { Input } from "../ui/input";
import { paymentRequestSchema } from "@/schema/paymentSchedule";
import { cn } from "@/lib/utils";
import { Button } from "../ui/button";
import Play from "../icons/Play";
import { useSendPaymentRequest } from "@/services/paymentSchedules/sendPaymentAmount";
import { toast } from "sonner";
import { queryClient } from "@/app/RootLayoutClient";
import UploadInvoice from "./UploadInvoice";

type PaymentRequestFormProps = ComponentProps<"form"> & {
  scheduleId: string;
  defaultAmount: number;
  defaultPercentage: number;
  totalBudget: any;
  invoice: string;
};

const PaymentRequestForm = ({
  className,
  scheduleId,
  defaultAmount,
  defaultPercentage,
  totalBudget,
  invoice,
  ...props
}: PaymentRequestFormProps) => {
  const [isEditable, setIsEditable] = useState(defaultAmount === 0);

  const form = useForm<z.infer<typeof paymentRequestSchema>>({
    resolver: zodResolver(paymentRequestSchema),
    defaultValues: {
      amount: defaultAmount.toString(),
      percentage: defaultPercentage?.toString(),
    },
  });

  const percentage = form.watch("percentage");

  useEffect(() => {
    const parsed = parseFloat(percentage);
    if (!isNaN(parsed)) {
      const calculatedAmount = ((parsed / 100) * totalBudget).toFixed(2);
      form.setValue("amount", calculatedAmount, {
        shouldValidate: true,
      });
    }
  }, [percentage, totalBudget, form]);

  const { mutate: sendPaymentRequest, isPending } = useSendPaymentRequest();

  const onSubmit = (values: z.infer<typeof paymentRequestSchema>) => {
    sendPaymentRequest(
      {
        scheduleId,
        amount: values.amount.toString(),
        percentage: values.percentage.toString(),
      },
      {
        onSuccess: () => {
          queryClient.invalidateQueries({
            queryKey: ["paymentSchedules"],
          });
          toast.success("Payment request sent successfully");
          setIsEditable(false);
        },
        onError: (error: any) => {
          toast.error(
            error?.response?.data?.message ||
              "Something went wrong. Please try again.",
          );
          form.reset();
        },
      },
    );
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className={cn("flex items-end gap-x-2", className)}
        {...props}
      >
        <FormField
          control={form.control}
          name="percentage"
          render={({ field }) => (
            <FormItem className="flex-1 relative ">
              <FormLabel className="absolute min-w-[200px] -top-6 left-0">
                Required payment percentage
              </FormLabel>
              <div className="relative">
                <Input
                  type="text"
                  value={field.value ? `${field.value.replace("%", "")}%` : ""}
                  onChange={(e) => {
                    const rawValue = e.target.value.replace("%", "");
                    if (!isNaN(Number(rawValue)) || rawValue === "") {
                      field.onChange(rawValue);
                    }
                  }}
                  required
                  disabled={!isEditable}
                  className="pr-6"
                />
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="amount"
          render={({ field }) => (
            <FormItem className="flex-1 relative">
              {/* <FormLabel>Amount</FormLabel> */}
              <div className="relative">
                <Input
                  type="text"
                  value={field.value ? `Rs ${field.value}` : ""}
                  disabled
                  className="pl-6 max-w-[180px]"
                />
              </div>
              <FormMessage />
            </FormItem>
          )}
        />

        <UploadInvoice projectId={scheduleId} invoice={invoice} />

        <div className="flex flex-col">
          {!isEditable && defaultAmount > 0 && (
            <Button
              type="button"
              className="text-[14px] text-[#2F80ED] font-[400] text-end bg-white hover:bg-white w-fit px-0 ml-auto"
              onClick={() => setIsEditable(true)}
            >
              Edit
            </Button>
          )}
          <Button
            type="submit"
            className={`px-3 [&>svg]:ml-[7px] ${!isEditable && "bg-[#A1A1A1] text-[#FFFFFF]"} `}
            disabled={!isEditable || isPending}
          >
            Send Request {isEditable && <Play />}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default PaymentRequestForm;
