"use client";

import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Plus } from "lucide-react";
import { format } from "date-fns";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import {
  <PERSON><PERSON>,
  <PERSON>et<PERSON>ontent,
  <PERSON>etHeader,
  <PERSON>etT<PERSON>le,
  SheetTrigger,
} from "@/components/ui/sheet";
import { paymentScheduleSchema } from "@/schema/paymentSchedule";
import PaymentScheduleFormFields from "./PaymentScheduleFormFields";
import { useParams } from "next/navigation";
import { useCreatePaymentSchedule } from "@/services/paymentSchedules/createPaymentSchedules";
import { toast } from "sonner";
import { useSheetFormState } from "@/hooks/useSheetFormState";

const CreateSchedule = () => {
  const [open, setOpen] = useState(false);

  const params = useParams();
  const projectId = params?.id as string;

  const form = useForm<z.infer<typeof paymentScheduleSchema>>({
    resolver: zodResolver(paymentScheduleSchema),
    defaultValues: {
      scheduleName: "",
      stageId: "",
      date: undefined,
    },
  });

  const createSchedule = useCreatePaymentSchedule();

  const onSubmit = async (values: z.infer<typeof paymentScheduleSchema>) => {
    console.log(values, "payment values");
    if (!projectId) return;

    createSchedule.mutate(
      {
        ...values,
        date: values.date
          ? format(new Date(values.date), "yyyy-MM-dd")
          : undefined,
        projectId,
      },
      {
        onSuccess: () => {
          toast.success("Schedule created successfully");
          setOpen(false);
        },
        onError: (error: any) => {
          console.log(error, "errorNow");
          toast.error(error?.response?.data?.message || "Something went wrong");
        },
      },
    );
  };

  const { handleOpenChange, handleExplicitClose } = useSheetFormState({
    form,
    setOpen,
    preserveOnImplicitClose: true,
  });

  return (
    <Sheet open={open} onOpenChange={handleOpenChange}>
      <SheetTrigger asChild>
        <Button className="gap-x-2 pl-4 pr-3">
          Create schedule
          <Plus className="size-[22px] stroke-[2.5px]" />
        </Button>
      </SheetTrigger>
      <SheetContent
        className="max-w-[507px] flex flex-col gap-0"
        onExplicitClose={handleExplicitClose}
      >
        <SheetHeader>
          <SheetTitle>Create a payment schedule</SheetTitle>
        </SheetHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col justify-between flex-1"
          >
            <div className="space-y-4">
              <PaymentScheduleFormFields form={form} />
            </div>
            <Button
              className="ml-auto px-4 py-3 sticky bottom-0"
              type="submit"
              loading={createSchedule.isPending}
            >
              {createSchedule.isPending ? "Creating..." : "Create"}
            </Button>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  );
};

export default CreateSchedule;
