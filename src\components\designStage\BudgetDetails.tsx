import React, { useState } from "react";
import { Dialog, DialogContent, DialogTrigger } from "../ui/dialog";
import Budget from "../icons/Budget";
import { DialogClose } from "@radix-ui/react-dialog";
import CloseB from "../icons/CloseB";
import useDesignStageAnalytics from "@/services/designStage/getBudget";

const BudgetDetails = ({ projectId }: { projectId: string }) => {
  const [open, setOpen] = useState(false);
  const { data, isPending } = useDesignStageAnalytics(projectId);
  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild className="h-fit">
        <div className="flex items-center gap-[4px] bg-[#EEF5FE] border-[1px] border-[#BFD8F9] rounded-[6px] px-[12px] py-[6px] cursor-pointer">
          <Budget />
          <span className="text-[14px] font-[500] text-[#2F80ED]">
            Budget Details
          </span>
        </div>
      </DialogTrigger>
      <DialogContent className="p-[24px] rounded-[12px] w-full max-w-[434px]">
        <div className="w-full flex items-center justify-between">
          <h3 className="font-[600] text-[20px] text-[#1E1E1E]">
            Budget details
          </h3>
          <DialogClose>
            <CloseB />
          </DialogClose>
        </div>

        {isPending ? (
          "Loading budget details..."
        ) : (
          <div className="mt-[32px] grid grid-cols-2 gap-y-[24px] gap-x-2">
            <div>
              <h4 className="text-[#7F8FA4] text-[12px] font-[500]">
                Total Budget
              </h4>
              <p className="text-[#00142E] text-[16px] font-[600] mt-[4px]">
                ₨ {data?.totalBudget}
              </p>
            </div>

            <div>
              <h4 className="text-[#7F8FA4] text-[12px] font-[500]">
                Remaining Budget
              </h4>
              <p className="text-[#00142E] text-[16px] font-[600] mt-[4px]">
                ₨ {data?.remainingBudget}
              </p>
            </div>

            <div>
              <h4 className="text-[#7F8FA4] text-[12px] font-[500]">
                Used Budget
              </h4>
              <p className="text-[#00142E] text-[16px] font-[600] mt-[4px]">
                ₨ {data?.usedBudget}
              </p>
            </div>

            <div>
              <h4 className="text-[#7F8FA4] text-[12px] font-[500]">
                Stage-wise Utilization
              </h4>
              <p className="text-[#4CAF50] text-[16px] font-[700] mt-[4px]">
                {data?.totalAllocatedPercent}%
              </p>
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};

export default BudgetDetails;
