import { useInfiniteQuery } from "@tanstack/react-query";

import api from "@/lib/api-client";
import { ProjectData } from "@/types/Project";

export type UseGetStagesForTimesheet = {
  projectId: string;
  search?: string;
  limit?: number;
};

type GetStagesForTimesheet = UseGetStagesForTimesheet & {
  pageParam?: number;
};

const getStagesForTimesheet = async ({
  projectId,
  search,
  pageParam,
  limit,
}: GetStagesForTimesheet): Promise<{
  data: { _id: string; stageName: string }[];
  totalCount: number;
  limit: number;
}> => {
  return await api.get(
    `/time-sheet/get-stages/${projectId}
?page=${pageParam}&limit=${limit}&search=${search}`,
  );
};

const useGetStagesForTimesheet = ({
  projectId,
  search,
  limit = 10,
}: UseGetStagesForTimesheet) => {
  return useInfiniteQuery({
    queryKey: ["time-sheet-stages", projectId, search, limit],
    queryFn: ({ pageParam = 1 }) =>
      getStagesForTimesheet({ projectId, search, pageParam, limit }),
    getNextPageParam: (lastPage, allPages) => {
      const nextPage = allPages.length + 1;
      return lastPage.totalCount > allPages.length * lastPage.limit
        ? nextPage
        : undefined;
    },
    initialPageParam: 1,
    enabled: !!projectId,
  });
};

export default useGetStagesForTimesheet;
