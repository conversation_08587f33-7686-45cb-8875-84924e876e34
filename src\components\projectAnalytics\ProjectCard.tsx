// import { ComponentPropsWithoutRef } from "react";
// import { format } from "date-fns";

// import { Progress } from "@/components/ui/progress";
// import { ProjectAnalyticsTotal } from "@/types/Project";
// import DayOfCompletionStatusBadge from "./DayOfCompletionStatusBadge";
// import { cn } from "@/lib/utils";
// import Users from "../icons/Users";

// type ProjectCardProps = ComponentPropsWithoutRef<"div"> & {
//   project: ProjectAnalyticsTotal;
// };

// const ProjectCard = ({ project, className, ...props }: ProjectCardProps) => {
//   return (
//     <div className={cn("flex gap-x-[4.06rem]", className)} {...props}>
//       <div className="space-y-4 basis-[360px]">
//         <div className="space-y-1">
//           <div className="text-xs  font-medium px-2.5 py-1 rounded bg-blue-3 text-ID-text w-fit">
//             ID {project.projectId}
//           </div>
//           <p className="text-2xl font-medium text-neutrals-G900 break-all">
//             {project.projectName}
//           </p>
//         </div>
//         <div className="px-4 py-2 rounded-lg border shrink-0 border-feather-gray flex gap-x-4 items-center text-sm">
//           <p className="text-sm text-sidebar-gray">Project Timeline:</p>
//           <p className="font-medium text-name-title">
//             {format(new Date(project.startDate), "dd MMM yyyy")} -{" "}
//             {format(new Date(project.endDate), "dd MMM yyyy")}
//           </p>
//         </div>
//       </div>
//       <div className="flex flex-col justify-between flex-1">
//         <div className="flex justify-between gap-x-2">
//           <div className="space-y-1 ">
//             <h3 className="text-sm flex gap-x-1 items-center text-sidebar-gray">
//               <Users />
//               Total no. of workers
//             </h3>
//             <p className="text-lg font-semibold text-name-title">
//               {project.noOfWorker}
//             </p>
//           </div>
//           <div className="space-y-1">
//             <h3 className="text-sm text-sidebar-gray">
//               Productivity of workers
//             </h3>
//             <p className="text-lg font-semibold text-name-title">
//               {project.workerProductivity?.toFixed(2)}%
//             </p>
//           </div>
//           <div className="space-y-1">
//             <h3 className="text-sm text-sidebar-gray">Projected completion</h3>
//             <div className="flex gap-2">
//               <p className="font-semibold text-name-title">
//                 {format(new Date(project.projectedEndDate), "dd MMM yyyy")}
//               </p>

//               <DayOfCompletionStatusBadge
//                 projectedEndDate={project.projectedEndDate}
//                 expectedEndDate={project.endDate}
//                 className="shrink-0"
//               />
//             </div>
//           </div>
//         </div>
//         <div className="flex items-end grow gap-x-[18px]">
//           <Progress
//             value={project.progress}
//             variant={
//               project.progress < 40
//                 ? "danger"
//                 : project.progress < 90
//                   ? "warning"
//                   : "success"
//             }
//             className="h-2"
//           />
//           <p
//             className={cn(
//               "text-sm font-medium shrink-0",
//               project.progress < 40
//                 ? "text-[#C00]"
//                 : project.progress < 90
//                   ? "text-[#DA9500]"
//                   : "text-[#4CAF50]",
//             )}
//           >
//             {project.progress?.toFixed(2)}% Completed
//           </p>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default ProjectCard;

import { ComponentPropsWithoutRef } from "react";
import { format } from "date-fns";
import { Progress } from "@/components/ui/progress";
import { ProjectAnalyticsTotal } from "@/types/Project";
import DayOfCompletionStatusBadge from "./DayOfCompletionStatusBadge";
import { cn } from "@/lib/utils";
import { MapPin } from "lucide-react";
import CompletionStatus from "../ui/completionStatus";
import UsersIcon from "../icons/usersIcon";
import ProductivityIcon from "../icons/ProductivityIcon";
import CalenderIcon from "../icons/calenderIcon";
type ProjectCardProps = ComponentPropsWithoutRef<"div"> & {
  project: ProjectAnalyticsTotal;
};

const ProjectCard = ({ project, className, ...props }: ProjectCardProps) => {
  return (
    <div
      className={cn(
        " px-6 py-5 bg-white rounded-xl border border-neutrals-G40 relative overflow-hidden mb-6",
        className,
      )}
      {...props}
    >
      <div className="absolute top-0 left-0 w-full h-[88px] bg-primary-blue-B30" />

      <div className="relative">
        <div className="flex justify-between items-start mb-8 ">
          <div className="space-y-1">
            <h2 className="text-xl font-semibold text-neutrals-G900">
              {project.projectName}
            </h2>
            <div className="flex gap-1">
              <div className="p-1 rounded border border-[#c6c6c6] bg-neutrals-G30 h-[23px] inline-flex items-center">
                <span className="text-xs text-neutrals-G600">
                  #ID {project.projectId}
                </span>
              </div>
              <div className="p-1 flex items-center rounded border border-[#c6c6c6] bg-neutrals-G30 h-[23px]">
                <MapPin className="size-3.5 mr-1" />
                <span className="text-xs text-neutrals-G600">
                  {project.location}
                </span>
              </div>
            </div>
          </div>

          <CompletionStatus status={project.status} className="shrink-0" />
        </div>

        <div className="flex justify-between items-start gap-5">
          <div className="flex-1 flex justify-between items-center">
            <div className="w-1/3 flex items-center gap-1">
              <span className="inline-flex items-center gap-1 text-neutrals-G800 text-sm font-bold">
                <UsersIcon /> {project.noOfWorker}
              </span>
              <span className="text-neutrals-G800 text-sm font-normal">
                {project.noOfWorker === 1 ? "worker" : "workers"}
              </span>
            </div>

            <div className="w-1/3 flex items-center gap-1">
              <span className="inline-flex items-center gap-1 text-neutrals-G800 text-sm font-bold">
                <ProductivityIcon />
                {project.workerProductivity?.toFixed(0)}%
                <span className="text-neutrals-G800 text-sm font-normal">
                  Productivity
                </span>
              </span>
            </div>

            <div className="w-1/3 flex items-center gap-1">
              <CalenderIcon />
              <span className="text-neutrals-G800 text-sm font-normal">
                {format(new Date(project.startDate), "dd MMM")} -{" "}
                {format(new Date(project.endDate), "dd MMM")}
              </span>
            </div>
          </div>

          <div className="w-[280px] space-y-1">
            <div className="flex justify-between items-center">
              <span className="text-xs text-neutrals-G600">
                Projected Completion
              </span>
              <div className="flex items-center gap-1.5">
                <span className="text-xs text-neutrals-G600">
                  {format(new Date(project.projectedEndDate), "dd MMM")}
                </span>
                <DayOfCompletionStatusBadge
                  projectedEndDate={project.projectedEndDate}
                  expectedEndDate={project.endDate}
                  className="shrink-0"
                />
              </div>
            </div>
            <Progress
              value={project.progress}
              variant={
                project.progress < 40
                  ? "danger"
                  : project.progress < 90
                    ? "warning"
                    : "success"
              }
              className="h-1"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProjectCard;
