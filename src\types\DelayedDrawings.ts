import { ProjectData } from "./Project";

export type Milestone = {
  _id: string;
  milestoneTemplateId: string;
  milestoneName: string;
  isCustom: boolean;
  projectId: string;
  startDate: string;
  duration: number;
  paymentDate: string;
  completed: boolean;
  createdAt: string;
  updatedAt: string;
  __v: number;
};

export type MilestoneTemplate = {
  _id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
  order: number;
};

export type Alert = {
  _id: string;
  alertType: string;
  organisationId: string;
  userId: string;
  milestoneId: string;
  delayedDate: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
  milestone: Milestone;
  milestoneTemplates: MilestoneTemplate;
  project: ProjectData;
};
