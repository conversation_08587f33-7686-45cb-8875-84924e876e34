"use client";

import React from "react";
import { useParams, useRouter } from "next/navigation";
import AnalyticsIcon from "../icons/Analytics";
import MilestoneIcon from "../icons/Milestone";
import DailyUpdateIcon from "../icons/DailyUpdate";
import ProductivityTrackerIcon from "../icons/ProuctivityTracker";
import QualityChecklistIcon from "../icons/QualityChecklist";
import SiteDocumentIcon from "../icons/SiteDocument";
import MenuButton from "./MenuButton";
import SidebarAccordion from "../ui/sidebarAccordion";

import DesignIcon from "../icons/DesignIcon";
import PaymentIcon from "../icons/PaymentIcon";
import DesignTeamIcon from "../icons/DesignTeam";
import SettingsIcon from "../icons/SettingsIcon";
import ChatIcon from "../icons/Chat";
import Construction from "../icons/Construction";
// import { getIsTeamMemberFromCookies } from "@/lib/utils";
import { useAuth } from "@/app/contexts/AuthContext";
import useGetProjectById from "@/services/project/getProject";
import {
  Dialog,
  DialogContent,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "../ui/dialog";
import LockIcon from "../icons/Lock";

const ProjectSidebar = () => {
  const params = useParams();
  const projectId = params.id;

  const [showPopup, setShowPopup] = React.useState(false);
  const router = useRouter();

  const { data: project, isPending } = useGetProjectById(projectId as string);

  const { isTeamMember: isTeam, isAdmin } = useAuth();

  if ((isTeam === null && isPending) || !project) {
    return (
      <aside className="w-[270px] rounded-xl border flex flex-col items-center justify-center p-4">
        <span className="text-sm text-muted-foreground">Loading...</span>
      </aside>
    );
  }

  const projectScope = project?.projectScope ?? "both";
  const showDesign = projectScope === "design" || projectScope === "both";
  const showConstruction =
    projectScope === "construction" || projectScope === "both";

  const handleConstructionClick = (e: React.MouseEvent<HTMLElement>) => {
    if (!project.contractorOrg) {
      e.preventDefault?.();
      setShowPopup(true);
    }
  };

  return (
    <>
      <aside className="w-[270px] rounded-xl border flex flex-col p-3.5 gap-y-2 transition-all">
        {(!isTeam || isAdmin) && (
          <MenuButton path={`/projects/${projectId}/analytics`}>
            <MenuButton.IconWrapper>
              <AnalyticsIcon />
            </MenuButton.IconWrapper>
            <MenuButton.Text className="font-semibold">
              Analytics
            </MenuButton.Text>
          </MenuButton>
        )}

        {showDesign && (
          <SidebarAccordion title="Design" icon={<DesignIcon />}>
            <MenuButton path={`/projects/${projectId}/design-stage`}>
              <MenuButton.IconWrapper>
                <MilestoneIcon />
              </MenuButton.IconWrapper>
              <MenuButton.Text>Design Stage</MenuButton.Text>
            </MenuButton>
            {(!isTeam || isAdmin) && (
              <MenuButton path={`/projects/${projectId}/payment-schedules`}>
                <MenuButton.IconWrapper>
                  <PaymentIcon />
                </MenuButton.IconWrapper>
                <MenuButton.Text>Payment Schedules</MenuButton.Text>
              </MenuButton>
            )}

            <MenuButton path={`/projects/${projectId}/design-discussions`}>
              <MenuButton.IconWrapper>
                <ChatIcon />
              </MenuButton.IconWrapper>
              <MenuButton.Text>Design Discussion</MenuButton.Text>
            </MenuButton>
            <MenuButton path={`/projects/${projectId}/design-team`}>
              <MenuButton.IconWrapper>
                <DesignTeamIcon />
              </MenuButton.IconWrapper>
              <MenuButton.Text>Design team</MenuButton.Text>
            </MenuButton>
            {(!isTeam || isAdmin) && (
              <MenuButton path={`/projects/${projectId}/architect-timesheet`}>
                <MenuButton.IconWrapper>
                  <DailyUpdateIcon />
                </MenuButton.IconWrapper>
                <MenuButton.Text>Architect timesheet</MenuButton.Text>
              </MenuButton>
            )}
          </SidebarAccordion>
        )}

        {showConstruction && (
          <SidebarAccordion title="Construction" icon={<Construction />}>
            {(!isTeam || isAdmin) && (
              <MenuButton
                path={`/projects/${projectId}/milestones`}
                onClick={(e) => handleConstructionClick(e)}
              >
                <MenuButton.IconWrapper>
                  <MilestoneIcon />
                </MenuButton.IconWrapper>
                <MenuButton.Text>Milestones</MenuButton.Text>
              </MenuButton>
            )}

            <MenuButton
              path={`/projects/${projectId}/construction-discussions`}
              onClick={(e) => handleConstructionClick(e)}
            >
              <MenuButton.IconWrapper>
                <ChatIcon />
              </MenuButton.IconWrapper>
              <MenuButton.Text>Construction Discussion</MenuButton.Text>
            </MenuButton>
            <MenuButton
              path={`/projects/${projectId}/site-documents`}
              onClick={(e) => handleConstructionClick(e)}
            >
              <MenuButton.IconWrapper>
                <SiteDocumentIcon />
              </MenuButton.IconWrapper>
              <MenuButton.Text>Site Documents</MenuButton.Text>
            </MenuButton>
            <MenuButton
              path={`/projects/${projectId}/quality-checklist`}
              onClick={(e) => handleConstructionClick(e)}
            >
              <MenuButton.IconWrapper>
                <QualityChecklistIcon />
              </MenuButton.IconWrapper>
              <MenuButton.Text>Quality Checklist</MenuButton.Text>
            </MenuButton>
            <MenuButton
              path={`/projects/${projectId}/productivity-database`}
              onClick={(e) => handleConstructionClick(e)}
            >
              <MenuButton.IconWrapper>
                <ProductivityTrackerIcon />
              </MenuButton.IconWrapper>
              <MenuButton.Text>Productivity Database</MenuButton.Text>
            </MenuButton>
            <MenuButton
              path={`/projects/${projectId}/daily-updates`}
              onClick={(e) => handleConstructionClick(e)}
            >
              <MenuButton.IconWrapper>
                <DailyUpdateIcon />
              </MenuButton.IconWrapper>
              <MenuButton.Text>Daily Update</MenuButton.Text>
            </MenuButton>
          </SidebarAccordion>
        )}

        <div className="mt-auto font-semibold">
          <MenuButton path={`/projects/${projectId}/project-details`}>
            <MenuButton.IconWrapper>
              <SettingsIcon />
            </MenuButton.IconWrapper>
            <MenuButton.Text>Project Settings</MenuButton.Text>
          </MenuButton>
        </div>
      </aside>

      <Dialog open={showPopup} onOpenChange={setShowPopup}>
        <DialogContent className="p-[24px] sm:max-w-[402px] w-full sm:rounded-[12px]">
          <div className="w-full items-center flex flex-col justify-center text-center mb-[20px]">
            <LockIcon />
            <h3 className="text-[20px] font-[600] mt-[12px] mb-[8px] leading-[-0.3px]">
              Construction features are locked.
            </h3>
            <p className="text-[14px] font-[400] text-[#474747]">
              Add a <span className="font-[700]">contractor</span> in your
              project settings to get started.
            </p>{" "}
          </div>
          <DialogFooter>
            <button
              className="w-full bg-[#E2E2E2] text-[#6B6B6B] rounded-[8px] text-[14px] font-[600] py-[12px]"
              onClick={() => {
                setShowPopup(false);
              }}
            >
              Cancel
            </button>
            <button
              className="w-full bg-[#2F80ED] rounded-[8px] text-[14px] font-[600] py-[12px] text-[#FFFFFF]"
              onClick={() => {
                setShowPopup(false);
                router.push(`/projects/${projectId}/project-details`);
              }}
            >
              Go to project settings
            </button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default ProjectSidebar;
