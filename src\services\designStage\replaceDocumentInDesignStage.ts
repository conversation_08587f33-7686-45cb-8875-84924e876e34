import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import api from "@/lib/api-client";

type replaceDocumentToDesignStage = {
  stageId: string;
  s3Link: string;
  documentId: string;
  filename: string;
};

const replaceDocumentToDesignStage = async (
  body: replaceDocumentToDesignStage,
) => {
  const response = await api({
    url: "/design-stage/replace-document",
    method: "PUT",
    data: body,
  });

  return response.data;
};

const useReplaceDocumentToDesignStage = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: replaceDocumentToDesignStage,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["design-stage"] });
      queryClient.invalidateQueries({
        queryKey: ["design-stage-document-history"],
      });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message ||
          "Failed to replace document to design stage",
      );
    },
  });
};

export default useReplaceDocumentToDesignStage;
