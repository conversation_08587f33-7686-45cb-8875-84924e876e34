import { z } from "zod";

export const checklistSchema = z.object({
  checklist: z.string({ required_error: "" }).min(1, { message: "" }),
  workType: z.string({ required_error: "" }).min(1, { message: "" }),
  milestoneTemplateId: z.string({ required_error: "" }).min(1, { message: "" }),
});

export const checklistItemSchema = z.object({
  question: z.string({ required_error: "" }).min(1, { message: "" }),
});
