"use client";

import { ReactNode } from "react";
import { AuthProvider } from "@/app/contexts/AuthContext";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { Toaster } from "@/components/ui/toast";
import { queryConfig } from "@/lib/react-query";

interface RootLayoutClientProps {
  children: ReactNode;
}

export const queryClient = new QueryClient({ defaultOptions: queryConfig });

export default function RootLayoutClient({ children }: RootLayoutClientProps) {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthProvider>
        {children}
        <Toaster />
      </AuthProvider>
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}
