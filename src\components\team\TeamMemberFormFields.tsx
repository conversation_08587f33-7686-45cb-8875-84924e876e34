import { UseFormReturn } from "react-hook-form";
import { z } from "zod";

import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { teamMemberSchema } from "@/schema/teamMember";

type TeamMemberFormFieldsProps = {
  form: UseFormReturn<z.infer<typeof teamMemberSchema>>;
  isEdit?: boolean;
};

const TeamMemberFormFields = ({
  form,
  isEdit = false,
}: TeamMemberFormFieldsProps) => {
  return (
    <>
      <FormField
        control={form.control}
        name="name"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Team member name</FormLabel>
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="role"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Role</FormLabel>
            <FormControl>
              <Input {...field} />
            </FormControl>
            <FormMessage />
            {/* <Select onValueChange={field.onChange} value={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
              </FormControl>
              <SelectContent
                hideScrollDownButton
                position="popper"
                className="max-h-52 max-w-[--radix-select-trigger-width]"
              >
                <SelectItem value="sd">sd</SelectItem>
              </SelectContent>
            </Select> */}
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="salary"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Salary</FormLabel>
            <FormControl>
              <Input type="number" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="phone"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Phone</FormLabel>
            <FormControl>
              <Input type="number" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="email"
        render={({ field }) => (
          <FormItem>
            <FormLabel>Email</FormLabel>
            <FormControl>
              <Input {...field} disabled={isEdit} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />

      <FormField
        control={form.control}
        name="isAdmin"
        render={({ field }) => (
          <FormItem className="flex items-center justify-between w-full mt-[28px]">
            <div className="label-container">
              <FormLabel className="text-[#1E1E1E] text-[14px] font-[500]">
                Access admin features
              </FormLabel>
              <p className="text-[#49515B]">
                Allow this user to access all the admin features
              </p>
            </div>
            <FormControl>
              <label className="switch">
                <input
                  type="checkbox"
                  checked={field.value}
                  onChange={(e) => field.onChange(e.target.checked)}
                />
                <span className="slider" />
              </label>
            </FormControl>
          </FormItem>
        )}
      />

      {/* <div className="flex items-center justify-between w-full">
        <div>
          <h6 className="text-[14px] font-[500] text-[#1E1E1E]">
            Access admin features
          </h6>
          <p className="text-[#49515B] text-[14px] font-[400] mt-[8px]">
            Allow this user to access all the admin features
          </p>
        </div>
        toggle
      </div> */}
    </>
  );
};

export default TeamMemberFormFields;
