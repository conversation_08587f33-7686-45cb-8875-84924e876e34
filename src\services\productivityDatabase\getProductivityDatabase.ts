import { useQuery } from "@tanstack/react-query";

import api from "@/lib/api-client";
import { ProductivityDatabase } from "@/types/ProductivityDatabase";

type getProductivityDatabase = { projectId: string };

const getProductivityTracker = async ({
  projectId,
}: getProductivityDatabase): Promise<ProductivityDatabase[]> => {
  const response = await api.get(`/productivity-tracker/${projectId}`);
  return response.data;
};

type UsegetProductivityDatabase = getProductivityDatabase;

const useGetProductivityDatabase = ({
  projectId,
}: UsegetProductivityDatabase) => {
  return useQuery({
    queryKey: ["productivity-database", projectId],
    queryFn: async () => getProductivityTracker({ projectId }),
  });
};

export default useGetProductivityDatabase;
