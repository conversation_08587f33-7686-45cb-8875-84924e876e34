import { z } from "zod";

import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { designTeamSchema } from "@/schema/designTeam";

type RemoveDesignTeamMember = Omit<z.infer<typeof designTeamSchema>, "role">;

const removeDesignTeamMember = async ({
  projectId,
  userId,
}: RemoveDesignTeamMember) => {
  const response = await api.delete(
    `/design-team/remove-design-team/${projectId}/${userId}`,
  );
  return response.data;
};

const useRemoveDesignTeamMember = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: removeDesignTeamMember,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["design-team"] });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to remove design team",
      );
    },
  });
};

export default useRemoveDesignTeamMember;
