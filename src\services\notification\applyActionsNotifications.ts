import { toast } from "sonner";

import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";

type ApplyActionsNotifications = {
  notificationId: string;
  action: "collect_after" | "collect_now";
};

const applyActionsNotifications = async ({
  notificationId,
  action,
}: ApplyActionsNotifications) => {
  const response = await api({
    url: `/notifications/actions/${notificationId}`,
    method: "PUT",
    data: { action },
  });
  return response.data;
};

const useApplyActionsNotifications = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: applyActionsNotifications,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["notifications"] });
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to mark notification as read",
      );
    },
  });
};

export default useApplyActionsNotifications;
