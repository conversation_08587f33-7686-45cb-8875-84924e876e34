import { useState } from "react";

import {
  AlertDial<PERSON>,
  AlertDialogTrigger,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alertDialog";
import { Button } from "@/components/ui/button";
import DeleteIcon from "@/components/icons/Delete";
import useDeleteItemFromChecklist from "@/services/qualityChecklist/deleteItemFromChecklist";
import { toast } from "sonner";

type DeleteItemFromChecklistProps = {
  id: string;
  projectId: string;
  checklistId: string;
};

const DeleteItemFromChecklist = ({
  id,
  projectId,
  checklistId,
}: DeleteItemFromChecklistProps) => {
  const [isOpen, setIsOpen] = useState(false);

  const { mutate, isPending } = useDeleteItemFromChecklist(() => {
    setIsO<PERSON>(false);
    toast.success("Item deleted successfully");
  });

  const handleDeleteItemFromChecklist = () => {
    mutate({ questionId: id, projectId, qualityChecklistId: checklistId });
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger asChild>
        <button>
          <DeleteIcon />
        </button>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete item?</AlertDialogTitle>
          <AlertDialogDescription>
            Doing this will remove the all the data within this checklist
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button loading={isPending} onClick={handleDeleteItemFromChecklist}>
              Yes, Delete
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DeleteItemFromChecklist;
