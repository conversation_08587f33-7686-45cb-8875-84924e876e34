import { useInfiniteQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { DateRange } from "react-day-picker";
import { DailyUpdatesResponse } from "@/types/DailyUpdates";
import api from "@/lib/api-client";

type GetDailyUpdatesParams = {
  projectId: string;
  page?: number;
  limit?: number;
  dateFrom?: string;
  dateTo?: string;
};

const ITEMS_PER_PAGE = 20;

const getDailyUpdates = async ({
  projectId,
  page = 1,
  limit = ITEMS_PER_PAGE,
  dateFrom,
  dateTo,
}: GetDailyUpdatesParams): Promise<DailyUpdatesResponse> => {
  const params = new URLSearchParams({
    page: page.toString(),
    limit: limit.toString(),
    ...(dateFrom && { dateFrom }),
    ...(dateTo && { dateTo }),
  });
  return await api.get(`/daily-updates/${projectId}?${params}`);
};

const useGetDailyUpdates = (
  projectId: string,
  dateRange?: DateRange | undefined,
) => {
  return useInfiniteQuery({
    queryKey: ["daily-updates", projectId, dateRange?.from, dateRange?.to],
    queryFn: ({ pageParam = 1 }) =>
      getDailyUpdates({
        projectId,
        page: pageParam,
        limit: ITEMS_PER_PAGE,
        dateFrom: dateRange?.from
          ? format(dateRange.from, "yyyy-MM-dd")
          : undefined,
        dateTo: dateRange?.to ? format(dateRange.to, "yyyy-MM-dd") : undefined,
      }),
    getNextPageParam: (lastPage, allPages) => {
      const totalPages = Math.ceil(lastPage.totalCount / ITEMS_PER_PAGE);
      const nextPage = allPages.length + 1;
      return nextPage <= totalPages ? nextPage : undefined;
    },
    initialPageParam: 1,
    enabled: !!projectId,
  });
};

export default useGetDailyUpdates;
