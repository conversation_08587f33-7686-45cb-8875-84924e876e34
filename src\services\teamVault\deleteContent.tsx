import { toast } from "sonner";
import { useMutation } from "@tanstack/react-query";

import api from "@/lib/api-client";

const deleteContentFromTeamVault = async (itemId: string) => {
  const { data } = await api({
    url: `/vault/item/${itemId}`,
    method: "DELETE",
  });
  return data;
};

const useDeleteContentFromTeamVault = (onSuccess?: () => void) => {
  return useMutation({
    mutationFn: deleteContentFromTeamVault,
    onSuccess: () => {
      onSuccess?.();
      toast.success("Content deleted successfully");
    },
    onError: () => {
      toast.error("Failed to delete content from team vault");
    },
  });
};

export default useDeleteContentFromTeamVault;
