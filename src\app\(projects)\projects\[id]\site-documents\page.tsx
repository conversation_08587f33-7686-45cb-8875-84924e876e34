"use client";

import React, { useState } from "react";
import PageHeader from "@/components/layout/pageHeader/PageHeader";
import { Button } from "@/components/ui/button";
import { useParams } from "next/navigation";
import { useSiteDocuments } from "@/services/project/sitedocs-hooks";
import useGetProjectById from "@/services/project/getProject";
import NoDataToShow from "@/components/ui/noDataToShow";
import DocumentsTable from "@/components/sitedocs/SiteDocumentTable";
import UploadDocumentDialog from "@/components/sitedocs/UploadDocument";
import Loader from "@/components/ui/loader";
import ErrorText from "@/components/ui/errortext";
import { Plus } from "lucide-react";

const SiteDocumentsPage = () => {
  const { id: profileId } = useParams();
  const {
    data: documentsData,
    isLoading,
    error,
    refetch,
  } = useSiteDocuments(profileId as string);
  const { data: projectData } = useGetProjectById(profileId as string);
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);

  if (isLoading) {
    return <Loader />;
  }

  if (error) {
    return <ErrorText entity="site documents" />;
  }

  const documents = Array.isArray(documentsData) ? documentsData : [];

  return (
    <div className="flex flex-col gap-y-6">
      <div className="flex justify-between items-center">
        <div className="space-y-1.5 text-neutrals-G800">
          <h4 className="font-semibold"> Site Documents</h4>
          <p className="text-sm">
            Manage all documents related to this project
          </p>
        </div>
        <Button
          className="pl-4 py-2 pr-3 gap-x-2"
          onClick={() => setIsUploadDialogOpen(true)}
        >
          Upload Documents <Plus className="size-[22px] stroke-[2.5px]" />
        </Button>
      </div>

      <div className="flex-grow overflow-auto scrollbar-hide">
        {documents && documents.length > 0 ? (
          documents.map((categoryData) =>
            categoryData.files && categoryData.files.length > 0 ? (
              <DocumentsTable
                key={categoryData.category}
                title={categoryData.category}
                documents={categoryData.files}
                profileId={profileId as string}
                onEdit={refetch}
              />
            ) : null,
          )
        ) : (
          <NoDataToShow />
        )}
      </div>

      <UploadDocumentDialog
        isOpen={isUploadDialogOpen}
        onClose={() => {
          setIsUploadDialogOpen(false);
          refetch();
        }}
        profileId={profileId as string}
      />
    </div>
  );
};

export default SiteDocumentsPage;
