import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { buttonVariants } from "../ui/button";
import Calendar from "../icons/Calendar";

const months = [
  "january",
  "february",
  "march",
  "april",
  "may",
  "june",
  "july",
  "august",
  "september",
  "october",
  "november",
  "december",
];

const MonthPicker = () => {
  return (
    <Select>
      <SelectTrigger
        className={buttonVariants({
          variant: "outline",
          className:
            "capitalize border-primary-blue-B900/[37%] w-auto min-w-[153px] font-normal",
        })}
      >
        <div className="flex gap-x-1.5 items-center">
          <Calendar className="text-primary-blue-B900" />
          <SelectValue placeholder="Select a month" />
        </div>
      </SelectTrigger>
      <SelectContent>
        <SelectGroup>
          {months.map((month) => (
            <SelectItem key={month} value={month} className="capitalize">
              {month}
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
};

export default MonthPicker;
