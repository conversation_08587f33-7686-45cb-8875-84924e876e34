import { useState } from "react";
import { toast } from "sonner";
import {
  <PERSON>ert<PERSON><PERSON>og,
  AlertDialog<PERSON><PERSON><PERSON>,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogCancel,
  AlertDialogAction,
} from "@/components/ui/alertDialog";
import { useParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import useDeleteMilestone from "@/services/milestone/deleteMilestone";
import { Milestone } from "@/types/Milestone";

type DeleteMilestoneProps = {
  milestone: Milestone;
  onSuccess?: () => void;
};

const DeleteMilestone = ({ milestone, onSuccess }: DeleteMilestoneProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const { id } = useParams() as { id: string };

  const handleDeleteMilestoneSuccess = () => {
    setIsOpen(false);
    toast.success("Milestone deleted successfully");
    onSuccess?.();
  };

  const { mutate: deleteMilestone, isPending: isPendingDeleteMilestone } =
    useDeleteMilestone(handleDeleteMilestoneSuccess);

  const handleDeleteMilestone = () => {
    deleteMilestone({ milestoneId: milestone._id, projectId: id });
  };

  const displayName =
    milestone.milestoneName || milestone.milestoneTemplateId?.name;

  return (
    <AlertDialog open={isOpen} onOpenChange={setIsOpen}>
      <AlertDialogTrigger className="gap-x-2 items-center flex pr-3 pl-4 py-2 text-sm font-semibold text-[#CC0000] bg-[#F8DEDE] rounded-[8px]">
        Delete{" "}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="18"
          height="19"
          viewBox="0 0 18 19"
          fill="none"
        >
          <path
            d="M15.2222 4.25C15.4285 4.25 15.6263 4.32902 15.7722 4.46967C15.9181 4.61032 16 4.80109 16 5C16 5.19891 15.9181 5.38968 15.7722 5.53033C15.6263 5.67098 15.4285 5.75 15.2222 5.75H14.4444L14.4421 5.80325L13.7164 15.6065C13.6885 15.9849 13.5129 16.3391 13.225 16.5977C12.9371 16.8563 12.5582 17 12.1648 17H5.83444C5.441 17 5.06216 16.8563 4.77424 16.5977C4.48632 16.3391 4.31071 15.9849 4.28278 15.6065L3.55711 5.804L3.55556 5.75H2.77778C2.5715 5.75 2.37367 5.67098 2.22781 5.53033C2.08194 5.38968 2 5.19891 2 5C2 4.80109 2.08194 4.61032 2.22781 4.46967C2.37367 4.32902 2.5715 4.25 2.77778 4.25H15.2222ZM12.8866 5.75H5.11344L5.83522 15.5H12.1648L12.8866 5.75ZM10.5556 2C10.7618 2 10.9597 2.07902 11.1055 2.21967C11.2514 2.36032 11.3333 2.55109 11.3333 2.75C11.3333 2.94891 11.2514 3.13968 11.1055 3.28033C10.9597 3.42098 10.7618 3.5 10.5556 3.5H7.44444C7.23816 3.5 7.04033 3.42098 6.89447 3.28033C6.74861 3.13968 6.66667 2.94891 6.66667 2.75C6.66667 2.55109 6.74861 2.36032 6.89447 2.21967C7.04033 2.07902 7.23816 2 7.44444 2H10.5556Z"
            fill="#CC0000"
          />
        </svg>
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Milestone?</AlertDialogTitle>
          <AlertDialogDescription>
            Doing this will remove all the data you entered for the milestone{" "}
            <span className="text-neutrals-G900 font-medium">
              &apos;{displayName}&apos;.
            </span>
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button
              onClick={handleDeleteMilestone}
              loading={isPendingDeleteMilestone}
            >
              Yes, Remove
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

export default DeleteMilestone;
