"use client";
import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useInView } from "react-intersection-observer";

import Search from "@/components/icons/Search";
import PageHeader from "@/components/layout/pageHeader/PageHeader";
import AddTeamMember from "@/components/team/AddTeamMember";
import TeamCard from "@/components/team/TeamCard";
import { Input } from "@/components/ui/input";
import useGetTeamMembers from "@/services/teamMember/getTeamMembers";
import Loader from "@/components/ui/loader";
import ErrorText from "@/components/ui/errortext";
import NoDataToShow from "@/components/ui/noDataToShow";
import withTeamGuard from "@/components/TeamGuard";

const TeamPage = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const searchTerm = searchParams.get("search") || "";

  const { ref, inView } = useInView();

  const [search, setSearch] = useState(searchTerm);

  const handleSearch = () => {
    if (!search && searchParams) {
      return router.push("/team");
    }

    router.push(`/team?search=${search}`);
  };

  const {
    data,
    isPending,
    isError,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useGetTeamMembers({ search: searchTerm });

  useEffect(() => {
    if (inView && hasNextPage && !isFetchingNextPage) {
      fetchNextPage();
    }
  }, [inView, fetchNextPage, hasNextPage, isFetchingNextPage]);

  if (isPending) return <Loader />;
  if (isError) return <ErrorText entity="team members" />;

  const teamMembers = data.pages.flatMap((page) => page.data) ?? [];

  return (
    <div className="flex flex-col h-screen">
      <PageHeader>
        <div>
          <PageHeader.Heading>Team</PageHeader.Heading>
          <PageHeader.Description>Manage your team </PageHeader.Description>
        </div>
        <AddTeamMember />
      </PageHeader>
      <div className="flex-1 overflow-auto p-5 scrollbar-hide">
        <form
          onSubmit={(e) => {
            e.preventDefault();
            handleSearch();
          }}
          className="flex w-fit bg-neutrals-G40 rounded-lg overflow-hidden mb-6"
        >
          <Input
            type="search"
            placeholder="Search by name"
            className="w-1/4 min-w-[297px] h-[2.25rem] border-none bg-inherit placeholder:text-neutrals-G400 placeholder:text-sm placeholder:font-normal"
            value={search}
            onChange={(e) => setSearch(e.currentTarget.value)}
          />
          <button className="pr-3 py-2">
            <Search />
          </button>
        </form>
        {teamMembers.length === 0 ? (
          <NoDataToShow />
        ) : (
          <>
            <div className="grid grid-cols-3 gap-4">
              {teamMembers.map((member) => (
                <TeamCard key={member._id} data={member} />
              ))}
            </div>
            <div
              ref={ref}
              className="w-full h-20 flex items-center justify-center"
            >
              {isFetchingNextPage && <Loader size={10} />}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default withTeamGuard(TeamPage);
