import { useQuery } from "@tanstack/react-query";

import api from "@/lib/api-client";
import { WorktypeTemplate } from "@/types/Worktype";

type GetWorktypeTemplate = {
  projectId: string;
  milestoneTemplateId: string;
};

const getWorktypeTemplate = async ({
  projectId,
  milestoneTemplateId,
}: GetWorktypeTemplate): Promise<WorktypeTemplate[]> => {
  const response = await api.get(
    `/worktypes/templates/${projectId}/${milestoneTemplateId}`,
  );
  return response.data;
};

type UseGetWorktypeTemplate = GetWorktypeTemplate;

const useGetWorktypeTemplate = (args: UseGetWorktypeTemplate) => {
  return useQuery({
    queryKey: ["worktype-templates", args.projectId, args.milestoneTemplateId],
    queryFn: async () => await getWorktypeTemplate(args),
    enabled: !!args.projectId && !!args.milestoneTemplateId,
  });
};

export default useGetWorktypeTemplate;
