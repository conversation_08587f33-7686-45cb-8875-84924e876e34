import { useQueryClient } from "@tanstack/react-query";
import Cookies from "js-cookie";
import { signOut } from "firebase/auth";
import { useRouter } from "next/navigation";

import { auth } from "@/app/firebase";

const useSignoutUser = () => {
  const queryClient = useQueryClient();
  const router = useRouter();

  const handleSignout = async () => {
    try {
      await signOut(auth);
      Cookies.remove("token");
      // Cookies.remove("isTeamMember");
      queryClient.removeQueries();
      router.push("/login");
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };
  return { handleSignout };
};

export default useSignoutUser;
