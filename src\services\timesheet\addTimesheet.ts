import { toast } from "sonner";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { z } from "zod";

import api from "@/lib/api-client";
import { timesheetSchema } from "@/schema/timesheet";

const addTimesheet = async (body: z.infer<typeof timesheetSchema>) => {
  const response = await api({
    url: "/time-sheet/add",
    method: "POST",
    data: body,
  });
  return response.data;
};

const useAddTimesheet = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: addTimesheet,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["time-sheet"] });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || "Failed to add timesheet");
    },
  });
};

export default useAddTimesheet;
