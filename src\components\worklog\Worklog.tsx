import { format, isToday } from "date-fns";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import File3 from "../icons/File3";
import {
  Di<PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>T<PERSON>le,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Textarea } from "../ui/textarea";
import TestIcon from "../icons/test1";

// import { ArchitectWorklog } from "@/types/ArchitectWorklog";

type WorklogProps = {
  // data: ArchitectWorklog;
  data: any;
};

const Worklog = ({ data }: WorklogProps) => {
  console.log(data, "data array");
  return (
    <div className="p-3 bg-white rounded-xl border border-border-gray1 flex flex-col">
      <div className="px-3.5 py-2.5 border-b border-[#e6e6e6] flex items-center justify-between gap-x-3">
        <h3 className="text-neutrals-G900 text-xl font-semibold">
          {isToday(data?.date) ? "Today," : ""}{" "}
          {format(data?.date, "dd MMM yyyy")}
        </h3>
      </div>
      <Table>
        <TableHeader className="sticky top-0">
          <TableRow className="*:h-auto *:pb-3 *:pt-4">
            <TableHead className="w-1/6">SI</TableHead>
            <TableHead>Architect Name</TableHead>
            <TableHead>Stages</TableHead>
            <TableHead>Work hours</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data?.entries?.map((entry: any, index: number) => (
            <TableRow key={index} className="*:py-3.5">
              <TableCell className="font-medium">{++index}</TableCell>
              <TableCell>{entry.architectName}</TableCell>
              <TableCell>
                <Dialog>
                  <DialogTrigger>
                    {entry.stages[0]?.stageName}{" "}
                    {entry.stages.length > 1 && (
                      <>
                        &{" "}
                        <span className="text-primary-blue-B900 font-bold">
                          {entry.stages.length - 1} more
                        </span>
                      </>
                    )}
                  </DialogTrigger>
                  <DialogContent
                    isCloseIconVisible
                    className="text-neutrals-G900 gap-y-6 sm:max-w-[40%] p-0"
                  >
                    <DialogHeader className="px-6 pt-6">
                      <DialogTitle>Assigned stages</DialogTitle>
                    </DialogHeader>
                    <div className="px-2.5 pb-6">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="w-1/3 capitalize">
                              Stage
                            </TableHead>
                            <TableHead className="w-1/4 capitalize">
                              Hours
                            </TableHead>
                            <TableHead className="w-2/3 capitalize">
                              Description
                            </TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {entry?.stages?.map((stage: any, index: number) => (
                            <TableRow key={index} className="hover:bg-gray-50">
                              <TableCell>{stage?.stageName}</TableCell>
                              <TableCell>{stage?.workHours}</TableCell>
                              <TableCell>{stage?.description[0]}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                  </DialogContent>
                </Dialog>
              </TableCell>
              <TableCell className="flex justify-between">
                {entry?.totalWorkHours}
                <Dialog>
                  <DialogTrigger className="w-[16px]">
                    <TestIcon />
                  </DialogTrigger>
                  <DialogContent
                    isCloseIconVisible={true}
                    className="text-neutrals-G900 gap-y-6 sm:max-w-[40%] p-0"
                  >
                    <DialogHeader className="px-6 pt-6">
                      <DialogTitle>Assigned stages</DialogTitle>
                    </DialogHeader>
                    <div className="px-2.5 pb-6">
                      <Table>
                        <TableHeader>
                          <TableRow>
                            <TableHead className="w-1/3 capitalize">
                              Stage
                            </TableHead>
                            <TableHead className="w-1/4 capitalize">
                              Hours
                            </TableHead>
                            <TableHead className="w-2/3 capitalize">
                              Description
                            </TableHead>
                          </TableRow>
                        </TableHeader>
                        <TableBody>
                          {entry?.stages?.map((stage: any, index: number) => (
                            <TableRow key={index} className="hover:bg-gray-50">
                              <TableCell>{stage?.stageName}</TableCell>
                              <TableCell>{stage?.workHours}</TableCell>
                              <TableCell>{stage?.description[0]}</TableCell>
                            </TableRow>
                          ))}
                        </TableBody>
                      </Table>
                    </div>
                    {/* <DialogHeader className="font-semibold text-xl">
                      Note
                    </DialogHeader>
                    <Textarea
                      className="resize-none h-[135px] shadow-none disabled:text-[#1d2836]"
                      value={entry.description}
                    /> */}
                  </DialogContent>
                </Dialog>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default Worklog;
