import { UseFormReturn, useWatch } from "react-hook-form";
import { z } from "zod";
import { ComponentProps, useCallback, useEffect, useRef } from "react";

import { milestoneSchema } from "@/schema/milestone";
import { FormField } from "@/components/ui/form";
import { calculateDurationFromStartAndEndDate } from "@/lib/utils";

// type DurationProps = Pick<ComponentProps<typeof FormField>, "name"> & {
//   form: UseFormReturn<z.infer<typeof milestoneSchema>>;
//   startDateFieldName: string;
//   endDateFieldName: string;
// };

// // TODO: update the prop type, such that the component can be resued in other forms
// const Duration = ({
//   form,
//   name,
//   startDateFieldName,
//   endDateFieldName,
// }: DurationProps) => {
//   const [startDate, endDate] = useWatch({
//     name: [startDateFieldName, endDateFieldName],
//   });

//   const duration = form.watch("duration");

// useEffect(() => {
//   if (startDate && endDate) {
//     form.setValue(
//       // @ts-expect-error TODO: Fix the type
//       name,
//       calculateDurationFromStartAndEndDate(startDate, endDate),
//     );
//   }
// }, [startDate, endDate, form, name]);
//   // return <FormField control={form.control} name={name} {...props} />;

//   return (
// <p
//   className={`text-name-title text-sm text-right ${(!duration || duration < 0) && "hidden"}`}
// >
//   Duration : {`${duration} ${duration === 1 ? "day" : "days"}`}
// </p>
//   );
// };

// export default Duration;

type DurationProps = {
  value: number;
  onChange: (duration: number) => void;
  startDate?: Date;
  endDate?: Date;
};

const Duration = ({ value, onChange, startDate, endDate }: DurationProps) => {
  // Store the latest onChange in a ref
  const lastCalculatedDuration = useRef<number>(value);

  useEffect(() => {
    if (startDate && endDate) {
      const newDuration = calculateDurationFromStartAndEndDate(
        startDate,
        endDate,
      );

      // Only call onChange if the duration has actually changed
      if (lastCalculatedDuration.current !== newDuration) {
        lastCalculatedDuration.current = newDuration;
        onChange(newDuration);
      }
    }
  }, [startDate, endDate, onChange]);

  return (
    <p
      className={`text-name-title text-sm text-right ${(!value || value < 0) && "hidden"}`}
    >
      Duration : {`${value} ${value === 1 ? "day" : "days"}`}
    </p>
  );
};

export default Duration;
