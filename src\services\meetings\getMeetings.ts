import { useInfiniteQuery } from "@tanstack/react-query";
import api from "@/lib/api-client";
import { GetMeetingsParams, MeetingsResponse } from "@/types/Meetings";

const fetchMeetings = async ({
  status,
  pageParam = 1,
  limit = 10,
}: GetMeetingsParams & { pageParam?: number }) => {
  const response = await api.get<MeetingsResponse>("/meetings", {
    params: {
      status,
      page: pageParam,
      limit,
    },
  });
  // console.log(response.data);
  return response.data;
};

export const useGetMeetings = ({ status }: GetMeetingsParams) => {
  return useInfiniteQuery({
    queryKey: ["meetings", status],
    queryFn: ({ pageParam }) => fetchMeetings({ status, pageParam }),
    initialPageParam: 1,
    getNextPageParam: (lastPage, allPages) => {
      const nextPage = allPages.length + 1;
      return lastPage.totalCount > allPages.length * lastPage.limit
        ? nextPage
        : undefined;
    },
  });
};
