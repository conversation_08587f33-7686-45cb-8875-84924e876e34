"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Plus } from "lucide-react";
import { toast } from "sonner";

import { timesheetSchema } from "@/schema/timesheet";
import { useSheetFormState } from "@/hooks/useSheetFormState";
import {
  Sheet,
  SheetContent,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from "@/components/ui/sheet";
import { Button } from "../ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "../ui/input";
import { Textarea } from "../ui/textarea";
import { useGetPaginatedProjects } from "../../services/timesheet/getPaginatedProjects";
import { LoadingSpinner } from "../ui/loader";
import { cn } from "@/lib/utils";
import { useGetPaginatedStagesForTimesheet } from "@/services/timesheet/getPaginatedStagesForTimesheet";
import useAddTimesheet from "@/services/timesheet/addTimesheet";

const AddTimesheet = () => {
  const [open, setOpen] = useState(false);

  const form = useForm<z.infer<typeof timesheetSchema>>({
    resolver: zodResolver(timesheetSchema),
    defaultValues: {
      projectId: "",
      stageId: "",
      hour: 0,
      description: "",
    },
  });

  const projectId = form.watch("projectId");

  const {
    data: projects,
    isPending: isPendingProjects,
    isFetchingNextPage: isFetchingNextPageProjects,
    ref: refProjects,
  } = useGetPaginatedProjects();

  const {
    data: stages,
    isPending: isPendingStages,
    isFetchingNextPage: isFetchingNextPageStages,
    ref: refStages,
  } = useGetPaginatedStagesForTimesheet({ projectId, search: "" });

  useEffect(() => {
    if (projectId) {
      form.setValue("stageId", "");
    }
  }, [projectId, form]);

  const { mutate, isPending } = useAddTimesheet(() => {
    setOpen(false);
    toast.success("Timesheet added successfully!");
  });

  const { handleOpenChange, handleExplicitClose } = useSheetFormState({
    form,
    setOpen,
    preserveOnImplicitClose: true,
  });

  const onSubmit = async (values: z.infer<typeof timesheetSchema>) => {
    mutate(values);
  };

  return (
    <Sheet open={open} onOpenChange={handleOpenChange}>
      <SheetTrigger asChild>
        <Button className="gap-2 pl-4 pr-3">
          Add Timesheet
          <Plus className="size-[22px] stroke-[2.5px]" />
        </Button>
      </SheetTrigger>
      <SheetContent
        className="max-w-[507px] flex flex-col gap-0"
        onExplicitClose={handleExplicitClose}
      >
        <SheetHeader>
          <SheetTitle>Add Timesheet</SheetTitle>
        </SheetHeader>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-1 flex-col gap-y-[37px] justify-between"
          >
            <div className="flex flex-1 flex-col gap-y-3">
              <FormField
                control={form.control}
                name="projectId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Project</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value || ""}
                    >
                      {" "}
                      <FormControl>
                        <SelectTrigger
                          loading={isPendingProjects}
                          className="data-[placeholder]:text-name-title text-name-title font-medium"
                        >
                          <SelectValue placeholder="Select">
                            {
                              projects.find(
                                (project) => project._id === field.value,
                              )?.name
                            }
                          </SelectValue>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {projects.length > 0 ? (
                          <>
                            {projects.map((project) => (
                              <SelectItem key={project._id} value={project._id}>
                                {project.name}
                              </SelectItem>
                            ))}
                            <SelectItem
                              ref={refProjects}
                              value={"none"}
                              disabled
                              className={cn(
                                "data-[disabled]:opacity-100 justify-center ",
                                isFetchingNextPageProjects ? "mb-1" : "h-0",
                              )}
                            >
                              {isFetchingNextPageProjects && (
                                <LoadingSpinner
                                  size={4}
                                  className="border-2 "
                                />
                              )}
                            </SelectItem>
                          </>
                        ) : (
                          <SelectItem value="none" disabled>
                            No projects found
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>

                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="stageId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Design Stage</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value || ""}
                    >
                      <FormControl>
                        <SelectTrigger loading={isPendingStages && !!projectId}>
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent
                        hideScrollDownButton
                        position="popper"
                        className="max-h-52 max-w-[--radix-select-trigger-width]"
                      >
                        {!projectId ? (
                          <SelectItem value="none" disabled>
                            Please select a project first
                          </SelectItem>
                        ) : stages.length > 0 ? (
                          <>
                            {stages.map((stage) => (
                              <SelectItem key={stage._id} value={stage._id}>
                                {stage.stageName}
                              </SelectItem>
                            ))}
                            <SelectItem
                              ref={refStages}
                              value={"none"}
                              disabled
                              className={cn(
                                "data-[disabled]:opacity-100 justify-center ",
                                isFetchingNextPageStages ? "mb-1" : "h-0",
                              )}
                            >
                              {isFetchingNextPageStages && (
                                <LoadingSpinner
                                  size={4}
                                  className="border-2 "
                                />
                              )}
                            </SelectItem>
                          </>
                        ) : (
                          <SelectItem value="none" disabled>
                            No stages found
                          </SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="hour"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Work hour</FormLabel>
                    <FormControl>
                      <Input type="number" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem className="flex-1 mt-1 flex gap-y-2 flex-col">
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea className="resize-none flex-1" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <Button className="ml-auto px-4 py-3" loading={isPending}>
              Log work
            </Button>
          </form>
        </Form>
      </SheetContent>
    </Sheet>
  );
};

export default AddTimesheet;
