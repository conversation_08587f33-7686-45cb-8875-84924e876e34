"use client";
import { useState } from "react";
import { DateRange } from "react-day-picker";
import { format, subDays } from "date-fns";
import { useParams } from "next/navigation";

import DateRangePicker from "@/components/ui/dateRangePicker";
import Worklog from "@/components/worklog/Worklog";
import useGetArchitectWorklog from "@/services/architectWorklog/getArchitectWorklog";
import Loader from "@/components/ui/loader";
import ErrorText from "@/components/ui/errortext";
import withTeamGuard from "@/components/TeamGuard";

const defaultRange = {
  // to: subDays(new Date(), 1)
  to: new Date(),
  from: subDays(new Date(), 7),
};

const ArchitectWorklog = () => {
  const id = useParams().id as string;

  const [dateRange, setDateRange] = useState<DateRange | undefined>(
    defaultRange,
  );

  const { data, isPending, isError } = useGetArchitectWorklog({
    projectId: id,
    startDate: dateRange?.from
      ? format(dateRange.from, "yyyy-MM-dd")
      : format(defaultRange.from, "yyyy-MM-dd"),
    endDate: dateRange?.to
      ? format(dateRange.to, "yyyy-MM-dd")
      : format(defaultRange.to, "yyyy-MM-dd"),
    // startDate: dateRange?.from
    //   ? format(dateRange.from, "yyyy-MM-dd")
    //   : format(defaultRange.from, "yyyy-MM-dd"),
    // endDate: dateRange?.to
    //   ? format(dateRange.to, "yyyy-MM-dd")
    //   : format(defaultRange.to, "yyyy-MM-dd"),
  });

  console.log(data, "date data");

  const handleDateRangeChange = (range: DateRange | undefined) => {
    setDateRange(range);
  };

  if (isPending) return <Loader />;
  if (isError) return <ErrorText entity="architect timesheets" />;

  return (
    <div className="flex flex-col gap-y-5">
      <div className="flex justify-between items-center py-3.5 border-b border-neutrals-G40">
        <div className="space-y-1 text-neutrals-G900">
          <h4 className="font-semibold">Daily Worklog</h4>
          <p className="text-xs text-neutrals-G300">
            View all the design team timesheet entry over here.
          </p>
        </div>
        <DateRangePicker
          onChange={handleDateRangeChange}
          defaultValue={dateRange}
        />{" "}
      </div>
      <div className="flex-1 overflow-y-auto scrollbar-hide">
        <div className="space-y-3">
          {data.length === 0 ? (
            <p className="text-center text-sm text-gray-400">
              No worklog entries found for the selected date range.
            </p>
          ) : (
            data.map((worklog) => <Worklog key={worklog._id} data={worklog} />)
          )}

          {/* {data.map((worklog) => (
            <Worklog key={worklog._id} data={worklog} />
          ))} */}
        </div>
      </div>
    </div>
  );
};

export default withTeamGuard(ArchitectWorklog);
