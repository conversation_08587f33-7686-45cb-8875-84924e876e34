import React from "react";

const MeetingsIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="21"
    height="20"
    fill="none"
    viewBox="0 0 21 20"
    className={className}
  >
    <g clipPath="url(#clip0_2704_2788)">
      <path
        fill="currentColor"
        d="M17.167 3.333A1.667 1.667 0 0 1 18.833 5v8.333A1.66 1.66 0 0 1 17.167 15H20.5v1.667H.5V15h3.333a1.667 1.667 0 0 1-1.666-1.667V5a1.66 1.66 0 0 1 1.666-1.667zm0 1.667H3.833v8.333h13.334zM10.5 10c1.842 0 3.333.75 3.333 1.667v.833H7.167v-.833C7.167 10.75 8.658 10 10.5 10m0-4.167a1.667 1.667 0 1 1 0 3.334 1.667 1.667 0 0 1 0-3.334"
      ></path>
    </g>
    <defs>
      <clipPath id="clip0_2704_2788">
        <path fill="#fff" d="M.5 0h20v20H.5z"></path>
      </clipPath>
    </defs>
  </svg>
);

export default MeetingsIcon;
