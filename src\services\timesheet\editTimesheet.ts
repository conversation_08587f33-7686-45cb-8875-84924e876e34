import { toast } from "sonner";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import api from "@/lib/api-client";

const updateTimesheet = async ({
  id,
  body,
}: {
  id: string;
  body: {
    hour: number;
    description: string;
  };
}) => {
  const response = await api({
    url: `/time-sheet/update/${id}`,
    method: "PATCH",
    data: body,
  });
  return response.data;
};

const useUpdateTimesheet = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateTimesheet,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["time-sheet"] });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to update timesheet",
      );
    },
  });
};

export default useUpdateTimesheet;
