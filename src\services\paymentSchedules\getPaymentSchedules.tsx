"use client";

import api from "@/lib/api-client";
import { useQuery } from "@tanstack/react-query";

export type PaymentSchedule = {
  _id: string;
  scheduleName: string;
  invoice: string;
  stageId: string;
  date: string;
  documentsUploaded: number;
  amount: number;
  percentage: number;
  createdAt: string;
  paymentStatus: {
    invoiceUploaded: boolean;
    verifyClientPayment: boolean;
    verifyPayment: boolean;
  };
  stageDetails: {
    stageName: string;
    documentsCount: any;
  };
  createdByDetails: {
    email: string;
    name: string;
  };
};

// type GetPaymentSchedulesResponse = {
//   data: PaymentSchedule[];
//   totalBudget: number;
// };

export const useGetPaymentSchedules = (projectId: string) => {
  return useQuery<any>({
    queryKey: ["paymentSchedules", projectId],
    queryFn: async () => {
      const res = await api.get<any>(`/payment-schedule/get/${projectId}`);
      return res;
    },
    enabled: !!projectId,
  });
};
