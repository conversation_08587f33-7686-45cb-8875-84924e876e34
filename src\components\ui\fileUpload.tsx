"use client";

import { useState, useRef } from "react";
// import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
// import { Input } from "@/components/ui/input";
// import { cn } from "@/lib/utils";
import { FormMessage } from "@/components/ui/form";
import DeleteRedIcon from "../icons/DeleteRed";
import FileIcon from "../icons/File";

type CustomFileInputProps = {
  onFileChange: (file: File | null) => void;
  onFileUpload: () => void;
};

export default function CustomFileInput({
  onFileChange,
  onFileUpload,
}: CustomFileInputProps) {
  const [fileName, setFileName] = useState("");
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFileSize = (file: File): boolean => {
    setError(null);
    return true;
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0] || null;
    if (file) {
      if (validateFileSize(file)) {
        setFileName(file.name);
        onFileChange(file);
        onFileUpload();
      } else {
        setFileName("");
        onFileChange(null);
        event.target.value = "";
      }
    } else {
      setFileName("");
      setError(null);
      onFileChange(null);
    }
  };

  const handleRemoveFile = () => {
    setFileName("");
    setError(null);
    onFileChange(null);
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  // const handleUploadClick = () => {
  //   if (fileName) {
  //     onFileUpload();
  //   } else {
  //     fileInputRef.current?.click();
  //   }
  // };

  return (
    <div className="flex flex-col gap-2">
      <input
        type="file"
        ref={fileInputRef}
        onChange={handleFileChange}
        className="hidden"
        accept=".pdf,.obj"
      />

      {!fileName ? (
        <div
          onClick={() => fileInputRef.current?.click()}
          className="h-[135px] px-3 py-2.5 bg-primary-blue-B30 border-[1px]  border-dashed rounded-md shadow-input  border-primary-blue-B100 flex-col justify-center items-center gap-1 flex cursor-pointer hover:bg-[#e6f0fe]"
        >
          <div className="text-primary-blue-B900 text-base font-medium">
            Click to upload
          </div>
          <div className="flex items-center gap-1 text-xs text-Grey-Dark-Grey">
            <span>Supported formats : PDF, OBJ,</span>
            <span>No file size limit</span>
          </div>
        </div>
      ) : (
        <div className="px-3 py-2.5 bg-white rounded-md shadow-input border border-slate-300 flex justify-between items-center">
          <span className="text-name-title text-sm font-normal truncate inline-flex items-center gap-1">
            <FileIcon className="h-5 w-5" /> {fileName}
          </span>
          <button
            onClick={handleRemoveFile}
            className="text-gray-500 hover:text-gray-700"
          >
            <DeleteRedIcon className="h-5 w-5" />
          </button>
        </div>
      )}

      {error && (
        <FormMessage className="text-destructive text-sm">{error}</FormMessage>
      )}
    </div>
  );
}
