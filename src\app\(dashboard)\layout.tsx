"use client";

import { useEffect } from "react";

import Sidebar from "../../components/dashboard/Sidebar";
import ProtectedRoutes from "@/components/ProtectedRoutes";
import { socket } from "@/lib/socket";

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex h-screen">
      <Sidebar />
      <main className="flex-1 overflow-y-auto">
        <ProtectedRoutes>{children}</ProtectedRoutes>
      </main>
    </div>
  );
}
