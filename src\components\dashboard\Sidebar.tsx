"use client";

import React, { useState } from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import Image from "next/image";

import AnalyticsIcon from "../icons/Analytics";
import ProjectsIcon from "../icons/Projects";
import ContractorsIcon from "../icons/Contractors";
import { Button } from "../ui/button";
import Logout from "../icons/Logout";
import useSignoutUser from "@/services/auth/signoutUser";
import ProfileIcon from "../icons/Profile";
import NotificationIcon from "../icons/Notification";
import NotificationBadge from "../notifications/NotificationBadge";
import NotificationPanelWrapper from "../notifications/NotificationpanelWrapper";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "../ui/alertDialog";

import useGetNotificationCount from "@/services/notification/getNotificationCount";
import NotificationPanel from "../notifications/NotificationPanel";
import MeetingsIcon from "../icons/Meetings";
import TeamVaultIcon from "../icons/TeamVault";
import { cn } from "@/lib/utils";
import TeamIcon from "../icons/TeamIcon";
import Time from "../icons/Time";
import { useAuth } from "@/app/contexts/AuthContext";

const Sidebar = ({ shrink = false }: { shrink?: boolean }) => {
  const [showNotifications, setShowNotifications] = useState(false);
  // const isTeam = getIsTeamMemberFromCookies();

  const pathname = usePathname();
  const { handleSignout } = useSignoutUser();

  const { data } = useGetNotificationCount();
  const { isTeamMember: isTeam, isAdmin } = useAuth();

  if (isTeam === null) return null;

  const handleNotificationClick = () => {
    setShowNotifications(true);
  };

  const isActiveProfile = pathname === "/profile";
  const isActiveTimesheets = pathname === "/timesheets";

  // const navItems = [
  //   { name: "Dashboard", path: "/dashboard", icon: AnalyticsIcon },
  //   { name: "Projects", path: "/projects", icon: ProjectsIcon },
  //   { name: "Contractors", path: "/contractors", icon: ContractorsIcon },
  //   { name: "Team", path: "/team", icon: TeamIcon },
  //   { name: "Meetings", path: "/meetings", icon: MeetingsIcon },
  // ];

  let navItems = [
    { name: "Dashboard", path: "/dashboard", icon: AnalyticsIcon },
    { name: "Projects", path: "/projects", icon: ProjectsIcon },
    { name: "Contractors", path: "/contractors", icon: ContractorsIcon },
    { name: "Team", path: "/team", icon: TeamIcon },
    { name: "Meetings", path: "/meetings", icon: MeetingsIcon },
    { name: "Team Vault", path: "/team-vault", icon: TeamVaultIcon },
  ];

  if (isTeam && !isAdmin) {
    navItems = navItems.filter(
      (item) =>
        item.name !== "Contractors" &&
        item.name !== "Meetings" &&
        item.name !== "Team" &&
        item.name !== "Dashboard",
    );
  }

  const href = isTeam && !isAdmin ? `/projects` : `/dashboard`;

  return (
    <aside
      className={cn(
        "w-[260px] h-screen flex-col justify-between transition-all gap-y-10  inline-flex px-[18px] py-5 border border-[#E6E6E6]",
        shrink && "px-3 w-[69px]",
      )}
    >
      <div
        className={cn(
          "flex flex-col grow overflow-y-auto gap-y-10",
          shrink && "items-center",
        )}
      >
        <Link href={href} className="px-2.5">
          <Image
            src={
              shrink ? "/projectsmateLogoShrink.svg" : "/projectsmatelogo.png"
            }
            alt="Projectsmate"
            width={shrink ? 32 : 164}
            height={shrink ? 34 : 38}
            className={cn("object-contain w-[164px] h-[38px]")}
          />
        </Link>
        <nav className="flex flex-col gap-y-2">
          {navItems.map((item) => {
            const isActive = pathname === item.path;
            return (
              <Link
                key={item.path}
                href={item.path}
                className={cn(
                  "rounded-[8px]",
                  isActive &&
                    "bg-primary-blue-B40 border border-primary-blue-B50",
                  shrink ? "p-2" : "px-3 py-2 items-center gap-2 flex",
                )}
              >
                <item.icon
                  className={cn(
                    "size-[1.25rem] shrink-0",
                    isActive ? "text-primary-blue-B900" : "text-neutrals-G100",
                    shrink && "size-[1.125rem]",
                  )}
                />
                <div
                  className={cn(
                    "text-sm",
                    isActive
                      ? "text-primary-blue-B900 font-semibold"
                      : "text-neutrals-G600",
                    shrink && "hidden",
                  )}
                >
                  {item.name}
                </div>
              </Link>
            );
          })}
        </nav>
      </div>
      <div
        className={cn(
          "border-t-[1px] text-sm border-neutrals-G40 pt-2 flex flex-col gap-y-2",
          shrink && "items-center",
        )}
      >
        <Link
          href="/timesheets"
          className={cn(
            "rounded-[8px]",
            isActiveTimesheets
              ? "bg-primary-blue-B40 border border-primary-blue-B50 font-semibold text-primary-blue-B900"
              : "text-neutrals-G600",
            shrink ? "p-2" : "flex items-center gap-2 px-3 py-2",
          )}
        >
          <Time
            className={cn(
              "shrink-0",
              isActiveTimesheets
                ? "text-primary-blue-B900"
                : "text-neutrals-G100",
              shrink ? "size-[1.125rem]" : "size-[1.25rem]",
            )}
          />
          <div className={cn(shrink && "hidden")}>Timesheets</div>
        </Link>

        <div
          onClick={handleNotificationClick}
          className={cn(
            "cursor-pointer rounded-[8px]",
            showNotifications
              ? "text-primary-blue-B900 font-semibold bg-primary-blue-B40 border border-primary-blue-B50"
              : "text-neutrals-G600",
            shrink ? "p-2" : "px-3 py-2 flex items-center justify-between",
          )}
        >
          <div className={cn(!shrink && "flex gap-2 items-center")}>
            <NotificationIcon
              className={cn(
                showNotifications
                  ? "text-primary-blue-B900"
                  : "text-neutrals-G100",
              )}
            />
            <div className={cn(shrink && "hidden")}>Notifications</div>
          </div>
          <NotificationBadge
            count={data?.unread || 0}
            className={cn(shrink && "hidden")}
          />
        </div>
        <NotificationPanelWrapper
          showNotifications={showNotifications}
          onClose={() => setShowNotifications(false)}
        >
          <NotificationPanel />
        </NotificationPanelWrapper>

        <Link
          href="/profile"
          className={cn(
            "rounded-[8px]",
            isActiveProfile
              ? "bg-primary-blue-B40 border border-primary-blue-B50 font-semibold text-primary-blue-B900"
              : "text-neutrals-G600",
            shrink ? "p-2" : "flex items-center gap-2 px-3 py-2",
          )}
        >
          <ProfileIcon
            className={cn(
              "shrink-0",
              isActiveProfile ? "text-primary-blue-B900" : "text-neutrals-G100",
              shrink ? "size-[1.125rem]" : "size-[1.25rem]",
            )}
          />
          <div className={cn(shrink && "hidden")}>Profile</div>
        </Link>

        <AlertDialog>
          <AlertDialogTrigger asChild>
            <Button
              variant="outline"
              className={cn(
                "w-full rounded-[8px] font-normal text-[#CC0000] hover:text-[#CC0000] hover:bg-[#CC0000]/10",
                shrink ? "p-2" : "px-3 py-2 gap-x-2 justify-start",
              )}
            >
              <Logout className={cn(shrink && "size-[1.125rem]")} />
              <div className={cn(shrink && "hidden")}>Log out</div>
            </Button>
          </AlertDialogTrigger>
          <AlertDialogContent>
            <AlertDialogHeader>
              <AlertDialogTitle>Are you sure?</AlertDialogTitle>
              <AlertDialogDescription>
                Do you wish to log out?
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel>Cancel</AlertDialogCancel>
              <AlertDialogAction asChild>
                <Button onClick={handleSignout}>Log out</Button>
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </div>
    </aside>
  );
};

export default Sidebar;
