import api from "@/lib/api-client";
import { SiteDocumentResponse } from "@/types/SiteDocuments";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import axios from "axios";

export const useGetS3Url = (folder?: string) => {
  const endpoint = folder
    ? `/common/generate-s3-url/${folder}`
    : `/common/generate-s3-url/siteDocuments`;
  return useMutation({
    mutationFn: async ({
      fileName,
      projectId,
      organisationId,
    }: {
      fileName: string;
      projectId?: string;
      organisationId: string;
    }) => {
      const response = await api.post<string>(endpoint, {
        fileName,
        organisationId,
        projectId,
      });
      return response.data;
    },
  });
};
export const useGenerateS3Url = () => {
  return useMutation({
    mutationFn: async ({
      fileName,
      projectId,
    }: {
      fileName: string;
      projectId: string;
    }) => {
      const response = await api.post<string>(
        "/common/generate-s3-url/siteDocuments",
        { fileName, projectId },
      );
      return response.data;
    },
  });
};

export const useUploadToS3 = () => {
  return useMutation({
    mutationFn: async ({
      signedUrl,
      file,
    }: {
      signedUrl: string;
      file: File;
    }) => {
      const response = await axios.put(signedUrl, file, {
        headers: {
          "Content-Type": file.type,
        },
      });
      return response;
    },
  });
};

export const useAddSiteDocument = (profileId: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (data: {
      category: string;
      fileName: string;
      storagePath: string;
      milestoneId?: string;
    }) => {
      const response = await api.post<SiteDocumentResponse>(
        `/projects/${profileId}/documents`,
        data,
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["siteDocuments", profileId],
      });
    },
  });
};

export const useUpdateSiteDocument = (profileId: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async ({
      documentId,
      data,
    }: {
      documentId: string;
      data: {
        category: string;
        fileName: string;
        storagePath: string;
        milestoneId?: string;
      };
    }) => {
      const response = await api.put<SiteDocumentResponse>(
        `/projects/${profileId}/documents/${documentId}`,
        data,
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["siteDocuments", profileId],
      });
    },
  });
};

export const useDeleteSiteDocument = (profileId: string) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: async (documentId: string) => {
      const response = await api.delete<SiteDocumentResponse>(
        `/projects/${profileId}/documents/${documentId}`,
      );
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["siteDocuments", profileId],
      });
    },
  });
};

export const useSiteDocuments = (profileId: string) => {
  return useQuery<SiteDocumentResponse>({
    queryKey: ["siteDocuments", profileId],
    queryFn: async () => {
      try {
        const response = await api.get<SiteDocumentResponse>(
          `/projects/${profileId}/documents`,
        );
        return response.data;
      } catch (error) {
        console.error("Error fetching site documents:", error);
        throw error;
      }
    },
    refetchOnWindowFocus: false,
  });
};
