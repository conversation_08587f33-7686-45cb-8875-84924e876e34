import { ProductivityDatabase } from "@/types/ProductivityDatabase";
import {
  ProductivityCard,
  ProductivityCardHeader,
  ProductivityCardContent,
  ProductivitySection,
} from "../ui/productiviyDatabaseCard";
import ModifyQuantity from "./ModifyQuantity";
import { useParams } from "next/navigation";

type ProductivityDatabaseListProps = {
  data: ProductivityDatabase[];
};

const ProductivityDatabaseList = ({ data }: ProductivityDatabaseListProps) => {
  const { id } = useParams() as { id: string };
  function formatNumber(value: number | string) {
    if (value === 0 || value === undefined || value === null) return "NA";
    const num = Number(value);
    return Number.isInteger(num) ? num : num.toFixed(2);
  }

  return (
    <div className="flex flex-col gap-[10px]">
      {data.map((item) => (
        <ProductivitySection key={item._id} title={item.name}>
          {item.worktypes.map((worktype) => (
            <ProductivityCard key={worktype._id}>
              <ProductivityCardHeader
                title={worktype.name}
                editComponent={
                  <ModifyQuantity
                    projectId={id}
                    milestone={{ _id: item._id, name: item.name }}
                    worktype={worktype}
                  />
                }
              />
              <ProductivityCardContent
                standardUnit={{
                  value: `${formatNumber(worktype.standardUnits[0]?.minQuantity)} - ${formatNumber(worktype.standardUnits[0]?.maxQuantity)} ${worktype.standardUnits[0]?.unit.name || ""}`,
                  unit: "day",
                }}
                optionalUnit={
                  worktype.optionalUnits[0]
                    ? {
                        value: `${formatNumber(worktype.optionalUnits[0]?.minQuantity)} - ${formatNumber(worktype.optionalUnits[0]?.maxQuantity)} ${worktype.optionalUnits[0]?.unit.name || ""}`,
                        unit: "day",
                      }
                    : undefined
                }
              />
            </ProductivityCard>
          ))}
        </ProductivitySection>
      ))}
    </div>
  );
};

export default ProductivityDatabaseList;
