"use client";
import React, { useEffect } from "react";

import { useRouter } from "next/navigation";
import { getIsTeamMemberFromCookies } from "@/lib/utils";

const Page: React.FC = () => {
  const router = useRouter();

  const isTeam = getIsTeamMemberFromCookies();

  useEffect(() => {
    if (isTeam) {
      router.push("/projects");
    } else {
      router.push("/dashboard");
    }
    // router.push("/dashboard");
  }, [router, isTeam]);

  return null;
};

export default Page;
