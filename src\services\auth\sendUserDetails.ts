import api from "@/lib/api-client";
import { Architect } from "@/types/Organisation";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

type SendUserDetails = {
  name: string | null;
  organisationName?: string;
  organisationType: Architect;
};

const sendUserDetails = async (body: SendUserDetails) => {
  const response = await api({
    url: "/auth/signup",
    method: "POST",
    data: body,
  });
  return response.data;
};

const useSendUserDetails = (handleSuccess?: () => void) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: sendUserDetails,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["user"] });
      if (handleSuccess) handleSuccess();
    },
    onError: (error: any) => {
      toast.error(error.data.message || error.message);
    },
  });
};

export default useSendUserDetails;
