"use client";
import { ComponentProps, useState } from "react";
import { Download, Eye } from "lucide-react";
import { format, parseISO } from "date-fns";
import Link from "next/link";
import { toast } from "sonner";

import {
  Sheet,
  SheetContent,
  Sheet<PERSON>eader,
  Sheet<PERSON>itle,
} from "@/components/ui/sheet";
import DeleteIcon from "../icons/Delete";
import {
  AlertDialog,
  AlertDialogContent,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogAction,
  AlertDialogCancel,
} from "@/components/ui/alertDialog";
import { AlertDialogTrigger } from "@radix-ui/react-alert-dialog";
import { Button } from "../ui/button";
import useGetDocumentHistory from "@/services/designStage/getDocumentHistory";
import NoDataToShow from "../ui/noDataToShow";
import { LoadingSpinner } from "../ui/loader";
import { downloadFile } from "@/lib/utils";
import useRemoveDocument from "@/services/designStage/removeDocument";
import { DocumentHistory as Document } from "@/types/DesignStage";

const DeleteDocument = ({
  stageId,
  doc,
}: {
  stageId: string;
  doc: Document;
}) => {
  const [isDeleteDocumentOpen, setIsDeleteDocumentOpen] = useState(false);

  const { mutate: removeDocument, isPending: isPendingRemoveDocument } =
    useRemoveDocument(() => {
      toast.success("Document removed successfully");
    });

  return (
    <AlertDialog
      open={isDeleteDocumentOpen}
      onOpenChange={setIsDeleteDocumentOpen}
    >
      <AlertDialogTrigger>
        <DeleteIcon className="text-[#E06666] size-4" />
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Delete Document?</AlertDialogTitle>
          <AlertDialogDescription>
            Do you wish to delete this document from the document history?
          </AlertDialogDescription>
        </AlertDialogHeader>

        <AlertDialogFooter>
          <AlertDialogCancel>Cancel</AlertDialogCancel>
          <AlertDialogAction asChild>
            <Button
              onClick={() => {
                removeDocument({
                  stageId,
                  documentId: doc._id,
                  isFromHistory: "true",
                });
              }}
              loading={isPendingRemoveDocument}
            >
              Delete
            </Button>
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};

type DocumentHistoryProps = Pick<
  ComponentProps<typeof Sheet>,
  "open" | "onOpenChange"
> & {
  stageId: string;
};

const DocumentHistory = ({
  open,
  onOpenChange,
  stageId,
}: DocumentHistoryProps) => {
  const { data, isPending } = useGetDocumentHistory({ stageId });

  // Filter out documents that are deleted from history
  const filteredData = data?.filter((doc) => !doc.isDeletedfromHistory) || [];

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent className="max-w-[507px] flex flex-col gap-0">
        <SheetHeader className="">
          <SheetTitle>Document History</SheetTitle>
        </SheetHeader>
        {isPending ? (
          <LoadingSpinner size={8} className="mt-6 mx-auto" />
        ) : filteredData.length === 0 ? (
          <div className=" py-6 bg-white flex flex-col justify-center h-screen items-center">
            <NoDataToShow />
          </div>
        ) : (
          <div className="space-y-2">
            {filteredData.map((doc) => (
              <div
                key={doc._id}
                className="p-4 border border-neutrals-G40 rounded-xl space-y-6"
              >
                <div className="flex justify-between gap-x-2 items-start">
                  <div className="space-y-1">
                    <h5 className="text-neutrals-G400 text-xs">
                      Document Name
                    </h5>
                    <p className="text-neutrals-G900 font-medium text-sm break-all">
                      {doc.filename || doc.s3Link.split("/").pop()}
                    </p>
                  </div>
                  <DeleteDocument key={doc._id} stageId={stageId} doc={doc} />
                </div>
                <div className="flex justify-between">
                  <p className="text-xs text-neutrals-G400 font-medium">
                    Uploaded on{" "}
                    <span className="font-bold text-neutrals-G900">
                      {format(parseISO(doc.uploadedAt), "dd MMM yyyy")}
                    </span>{" "}
                    by{" "}
                    <span className="font-bold text-neutrals-G900 capitalize">
                      {doc.uploadedBy.name}
                    </span>
                  </p>
                  <div className="flex gap-x-4 text-neutrals-G200">
                    <Link href={`${doc.s3Link}`} target="_blank">
                      <Eye className="size-4" />
                    </Link>
                    <button
                      onClick={() =>
                        downloadFile(
                          `${doc.s3Link}`,
                          doc.filename ||
                            doc.s3Link.split("/").pop() ||
                            `document${doc.uploadedAt}`,
                        )
                      }
                    >
                      <Download className="size-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </SheetContent>
    </Sheet>
  );
};

export default DocumentHistory;
