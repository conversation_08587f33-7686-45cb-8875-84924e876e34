import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Plus } from "lucide-react";
import { toast } from "sonner";

import {
  <PERSON><PERSON>,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { designTeamSchema } from "@/schema/designTeam";
import DesignTeamFormField from "./DesignTeamFormField";
import useAddDesignTeamMember from "@/services/designTeam/addDesignTeamMember";
import { useQueryClient } from "@tanstack/react-query";

type AddDesignTeamProps = {
  projectId: string;
};

const AddDesignTeam = ({ projectId }: AddDesignTeamProps) => {
  const [isAddDesignTeamOpen, setIsAddDesignTeamOpen] = useState(false);

  const queryClient = useQueryClient();

  const form = useForm<z.infer<typeof designTeamSchema>>({
    defaultValues: {
      projectId,
      userId: "",
      role: "",
    },
    resolver: zodResolver(designTeamSchema),
  });

  const { mutate, isPending } = useAddDesignTeamMember(() => {
    setIsAddDesignTeamOpen(false);
    toast.success("Design team member added successfully!");
    queryClient.invalidateQueries({ queryKey: ["project-analytics-complete"] });
  });

  const onSubmit = (data: z.infer<typeof designTeamSchema>) => {
    mutate(data);
  };

  useEffect(() => {
    if (!isAddDesignTeamOpen) {
      form.reset();
      form.clearErrors();
    }
  }, [form, isAddDesignTeamOpen]);

  return (
    <Dialog open={isAddDesignTeamOpen} onOpenChange={setIsAddDesignTeamOpen}>
      <DialogTrigger asChild>
        <Button className="pl-4 py-2 pr-3 gap-x-2">
          Add Design Team <Plus className="size-[22px] stroke-[2.5px]" />
        </Button>
      </DialogTrigger>
      <DialogContent
        isCloseIconVisible
        className="gap-y-[30px] sm:max-w-[510px]"
      >
        <DialogTitle>Add Design Team</DialogTitle>
        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col gap-y-6"
          >
            <div className="flex flex-col gap-y-3">
              <DesignTeamFormField form={form} />
            </div>
            <Button loading={isPending} type="submit" className="ml-auto px-4">
              Save
            </Button>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default AddDesignTeam;
