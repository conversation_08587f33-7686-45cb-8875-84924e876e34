// import { forwardRef } from "react";

// import { Input, InputProps } from "../ui/input";
// import { cn } from "@/lib/utils";

// type StandardUnitInputProps = InputProps & {
//   unitName: string;
// };

// const StandardUnitInput = forwardRef<HTMLInputElement, StandardUnitInputProps>(
//   ({ unitName, className, ...props }, ref) => {
//     return (
//       <div className="flex h-[53px]  rounded-md border border-input bg-background px-3 py-2 text-name-title text-base font-medium ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 disabled:bg-[#A3A5B21C] disabled:border-none disabled:text-[#04040480]">
//         <Input
//           type="number"
//           ref={ref}
//           {...props}
//           className={cn(
//             "w-full bg-transparent outline-none focus-visible:outline-none p-0 h-auto mr-2 border-none",
//             className,
//           )}
//         />
//         <span className="shrink-0 pointer-events-none flex items-center text-name-title font-semibold">
//           {unitName}
//         </span>
//       </div>
//     );
//   },
// );

// StandardUnitInput.displayName = "StandardUnitInput";

// export default StandardUnitInput;

// import { forwardRef } from "react";
// import { Input, InputProps } from "@/components/ui/input";
// import { cn } from "@/lib/utils";

// type StandardUnitInputProps = InputProps & {
//   unitName: string;
// };

// const StandardUnitInput = forwardRef<HTMLInputElement, StandardUnitInputProps>(
//   ({ unitName, className, ...props }, ref) => {
//     return (
//       <div className="flex items-center px-3 py-2.5 rounded-md border border-border-gray ">
//         <Input
//           type="number"
//           ref={ref}
//           {...props}
//           className={cn("border-none shadow-none", className)}
//         />
//         <span className="text-neutrals-G100 text-base font-medium ml-2  whitespace-nowrap">
//           {unitName}
//         </span>
//       </div>
//     );
//   },
// );

// StandardUnitInput.displayName = "StandardUnitInput";

// export default StandardUnitInput;

// import { forwardRef } from "react";
// import { Input, InputProps } from "@/components/ui/input";
// import { cn } from "@/lib/utils";

// type StandardUnitInputProps = InputProps & {
//   unitName: string;
// };

// const StandardUnitInput = forwardRef<HTMLInputElement, StandardUnitInputProps>(
//   ({ unitName, className, ...props }, ref) => {
//     return (
//       <div className="flex items-center px-3 py-2.5 rounded-md border border-border-gray shadow-[0px_4px_150px_0px_rgba(0,0,0,0.08)]">
//         <Input
//           type="number"
//           ref={ref}
//           {...props}
//           className={cn(
//             "w-full border-none shadow-none text-neutrals-G900 text-base font-medium focus-visible:ring-0",
//             className,
//           )}
//         />
//         <span className="text-neutrals-G100 text-base font-medium ml-2 whitespace-nowrap">
//           {unitName}
//         </span>
//       </div>
//     );
//   },
// );

// StandardUnitInput.displayName = "StandardUnitInput";

// export default StandardUnitInput;

import { forwardRef } from "react";
import { InputProps } from "@/components/ui/input";

type StandardUnitInputProps = InputProps & {
  unitName: string;
};

const StandardUnitInput = forwardRef<HTMLInputElement, StandardUnitInputProps>(
  ({ unitName, className, ...props }, ref) => {
    return (
      <div className="flex items-center px-3 py-2.5 rounded-md border border-border-gray shadow-[0px_4px_150px_0px_rgba(0,0,0,0.08)]">
        <input
          type="number"
          ref={ref}
          pattern="[0-9]*"
          inputMode="numeric"
          {...props}
          className="w-full bg-transparent outline-none text-base font-medium text-neutrals-G900"
        />
        <span className="text-neutrals-G100 text-base font-medium ml-2">
          {unitName}
        </span>
      </div>
    );
  },
);

StandardUnitInput.displayName = "StandardUnitInput";

export default StandardUnitInput;
