import { useInfiniteQuery } from "@tanstack/react-query";
import api from "@/lib/api-client";
import { Contractor } from "@/types/Contractor";

type UseContractorsParams = {
  limit?: number;
  search?: string;
  enabled?: boolean;
};

type UseContractorsReturn = {
  contractors: Contractor[];
  isLoading: boolean;
  isFetchingNextPage: boolean;
  hasNextPage: boolean;
  isError: Error | null;
  fetchNextPage: () => Promise<any>;
  contractorOptions: { value: string; label: string }[];
  refetch: () => Promise<any>;
};

const useContractors = ({
  limit = 10,
  search = "",
  enabled = true,
}: UseContractorsParams = {}): UseContractorsReturn => {
  const query = useInfiniteQuery({
    queryKey: ["contractors", { search, limit }],
    queryFn: async ({ pageParam = 1 }) => {
      try {
        const params = new URLSearchParams({
          page: pageParam.toString(),
          limit: limit.toString(),
        });

        if (search) {
          params.append("search", search);
        }

        const response = await api.get<Contractor[]>(
          `/contractors?${params.toString()}`,
        );

        return {
          data: response.data,
          page: pageParam,
          limit,
          totalCount: response.data.length,
        };
      } catch (error) {
        throw error;
      }
    },
    getNextPageParam: (lastPage) => {
      if (!lastPage || lastPage.data.length < limit) return undefined;
      return lastPage.page + 1;
    },
    initialPageParam: 1,
    enabled,
  });

  const contractors = query.data?.pages.flatMap((page) => page.data) ?? [];

  const contractorOptions = contractors.map((contractor: Contractor) => ({
    value: contractor._id,
    label: contractor.organisationName,
  }));

  return {
    contractors,
    isLoading: query.isLoading,
    isFetchingNextPage: query.isFetchingNextPage,
    hasNextPage: query.hasNextPage,
    isError: query.error,
    fetchNextPage: query.fetchNextPage,
    contractorOptions,
    refetch: query.refetch,
  };
};

export default useContractors;
