import { formatDistanceToNow } from "date-fns";
import { useState } from "react";
import parse from "html-react-parser";
import sanitizeHtml from "sanitize-html";

import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import type { Notification } from "@/types/Notifications";
import useApplyActionsNotifications from "@/services/notification/applyActionsNotifications";
import useMarkNotificationAsRead from "@/services/notification/markNotificationAsRead";

type NotificationCardProps = {
  notification: Notification;
};
const NotificationCard = ({ notification }: NotificationCardProps) => {
  const isPaymentType =
    notification.notificationType === "payment_schedule_delayed_notification";

  const timeAgo = formatDistanceToNow(notification.createdAt, {
    addSuffix: true,
  });

  const { mutate, isPending } = useApplyActionsNotifications();
  const { mutate: markAsRead } = useMarkNotificationAsRead();

  const [clickedBtn, setClickedBtn] = useState<
    "collect_after" | "collect_now" | ""
  >("");

  return (
    <div
      onClick={() => {
        if (notification.read) return;
        markAsRead({ notificationId: notification._id });
      }}
      className={cn(
        "flex justify-between items-start gap-x-4 w-full px-[30px] py-4 text-sm ",
        notification.read
          ? "bg-white border-b border-neutrals-G40"
          : isPaymentType
            ? "bg-primary-blue-B40"
            : "bg-feather-gray",
      )}
    >
      <div
        className={cn(
          "leading-normal space-y-6",
          isPaymentType && !notification.read
            ? "text-neutrals-G900"
            : "text-name-title",
        )}
      >
        <div>{parse(sanitizeHtml(notification.message))}</div>
        {isPaymentType && (
          <div className="space-y-2">
            <span className="text-name-title">Select payment schedule :</span>
            <div className="flex gap-x-2">
              <Button
                variant="highlighted"
                loading={isPending && clickedBtn === "collect_after"}
                disabled={isPending}
                onClick={() => {
                  setClickedBtn("collect_after");
                  mutate({
                    notificationId: notification._id,
                    action: "collect_after",
                  });
                }}
              >
                Collect after completion
              </Button>
              <Button
                variant="default"
                loading={isPending && clickedBtn === "collect_now"}
                disabled={isPending}
                onClick={() => {
                  setClickedBtn("collect_now");
                  mutate({
                    notificationId: notification._id,
                    action: "collect_now",
                  });
                }}
                className="px-4"
              >
                Collect now
              </Button>
            </div>
          </div>
        )}
      </div>
      <div className="text-neutrals-G300  shrink-0">{timeAgo}</div>
    </div>
  );
};

export default NotificationCard;
