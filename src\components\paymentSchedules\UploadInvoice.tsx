import { ChangeEvent, useRef, useState } from "react";
import { toast } from "sonner";
import { Upload, X } from "lucide-react";

import {
  useGenerateS3Url,
  useUploadToS3,
} from "@/services/project/sitedocs-hooks";
import { Button } from "../ui/button";
import useUploadDocumentToDesignStage from "@/services/designStage/uploadDocumentToDesignStage";
import { useUpdatePaymentSchedule } from "@/services/paymentSchedules/updatePaymentSchedules";
import AttachIcon1 from "../icons/AttachIcon1";

type UploadDocumentToStageProps = {
  projectId: string;
  stageId?: string;
  invoice: string | null;
};

const UploadInvoice = ({
  projectId,
  stageId,
  invoice,
}: UploadDocumentToStageProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null);

  const { mutateAsync: generateS3Url } = useGenerateS3Url();
  const { mutateAsync: uploadToS3 } = useUploadToS3();
  const updateSchedule = useUpdatePaymentSchedule();

  const [loading, setLoading] = useState(false);

  const handleDocumentUpload = async (event: ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0] || null;
    if (!file) {
      return;
    }

    try {
      setLoading(true);
      const signedUrl = await generateS3Url({
        fileName: file.name,
        projectId,
      });

      await uploadToS3({
        signedUrl,
        file: file,
      });

      const s3Link = signedUrl.split("?")[0];

      await updateSchedule.mutate(
        {
          scheduleId: projectId,
          invoice: s3Link,
        },
        {
          onSuccess: () => {
            toast.success("Invoice uploaded successfully!");
          },
          onError: () => {
            toast.error("Failed to upload invoice");
          },
        },
      );
    } catch (error: any) {
      console.error("Error uploading document:", error);
      toast.error("Failed to upload document. Please try again.");
    } finally {
      setLoading(false);
      event.target.value = "";
    }
  };

  const handleDeleteInvoice = async () => {
    try {
      await updateSchedule.mutate(
        { scheduleId: projectId, invoice: null },
        {
          onSuccess: () => toast.success("Invoice removed."),
          onError: () => toast.error("Failed to remove invoice"),
        },
      );
    } catch (error) {
      console.error("Error removing invoice:", error);
      toast.error("Error removing invoice.");
    }
  };

  return (
    <>
      <input
        ref={fileInputRef}
        type="file"
        accept=".pdf,.obj,image/*"
        onChange={handleDocumentUpload}
        className="hidden"
      />
      <Button
        onClick={() => fileInputRef.current?.click()}
        variant="link"
        className="px-3.5 gap-x-1.5 font-medium hover:no-underline border-[1px] border-[#BFD8F9] bg-[#FEFEFF]"
        type="button"
        disabled={loading}
        loading={loading}
      >
        {invoice ? (
          <div className="flex items-center gap-x-1.5">
            Invoice Attached
            <button
              onClick={(e) => {
                e.stopPropagation();
                handleDeleteInvoice();
              }}
              type="button"
              className="text-muted-foreground hover:text-destructive"
            >
              <X className="w-4 h-4" />
            </button>
          </div>
        ) : (
          <div className="flex items-center gap-x-1.5">
            <AttachIcon1 /> Attach
          </div>
        )}
      </Button>{" "}
    </>
  );
};

export default UploadInvoice;
