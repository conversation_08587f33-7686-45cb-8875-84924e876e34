// import React from "react";

// import MilestoneCard from "./MilestoneCard";
// import { ProjectAnalytics } from "@/types/Project";

// interface MilestonesListProps {
//   milestones: ProjectAnalytics["milestones"];
// }

// const MilestonesList = ({ milestones }: MilestonesListProps) => {
//   return (
//     <>
//       {milestones.map((milestone) => (
//         <MilestoneCard key={crypto.randomUUID()} milestone={milestone} />
//       ))}
//     </>
//   );
// };

// export default MilestonesList;

// import React from "react";
// import { format, differenceInDays } from "date-fns";
// import { ProjectAnalytics } from "@/types/Project";
// import {
//   Accordion,
//   AccordionContent,
//   AccordionItem,
//   AccordionTrigger,
// } from "@/components/ui/accordion";
// import {
//   Table,
//   TableBody,
//   TableCell,
//   TableHead,
//   TableHeader,
//   TableRow,
// } from "@/components/ui/table";
// import { CircleProgress } from "@/components/ui/progressCircle";
// import { Badge } from "@/components/ui/badge";
// import { cn } from "@/lib/utils";
// import UsersIcon from "../icons/usersIcon";
// import CalenderIcon from "../icons/calenderIcon";

// interface MilestonesListProps {
//   milestones: ProjectAnalytics["milestones"];
// }

// const getStatusColor = (status: number) => {
//   if (status === 0) return "bg-[#4CAF50]/10 text-[#4CAF50]";
//   if (status > 0) return "bg-[#DA9500]/10 text-[#D99400]";
//   return "bg-[#FF5454]/10 text-[#FF5454]";
// };

// const getStatusText = (status: number) => {
//   if (status === 0) return "ON TIME";
//   if (status > 0) return `${status} DAYS BEHIND`;
//   return `${Math.abs(status)} DAYS LATE`;
// };

// const getProgressColor = (progress: number) => {
//   if (progress < 40) return "#C00";
//   if (progress < 90) return "#DA9500";
//   return "#4CAF50";
// };

// const MilestonesList = ({ milestones }: MilestonesListProps) => {
//   console.log(milestones);
//   return (
//     <Accordion type="single" collapsible className="space-y-4 ">
//       {/* <div className="h-28 w-full p-3 bg-white rounded-xl border border-neutrals-G40 flex-col justify-start items-start gap-3"></div> */}

//       {milestones.map((milestone) => (
//         <AccordionItem
//           key={crypto.randomUUID()}
//           value={milestone._id}
//           className="w-full p-3 bg-white rounded-xl border border-neutrals-G40 flex-col justify-start items-start gap-3"
//         >
//           <AccordionTrigger className="p-0 [&>svg]:self-end [&>svg]:mb-3">
//             <div className="self-stretch justify-start items-center gap-3 flex w-full">
//               <div className="w-[88px] h-[88px] relative">
//                 <CircleProgress
//                   value={49}
//                   progressColor={getProgressColor(milestone.progress)}
//                   className="absolute left-[7px] top-[7px] w-[74px] h-[74px]"
//                 />
//                 <div
//                   className="absolute left-[25px] top-[33px] text-lg font-semibold"
//                   style={{ color: getProgressColor(milestone.progress) }}
//                 >
//                   {milestone.progress}%
//                 </div>
//               </div>

//               <div className="grow flex flex-col gap-3">
//                 <div className=" flex items-start text-neutrals-G900 text-xl font-semibold ">
//                   {milestone.name}
//                 </div>

//                 <div className="flex justify-between items-center gap-5 ">
//                   <div className="flex items-center gap-1">
//                     <span className=" inline-flex items-center gap-1 text-neutrals-G800 text-sm font-bold">
//                       <UsersIcon /> {milestone.noOfWorkers}
//                     </span>
//                     <span className=" text-neutrals-G800 text-sm">
//                       {milestone.noOfWorkers === 1 ? "worker" : "workers"}
//                     </span>
//                   </div>

//                   <div className=" inline-flex items-center gap-1 text-neutrals-G800 text-sm">
//                     <CalenderIcon />
//                     {format(new Date(milestone.startDate), "dd MMM")} -{" "}
//                     {format(new Date(milestone.endDate), "dd MMM")}
//                   </div>

//                   <div className="flex items-center gap-1 justify-self-start">
//                     <span className="text-neutrals-G800 text-sm">
//                       Projected End Date:
//                     </span>
//                     <span className="text-neutrals-G800 text-sm">
//                       {format(new Date(milestone.projectedEndDate), "dd MMM")}
//                     </span>
//                     <Badge
//                       className={cn(
//                         "ml-1.5 py-1 px-2 rounded  font-medium",
//                         getStatusColor(
//                           differenceInDays(
//                             new Date(milestone.projectedEndDate),
//                             new Date(milestone.endDate),
//                           ),
//                         ),
//                       )}
//                     >
//                       {getStatusText(
//                         differenceInDays(
//                           new Date(milestone.projectedEndDate),
//                           new Date(milestone.endDate),
//                         ),
//                       )}
//                     </Badge>
//                   </div>
//                 </div>
//               </div>
//             </div>
//           </AccordionTrigger>

//           <AccordionContent className="pt-4">
//             <Table>
//               <TableHeader>
//                 <TableRow>
//                   <TableHead>WORK TYPES</TableHead>
//                   <TableHead>NO. OF WORKERS</TableHead>
//                   <TableHead>NO. OF WORK DAYS</TableHead>
//                   <TableHead>PRODUCTIVITY %</TableHead>
//                   <TableHead>PROGRESS</TableHead>
//                   <TableHead>STATUS</TableHead>
//                 </TableRow>
//               </TableHeader>

//               <TableBody>
//                 {milestone.workTypes.map((workType) => {
//                   const workDays = differenceInDays(
//                     new Date(milestone.endDate),
//                     new Date(milestone.startDate),
//                   );

//                   return (
//                     <TableRow key={crypto.randomUUID()}>
//                       <TableCell className="font-semibold">
//                         {workType.name}
//                       </TableCell>
//                       <TableCell>{workType.noOfWorkers}</TableCell>
//                       <TableCell>{workDays}</TableCell>
//                       <TableCell>
//                         {workType.productivity?.toFixed(2)}%
//                       </TableCell>
//                       <TableCell>
//                         {workType.totalWorkDone?.toFixed(2)}%
//                       </TableCell>
//                       <TableCell>
//                         <Badge
//                           className={cn(
//                             "py-1 px-2 rounded text-[10px] font-medium",
//                             getStatusColor(workType.status),
//                           )}
//                         >
//                           {getStatusText(workType.status)}
//                         </Badge>
//                       </TableCell>
//                     </TableRow>
//                   );
//                 })}
//               </TableBody>
//             </Table>
//           </AccordionContent>
//         </AccordionItem>
//       ))}
//     </Accordion>
//   );
// };

// export default MilestonesList;

import React from "react";
import { format, differenceInDays } from "date-fns";
import { ProjectAnalytics } from "@/types/Project";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { CircleProgress } from "@/components/ui/progressCircle";
import DayOfCompletionStatusBadge, {
  checkStatus,
  getStatusResult,
} from "./DayOfCompletionStatusBadge";
import UsersIcon from "../icons/usersIcon";
import CalenderIcon from "../icons/calenderIcon";
import { Badge } from "../ui/badge";

interface MilestonesListProps {
  milestones: ProjectAnalytics["milestones"];
}

const getProgressVariant = (progress: number) => {
  if (progress < 40) return "danger";
  if (progress < 90) return "warning";
  return "success";
};

const MilestonesList = ({ milestones }: MilestonesListProps) => {
  return (
    <Accordion type="single" collapsible className="space-y-4">
      {milestones.map((milestone) => (
        <AccordionItem
          key={milestone._id}
          value={milestone._id}
          className="w-full p-3 bg-white rounded-xl border border-neutrals-G40 flex-col justify-start items-start gap-3"
        >
          <AccordionTrigger className="p-0 [&>svg]:self-end [&>svg]:mb-4 [&>svg]:ml-5 ">
            <div className="self-stretch justify-start items-center gap-3 flex w-full">
              <div className="w-[88px] h-[88px] relative">
                <CircleProgress
                  value={milestone.progress}
                  variant={getProgressVariant(milestone.progress)}
                  className="absolute left-[7px] top-[7px] w-[74px] h-[74px]"
                />
              </div>

              <div className="grow flex flex-col gap-3">
                <div className="flex items-start text-neutrals-G900 text-xl font-semibold">
                  {milestone.name}
                </div>

                <div className="w-full grid grid-cols-12  items-center">
                  <div className="col-span-4">
                    <div className="flex items-center gap-1">
                      <span className="inline-flex items-center gap-1 text-neutrals-G800 text-sm font-bold">
                        <UsersIcon /> {milestone.noOfWorkers}
                      </span>
                      <span className="text-neutrals-G800 text-sm font-normal">
                        {milestone.noOfWorkers === 1 ? "worker" : "workers"}
                      </span>
                    </div>
                  </div>

                  <div className="col-span-4">
                    <div className="flex items-center gap-1">
                      <div className="inline-flex items-center gap-1 text-neutrals-G800 text-sm font-normal">
                        <CalenderIcon />
                        {format(new Date(milestone.startDate), "dd MMM")} -{" "}
                        {format(new Date(milestone.endDate), "dd MMM")}
                      </div>
                    </div>
                  </div>

                  <div className="col-span-4">
                    <div className="flex items-center gap-1">
                      <span className="text-neutrals-G800 text-sm whitespace-nowrap">
                        Projected End Date:
                      </span>
                      <span className="text-neutrals-G800 text-sm whitespace-nowrap">
                        {format(new Date(milestone.projectedEndDate), "dd MMM")}
                      </span>
                      <DayOfCompletionStatusBadge
                        projectedEndDate={milestone.projectedEndDate}
                        expectedEndDate={milestone.endDate}
                        className="ml-1.5"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </AccordionTrigger>

          <AccordionContent className="pt-4">
            <Table className="">
              <TableHeader>
                <TableRow>
                  <TableHead className="w-2/6">WORK TYPES</TableHead>
                  <TableHead className="w-1/6">NO. OF WORKERS</TableHead>
                  {/* <TableHead>NO. OF WORK DAYS</TableHead> */}
                  <TableHead className="w-1/6">PRODUCTIVITY %</TableHead>
                  <TableHead className="w-1/6">PROGRESS</TableHead>
                  <TableHead className="w-1/6">STATUS</TableHead>
                </TableRow>
              </TableHeader>

              <TableBody>
                {milestone.workTypes.map((workType) => {
                  // const workDays = differenceInDays(
                  //   new Date(milestone.endDate),
                  //   new Date(milestone.startDate),
                  // );

                  return (
                    <TableRow
                      key={crypto.randomUUID()}
                      className="*:font-semibold"
                    >
                      <TableCell className="text-base">
                        {workType.name}
                      </TableCell>
                      <TableCell className="">{workType.noOfWorkers}</TableCell>
                      {/* <TableCell>{workDays}</TableCell> */}
                      <TableCell>
                        {workType.productivity?.toFixed(2)}%
                      </TableCell>
                      <TableCell>
                        {workType.totalWorkDone?.toFixed(2)}%
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant={
                            workType.status >= 0 ? "default" : "destructive"
                          }
                          size={"sm"}
                        >
                          {getStatusResult(
                            workType.status,
                            checkStatus(workType.status),
                          )}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </AccordionContent>
        </AccordionItem>
      ))}
    </Accordion>
  );
};

export default MilestonesList;
