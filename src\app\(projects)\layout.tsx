import Link from "next/link";

import ProjectSidebar from "@/components/dashboard/ProjectSidebar";
import ProtectedRoutes from "@/components/ProtectedRoutes";
import Sidebar from "@/components/dashboard/Sidebar";
import PageHeader from "@/components/layout/pageHeader/PageHeader";
import ArrowLeft from "@/components/icons/ArrowLeft";

export default function ProjectLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex h-screen">
      <Sidebar shrink={true} />
      <div className="flex flex-col gap-y-3 flex-1 mb-4">
        <PageHeader className="p-3.5 gap-3 justify-start items-start [&>svg]:mt-1 shadow-lg">
          <Link href="/projects">
            <ArrowLeft />
          </Link>
          <div>
            <PageHeader.Heading>Project Details</PageHeader.Heading>
            <PageHeader.Description>
              View the complete details of your projects.
            </PageHeader.Description>
          </div>
        </PageHeader>
        <div className="flex flex-1 overflow-auto ml-3 pb-1 mr-5 gap-x-5">
          <ProjectSidebar />
          <main className="flex-1 overflow-y-auto scrollbar-hide">
            <ProtectedRoutes>{children}</ProtectedRoutes>
          </main>
        </div>
      </div>
    </div>
  );
}
