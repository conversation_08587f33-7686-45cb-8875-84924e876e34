import { useQuery } from "@tanstack/react-query";
import api from "@/lib/api-client";
import { Milestone } from "@/types/Milestone";

type GetMilestones = { projectId: string };

const getMilestone = async ({
  projectId,
}: GetMilestones): Promise<Milestone[]> => {
  const response = await api.get(`/milestones/${projectId}`);
  return response.data;
};

type UseGetMilestones = GetMilestones;

const useGetMilestones = ({ projectId }: UseGetMilestones) => {
  return useQuery({
    queryKey: ["milestones", projectId],
    queryFn: async () => getMilestone({ projectId }),
  });
};

export default useGetMilestones;
