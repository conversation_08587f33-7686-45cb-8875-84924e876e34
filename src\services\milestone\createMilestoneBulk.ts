import { toast } from "sonner";
import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";

type CreateProject = {
  projectId: string;
  milestoneTemplateId?: string;
  milestoneName?: string;
  isCustom?: boolean;
  startDate?: string;
  duration?: number;
  paymentDate?: string;
  worktypes?: Partial<{
    worktypeTemplateId: string;
    startDate: string;
    duration: number;
    totalQuantity: number;
    unit: string;
    totalQuantityOptionalUnit: number;
    optionalUnit: string;
  }>[];
};

const createMilestoneBulk = async ({
  projectId,
  ...body
}: CreateProject): Promise<{ _id: string }> => {
  const response = await api({
    url: `/milestones/${projectId}/bulk`,
    method: "POST",
    data: body,
  });
  return response.data;
};

const useCreateMilestoneBulk = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createMilestoneBulk,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["milestones"] });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to create milestone",
      );
    },
  });
};

export default useCreateMilestoneBulk;
