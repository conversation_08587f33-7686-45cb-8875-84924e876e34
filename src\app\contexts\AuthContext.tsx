"use client";

import { createContext, useContext, ReactNode, useEffect } from "react";
import { auth } from "../firebase";
import { useAuthState } from "react-firebase-hooks/auth";
import { User } from "firebase/auth";
import useGetUser from "@/services/auth/getUser";
import Cookies from "js-cookie";

interface AuthContextType {
  user: User | null | undefined;
  loading: boolean;
  error: Error | undefined;
  isTeamMember: boolean | null;
  isAdmin: boolean | null;
}

const AuthContext = createContext<AuthContextType | null>(null);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, loading, error] = useAuthState(auth);

  const { data, isSuccess } = useGetUser({
    enabled: !!user && typeof window !== "undefined",
  });

  const isTeamMember =
    isSuccess && data?.user?.isTeamMember !== undefined
      ? data.user.isTeamMember
      : null;

  const isAdmin =
    isSuccess && data?.user?.teamData?.isAdmin !== undefined
      ? data.user.teamData.isAdmin
      : null;

  useEffect(() => {
    if (isTeamMember !== null) {
      Cookies.set("isTeamMember", isTeamMember ? "true" : "false", {
        path: "/",
      });
    }

    if (isAdmin !== null) {
      Cookies.set("isAdmin", isAdmin ? "true" : "false", {
        path: "/",
      });
    }
  }, [isTeamMember, isAdmin]);

  useEffect(() => {
    if (isSuccess && data?.user?.isTeamMember !== undefined) {
      Cookies.set("isTeamMember", data.user.isTeamMember ? "true" : "false", {
        path: "/",
      });
    }

    if (isSuccess && data?.user?.teamData?.isAdmin !== undefined) {
      Cookies.set("isAdmin", data.user.teamData.isAdmin ? "true" : "false", {
        path: "/",
      });
    }
  }, [data, isSuccess]);

  return (
    <AuthContext.Provider
      value={{ user, loading, error, isTeamMember, isAdmin }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
