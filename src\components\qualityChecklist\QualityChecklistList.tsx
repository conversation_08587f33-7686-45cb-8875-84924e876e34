import { ChevronRight } from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";

import { Button } from "../ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";
import useGetQualityChecklist from "@/services/qualityChecklist/getQualityChecklist";
import NoDataToShow from "../ui/noDataToShow";
import Loader from "../ui/loader";
import ErrorText from "../ui/errortext";
import CompletionStatus from "../ui/completionStatus";

const QualityChecklistList = () => {
  const { id: projectId } = useParams() as { id: string };
  const {
    data: checklistsData,
    isLoading,
    error,
  } = useGetQualityChecklist(projectId);

  if (isLoading) {
    return <Loader />;
  }

  if (error) {
    return <ErrorText entity="quality checklists" />;
  }

  if (!checklistsData?.length) {
    return (
      <div className="flex justify-center">
        <NoDataToShow />
      </div>
    );
  }

  return (
    <>
      {checklistsData.map((milestoneChecklist) => (
        <div
          key={milestoneChecklist._id}
          className="p-3 gap-y-4 border border-border-gray1 rounded-xl"
        >
          <h5 className="px-3.5 py-2.5 text-neutrals-G800 font-semibold border-b border-[#E6E6E6] ">
            {milestoneChecklist.milestone.name}
          </h5>
          <Table>
            <TableHeader>
              <TableRow className="*:px-3.5 *:pt-4 *:pb-3">
                <TableHead>SI</TableHead>
                <TableHead className="w-[43%]">Checklist</TableHead>
                <TableHead className="w-1/5">Work type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead />
              </TableRow>
            </TableHeader>
            <TableBody>
              {milestoneChecklist.data.map((checklist, index) => (
                <TableRow
                  key={checklist._id}
                  className="*:px-3.5 *:py-3 h-3 *:font-medium"
                >
                  <TableCell>{index + 1}</TableCell>
                  <TableCell>{checklist.name}</TableCell>
                  <TableCell>{checklist.worktypeTemplateId.worktype}</TableCell>
                  <TableCell>
                    <CompletionStatus
                      status={
                        checklist.status === "completed"
                          ? "completed"
                          : "ongoing"
                      }
                    />
                  </TableCell>
                  <TableCell className="text-right">
                    <Link href={`quality-checklist/${checklist._id}`}>
                      <Button
                        variant="link"
                        className="gap-x-2 h-auto font-normal"
                      >
                        More details <ChevronRight className="size-4" />
                      </Button>
                    </Link>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      ))}
    </>
  );
};

export default QualityChecklistList;
