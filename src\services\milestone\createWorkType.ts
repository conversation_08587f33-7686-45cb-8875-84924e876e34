import { toast } from "sonner";

import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export type CreateWorkType = {
  projectId: string;
  milestoneId: string;
  worktypeTemplateId: string | undefined;
  worktypeName: string | undefined;
  isCustom: boolean;
  startDate?: string;
  duration?: number;
  totalQuantity?: number;
  unit?: string;
  totalQuantityOptionalUnit?: number;
  optionalUnit?: string;
};

const createWorkType = async ({
  projectId,
  milestoneId,
  ...body
}: CreateWorkType) => {
  const response = await api({
    url: `/worktypes/${projectId}/${milestoneId}`,
    method: "POST",
    data: body,
  });
  return response.data;
};

const useCreateWorkType = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: createWorkType,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["worktypes"] });
      if (onSuccess) onSuccess();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to create work type",
      );
    },
  });
};

export default useCreateWorkType;
