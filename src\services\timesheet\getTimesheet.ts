import { useQuery } from "@tanstack/react-query";

import api from "@/lib/api-client";
import { Timesheet } from "@/types/Timesheet";

type GetTimesheet = {
  startDate: string;
  endDate: string;
};

export type UseGetTimesheet = GetTimesheet;

// NOTE: startDate and endDate needs to be provided, from: "7 days before to", to: "day before today" by default
const getTimesheet = async ({
  startDate,
  endDate,
}: GetTimesheet): Promise<{ current: Timesheet[]; history: Timesheet[] }> => {
  const response = await api.get(`/time-sheet/get-sheets`, {
    params: {
      startDate,
      endDate,
    },
  });

  return response.data;
};

const useGetTimesheet = ({ startDate, endDate }: UseGetTimesheet) => {
  return useQuery({
    queryKey: ["time-sheet", startDate, endDate],
    queryFn: () => getTimesheet({ startDate, endDate }),
  });
};

export default useGetTimesheet;
