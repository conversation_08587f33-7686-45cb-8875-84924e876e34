export type MeetingConfiguration = {
  _id: string;
  organizationId: string;
  createdAt: string;
  updatedAt: string;
  calSlug: string;
  calUserName: string;
  calWebhookSecret: string;
  eventId: string;
  calApiKeys?: string;
  eventLink?: string;
};

export type MeetingTableData = {
  projectName: string;
  time: string;
  date: string;
};

export interface GetMeetingsParams {
  status: "pending" | "completed";
  page?: number;
  limit?: number;
}

export interface Meeting {
  _id: string;
  architectOrganizationId: string;
  contractorOrganizationId: string;
  projectId: {
    _id: string;
    name: string;
    projectId: string;
    clientName: string;
    clientWhatsAppNo: string;
    location: string;
    status: string;
    architectOrg: string;
    owner: string;
    users: string[];
    siteDocuments: {
      category: string;
      fileName: string;
      storagePath: string;
      milestoneId: string;
      revisionNumber: number;
      uploadDate: string;
      _id: string;
    }[];
    createdAt: string;
    updatedAt: string;
    __v: number;
    contractorOrg: string;
    endDate: string;
    meetingEventLink: string;
  };
  calMeetinguid: string;
  meetingDate: string;
  meetingStatus: "pending" | "completed";
  meetingLink: string;
  clientApprovalStatus: string;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

export interface MeetingsResponse {
  data: Meeting[];
  totalCount: number;
  page: number;
  limit: number;
}
