import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";
import Image from "next/image";

interface EmailCheckPageProps {
  searchParams: { email?: string };
}

function EmailCheckPage({ searchParams }: EmailCheckPageProps) {
  const email = searchParams.email || "your email";
  return (
    <div className=" w-[491px] h-[683px]  p-10 bg-white rounded-3xl shadow-[0px_4px_24px_0px_rgba(0,0,0,0.12)]">
      <div className="flex justify-center mb-10">
        <Image
          src="/projectsmatelogo.png"
          alt="Projectsmate Logo"
          width={163}
          height={54}
          className="w-[163px] h-[54px] object-contain"
        />
      </div>

      <div className="flex flex-col items-center gap-8">
        <div className="relative w-48 h-48">
          <Image
            src="/check-email.png"
            alt="Email Verification"
            width={193}
            height={193}
            className="w-full h-full"
          />
        </div>

        <div className="flex flex-col gap-6 w-full">
          <div className="flex flex-col gap-2">
            <h1 className="text-xl font-semibold text-center text-neutrals-G900  ">
              Check your Email
            </h1>
            <p className="text-base text-center  ">
              <span className="text-neutrals-G600">
                A link to reset your password has been sent to{" "}
              </span>
              <span className="text-[#292929] font-medium">
                {decodeURIComponent(email)}
              </span>
              <span className="text-neutrals-G600"> successfully</span>
            </p>
          </div>

          <Link href="/login" className="w-full">
            <Button className="w-full bg-primary rounded-[6px]  hover:bg-primary /90 text-base font-medium py-3 px-4">
              Back to login
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}

export default EmailCheckPage;
