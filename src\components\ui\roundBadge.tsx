import { cva, VariantProps } from "class-variance-authority";

import { cn } from "@/lib/utils";
import { ComponentProps } from "react";

const roundBagdeVariants = cva("size-3 rounded-full ", {
  variants: {
    variant: {
      default: "bg-gray-500",
      ongoing: "bg-ongoing",
      halted: "bg-halted",
      completed: "bg-completed",
    },
  },
  defaultVariants: {
    variant: "default",
  },
});

type RoundBadgeProps = VariantProps<typeof roundBagdeVariants> &
  ComponentProps<"span">;

const RoundBadge = ({ variant, className }: RoundBadgeProps) => {
  return <span className={cn(roundBagdeVariants({ variant, className }))} />;
};

export default RoundBadge;
