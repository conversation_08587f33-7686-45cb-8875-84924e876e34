import { useQuery } from "@tanstack/react-query";

import api from "@/lib/api-client";
import { MilestoneTemplate } from "@/types/Milestone";

const getMilestonesTemplate = async (): Promise<MilestoneTemplate[]> => {
  const response = await api.get("/milestones/templates");
  return response.data;
};

const useGetMilestonesTemplate = () => {
  return useQuery({
    queryKey: ["milestones-templates"],
    queryFn: getMilestonesTemplate,
  });
};

export default useGetMilestonesTemplate;
