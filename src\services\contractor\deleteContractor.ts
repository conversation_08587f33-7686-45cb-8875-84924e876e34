import { useMutation, useQueryClient } from "@tanstack/react-query";
import api from "@/lib/api-client";
import { toast } from "sonner";

const useDeleteContractor = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (contractorId: string) => {
      const response = await api.delete(`/contractors/${contractorId}`);
      return response.data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["contractors"] });
      toast.success("Contractor deleted successfully");
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to delete contractor",
      );
    },
  });
};

export default useDeleteContractor;
