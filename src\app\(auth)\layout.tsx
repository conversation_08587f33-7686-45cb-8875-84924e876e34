// "use client";

// import { ReactNode, useEffect } from "react";
// import { useRouter } from "next/navigation";
// import Image from "next/image";
// import useGetUser from "@/services/auth/getUser";

// const redirectingUrl = "/dashboard";

// export default function AuthLayout({ children }: { children: ReactNode }) {
//   const router = useRouter();
//   const { data: user, isSuccess } = useGetUser();

//   useEffect(() => {
//     router.prefetch(redirectingUrl);
//   }, [router]);

//   useEffect(() => {
//     if (isSuccess && user) {
//       router.push(redirectingUrl);
//     }
//   }, [isSuccess, user, router]);

//   return (
//     <div>
//       <main>
//         <div className="flex h-screen md:p-5">
//           <div className="w-1/2 hidden md:inline-flex  relative">
//             <Image
//               src="/projectsmate.png"
//               alt="Projects Mate"
//               layout="fill"
//               objectFit="cover"
//               priority
//             />
//           </div>
//           {children}
//         </div>
//       </main>
//     </div>
//   );
// }

"use client";

import { ReactNode, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import Image from "next/image";
import useGetUser from "@/services/auth/getUser";
import Cookies from "js-cookie";
import { getIsTeamMemberFromCookies } from "@/lib/utils";

const isTeam = getIsTeamMemberFromCookies();

const redirectingUrl = isTeam ? "/projects" : "/dashboard";

export default function AuthLayout({ children }: { children: ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  const { data: user, isSuccess } = useGetUser();

  useEffect(() => {
    router.prefetch(redirectingUrl);
  }, [router]);

  useEffect(() => {
    // Check if this is a new signup
    const isNewSignup = Cookies.get("newSignup") === "true";

    // Don't redirect if the user is on the subscription plan page or is a new signup
    if (
      isSuccess &&
      user &&
      !isNewSignup &&
      pathname !== "/subscription-plan"
    ) {
      router.push(redirectingUrl);
    }
  }, [isSuccess, user, router, pathname]);

  return (
    <div className="min-h-screen w-full">
      <main>
        <div className="relative min-h-screen w-full bg-[#EEF2F6]">
          <div className="absolute inset-0 w-full h-full">
            <Image
              src="/projectsmate_new_bg.png"
              alt="Building Pattern"
              layout="fill"
              objectFit="cover"
              priority
            />
          </div>

          <div className="relative flex min-h-screen items-center justify-center px-4">
            {children}
          </div>
        </div>
      </main>
    </div>
  );
}
