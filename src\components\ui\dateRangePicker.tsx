// "use client";

// import * as React from "react";
// import { addDays, format } from "date-fns";
// import { ChevronDown } from "lucide-react";
// import { DateRange } from "react-day-picker";

// import { cn } from "@/lib/utils";
// import { Button } from "@/components/ui/button";
// import { Calendar } from "@/components/ui/calendar";
// import {
//   Popover,
//   PopoverContent,
//   PopoverTrigger,
// } from "@/components/ui/popover";
// import CalendarIcon from "@/components/icons/Calendar";

// interface DatePickerWithRangeProps {
//   className?: string;
//   onChange?: (date: DateRange | undefined) => void;
//   defaultValue?: DateRange;
// }

// export default function DatePickerWithRange({
//   className,
//   onChange,
//   defaultValue,
// }: DatePickerWithRangeProps) {
//   const [date, setDate] = React.useState<DateRange | undefined>(defaultValue);

//   const handleDateChange = (newDate: DateRange | undefined) => {
//     setDate(newDate);
//     onChange?.(newDate);
//   };

//   return (
//     <div className={cn("grid gap-2", className)}>
//       <Popover>
//         <PopoverTrigger asChild>
//           <Button
//             id="date"
//             variant="outline"
//             className={cn(
//               "w-[200px] font-medium border-2 p-3 justify-between text-left text-sm",
//               !date && "text-muted-foreground",
//               "border-feather-gray hover:bg-feather-gray/10 focus:ring-primary",
//             )}
//           >
//             <CalendarIcon className=" size-5 text-primary-blue-B900" />
//             <span className="text-name-title">
//               {date?.from ? (
//                 date.to ? (
//                   <>
//                     {format(date.from, "dd MMM yyyy")} -{" "}
//                     {format(date.to, "dd MMM yyyy")}
//                   </>
//                 ) : (
//                   format(date.from, "dd MMM yyyy")
//                 )
//               ) : (
//                 "Pick a date range"
//               )}
//             </span>
//             <ChevronDown className="h-4 w-4 text-primary" />
//           </Button>
//         </PopoverTrigger>
//         <PopoverContent className="w-auto p-0" align="end">
//           <Calendar
//             mode="range"
//             defaultMonth={date?.from}
//             selected={date}
//             onSelect={handleDateChange}
//             numberOfMonths={2}
//             className="rounded-md border border-feather-gray"
//           />
//         </PopoverContent>
//       </Popover>
//     </div>
//   );
// }

"use client";
import * as React from "react";
import { addDays, format, parseISO } from "date-fns";
import { ChevronDown, X } from "lucide-react";
import { DateRange } from "react-day-picker";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import CalendarIcon from "@/components/icons/Calendar";

interface DatePickerWithRangeProps {
  className?: string;
  onChange?: (date: DateRange | undefined) => void;
  defaultValue?: DateRange;
}

export default function DatePickerWithRange({
  className,
  onChange,
  defaultValue,
}: DatePickerWithRangeProps) {
  const [date, setDate] = React.useState<DateRange | undefined>(defaultValue);

  const handleDateChange = (newDate: DateRange | undefined) => {
    setDate(newDate);
    onChange?.(newDate);
  };

  const handleClearDate = () => {
    setDate(undefined);
    onChange?.(undefined);
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <div
          className={cn(
            "h-9 px-3 py-2 bg-white rounded-lg border border-primary-blue-B900/40 justify-start items-center gap-3 inline-flex overflow-hidden cursor-pointer",
            className,
          )}
        >
          <div className="justify-start items-center gap-1.5 flex">
            <div className="w-5 h-5 relative overflow-hidden">
              <CalendarIcon className=" size-5 text-primary" />
            </div>
            <div className="text-neutrals-G600 text-sm font-normal">
              {date?.from ? (
                date.to ? (
                  <>
                    {format(parseISO(date.from.toISOString()), "dd MMM yyyy")} -{" "}
                    {format(parseISO(date.to.toISOString()), "dd MMM yyyy")}
                  </>
                ) : (
                  format(parseISO(date.from.toISOString()), "dd MMM yyyy")
                )
              ) : (
                "Pick a date range"
              )}
            </div>
          </div>
          {date && (
            <div
              className="w-5 h-5 relative overflow-hidden cursor-pointer"
              onClick={handleClearDate}
            >
              <X className="size-5 text-gray4" />
            </div>
          )}
        </div>
      </PopoverTrigger>
      <PopoverContent className="w-auto p-0" align="end">
        <Calendar
          mode="range"
          defaultMonth={date?.from}
          selected={date}
          onSelect={handleDateChange}
          numberOfMonths={2}
          className="rounded-md border border-feather-gray"
        />
      </PopoverContent>
    </Popover>
  );
}
