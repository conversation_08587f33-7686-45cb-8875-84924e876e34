"use client";
import React, { useState, useEffect } from "react";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Eye, EyeOff } from "lucide-react";
import {
  Form,
  FormItem,
  FormLabel,
  FormControl,
  FormField,
  FormMessage,
} from "@/components/ui/form";
import useGetMeetingConfiguration from "@/services/meetings/getMeetingConfiguration";
import useUpdateMeetingConfiguration from "@/services/meetings/updateMeetingConfiguration";
import { toast } from "sonner";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import z from "zod";
import { useGetMeetings } from "@/services/meetings/getMeetings";
import Loader from "../ui/loader";
import ErrorText from "../ui/errortext";
import MeetingTable from "./MeetingsTable";
import Image from "next/image";

const meetingConfigSchema = z.object({
  calApiKeys: z.string().min(1, ""),
  eventLink: z.string().url(""),
});

type MeetingConfigForm = z.infer<typeof meetingConfigSchema>;

const MeetingDetails = () => {
  const [showPassword, setShowPassword] = useState(false);

  const {
    data: upcomingMeetingsData,
    isLoading: isLoadingUpcoming,
    fetchNextPage: fetchNextUpcoming,
    hasNextPage: hasNextUpcoming,
    isFetchingNextPage: isFetchingNextUpcoming,
  } = useGetMeetings({ status: "pending" });

  const {
    data: completedMeetingsData,
    isLoading: isLoadingCompleted,
    fetchNextPage: fetchNextCompleted,
    hasNextPage: hasNextCompleted,
    isFetchingNextPage: isFetchingNextCompleted,
  } = useGetMeetings({ status: "completed" });

  const {
    data: meetingConfig,
    isLoading: isLoadingConfig,
    isError: isConfigError,
  } = useGetMeetingConfiguration();

  const { mutate: updateConfig, isPending: isUpdating } =
    useUpdateMeetingConfiguration({
      onSuccess: () => {
        toast.success("Meeting configuration updated successfully");
      },
    });

  const form = useForm<MeetingConfigForm>({
    resolver: zodResolver(meetingConfigSchema),
    defaultValues: {
      calApiKeys: "",
      eventLink: "",
    },
  });

  useEffect(() => {
    if (meetingConfig) {
      form.reset({
        calApiKeys: meetingConfig.calApiKeys || "",
        eventLink: meetingConfig.eventLink || "",
      });
    }
  }, [meetingConfig, form.reset]);

  const onSubmit = (data: MeetingConfigForm) => {
    updateConfig(data);
  };

  if (isLoadingConfig) {
    return <Loader />;
  }

  if (isConfigError) {
    return <ErrorText entity="meeting configuration" />;
  }

  return (
    <div className="w-full  space-y-6">
      <div className="bg-white rounded-2xl border p-6 space-y-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center gap-1">
            <h2 className="text-xl font-medium">Meeting Profile</h2>
          </div>
          <Button
            onClick={form.handleSubmit(onSubmit)}
            loading={isUpdating}
            className="flex items-center px-3.5 py-2"
            disabled={!form.formState.isDirty}
          >
            Save Changes
          </Button>
        </div>

        <div className="h-0.5 bg-feather-gray rounded-[146px]" />

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="eventLink"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>CAL URL</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Enter Cal URL"
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="calApiKeys"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>CAL API Key</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          {...field}
                          type={showPassword ? "text" : "password"}
                          placeholder="Enter Cal API Key"
                          value={field.value || ""}
                          className="pr-10 [&::-ms-reveal]:hidden [&::-webkit-inner-spin-button]:appearance-none"
                        />
                        {field.value && (
                          <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
                            onClick={() => setShowPassword(!showPassword)}
                          >
                            {showPassword ? (
                              <EyeOff className="h-4 w-4 text-gray-500" />
                            ) : (
                              <Eye className="h-4 w-4 text-gray-500" />
                            )}
                          </Button>
                        )}
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </form>
        </Form>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div className="bg-white rounded-2xl  border p-6 space-y-4 min-h-[406px]">
          <div className="flex items-center gap-2">
            <Image
              src="/upcoming.svg"
              alt="upcoming-meetings"
              width={26}
              height={26}
            />

            <span className="text-neutrals-G800 text-base font-semibold">
              Upcoming meetings
            </span>
          </div>
          <MeetingTable
            data={upcomingMeetingsData}
            isLoading={isLoadingUpcoming || isFetchingNextUpcoming}
            hasNextPage={hasNextUpcoming}
            fetchNextPage={fetchNextUpcoming}
          />
        </div>

        <div className="bg-white rounded-2xl  border p-6 space-y-4">
          <div className="flex items-center gap-2">
            <Image
              src="/Completed.svg"
              alt="completed-meetings"
              width={26}
              height={26}
            />
            <span className="text-neutrals-G800 text-base font-semibold">
              Completed meetings
            </span>
          </div>
          <MeetingTable
            data={completedMeetingsData}
            isLoading={isLoadingCompleted || isFetchingNextCompleted}
            hasNextPage={hasNextCompleted}
            fetchNextPage={fetchNextCompleted}
          />
        </div>
      </div>
    </div>
  );
};

export default MeetingDetails;
