"use client";

import PageHeader from "@/components/layout/pageHeader/PageHeader";
import NoDataToShow from "@/components/ui/noDataToShow";
import useGetDashboardAnalytics from "@/services/dashboard/getDashboardAnalytics";
import Loader from "@/components/ui/loader";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import DrawingsList from "@/components/dashboardAnalytics/DrawingsList/DrawingsList";
import useGetDashboardAlerts from "@/services/dashboard/getDashboardAlerts";
import ProjectCard from "@/components/dashboardAnalytics/ProjectCard";
import UpcomingPaymentsCard from "@/components/dashboardAnalytics/UpcomingPaymentCard";
import BudgetExceededContent from "@/components/dashboardAnalytics/BudgetExceededProjectCard";
import AnalyticsProjetctSection from "@/components/dashboardAnalytics/AnalyticsProjetctSection";
import useDesignProjectAlerts from "@/services/dashboard/getProjectAnalytics";
import withTeamGuard from "@/components/TeamGuard";

const DashboardPage = () => {
  const { data, isPending } = useGetDashboardAnalytics();
  const { data: dashboardData } = useDesignProjectAlerts();
  const { data: drawingData } = useGetDashboardAlerts("drawing_delayed");

  const delayedDrawingsCount =
    drawingData?.pages?.reduce(
      (total, page) => total + (page?.length || 0),
      0,
    ) || 0;

  if (isPending) return <Loader />;

  console.log(dashboardData, "dashboardData");

  return (
    <div className="flex flex-col h-screen">
      <PageHeader>
        <div>
          <PageHeader.Heading>Dashboard</PageHeader.Heading>
          <PageHeader.Description>
            Keep track of your daily operations.
          </PageHeader.Description>
        </div>
      </PageHeader>
      {/* <div className="flex-1 flex flex-col overflow-auto p-5 scrollbar-hide gap-y-6">
        <div className="grid grid-cols-3 gap-x-2">
          <AnalyticsCard>
            <div className="size-[70px] shrink-0 flex items-center justify-center rounded-xl bg-primary-blue-B40 shadow-lg">
              <InProgress className="w-[43px] h-[42px]" />
            </div>
            <AnalyticsCard.Header>
              <AnalyticsCard.Title>{data?.ongoingProjects}</AnalyticsCard.Title>
              <AnalyticsCard.Content>Ongoing Projects</AnalyticsCard.Content>
            </AnalyticsCard.Header>
          </AnalyticsCard>
          <AnalyticsCard>
            <div className="size-[70px] shrink-0 flex items-center justify-center rounded-xl border border-neutrals-G40 bg-neutrals-G40 shadow-lg">
              <CircleSlash />
            </div>
            <AnalyticsCard.Header>
              <AnalyticsCard.Title>{data?.haltedProjects}</AnalyticsCard.Title>
              <AnalyticsCard.Content> Halted Projects</AnalyticsCard.Content>
            </AnalyticsCard.Header>
          </AnalyticsCard>
          <AnalyticsCard>
            <div className="size-[70px] shrink-0 flex items-center justify-center rounded-xl bg-semantics-success-G40 shadow-lg">
              <Tick className="size-8" />
            </div>
            <AnalyticsCard.Header>
              <AnalyticsCard.Title>
                {data?.completedProjects}
              </AnalyticsCard.Title>
              <AnalyticsCard.Content>Completed Projects</AnalyticsCard.Content>
            </AnalyticsCard.Header>
          </AnalyticsCard>
        </div> */}

      <div className="flex-1 flex flex-col overflow-auto p-5 scrollbar-hide gap-y-6">
        <div className="flex gap-6">
          <AnalyticsProjetctSection
            title="Design Projects"
            imageSrc="/DesignProjects.png"
            ongoing={dashboardData?.ongoing || 0}
            halted={dashboardData?.halted || 0}
            completed={dashboardData?.completed || 0}
          />
          <AnalyticsProjetctSection
            title="Construction Projects"
            imageSrc="/ConstructionProjects.png"
            ongoing={data?.ongoingProjects || 0}
            halted={data?.haltedProjects || 0}
            completed={data?.completedProjects || 0}
          />
        </div>
        <Tabs defaultValue="budget-exceeded-projects" className="w-full flex-1">
          <TabsList className="*:gap-x-1 *:items-center">
            <TabsTrigger value="budget-exceeded-projects">
              {/* count value to be changed after api integration */}
              {dashboardData?.budgetExceededProjects?.length === 1
                ? "Budget Exceeded Project"
                : "Budget Exceeded Projects"}{" "}
              <span className="size-6 rounded-full text-primary bg-blue-3 font-semibold">
                {dashboardData?.budgetExceededProjects?.length}
              </span>
            </TabsTrigger>
            <TabsTrigger value="upcoming-payments">
              {dashboardData?.upcomingPayments?.length === 1
                ? "Upcoming Payment"
                : "Upcoming Payments"}
              <span className="size-6 rounded-full text-primary bg-blue-3 font-semibold">
                {dashboardData?.upcomingPayments?.length}
              </span>
            </TabsTrigger>
            <TabsTrigger value="delayed-constructions">
              <p>
                {data?.projects.length === 1
                  ? "Delayed Construction"
                  : "Delayed Constructions"}
              </p>
              <span className="size-6 rounded-full text-primary bg-blue-3 font-semibold">
                {data?.projects.length}
              </span>
            </TabsTrigger>
            <TabsTrigger value="upcoming-drawings">
              {delayedDrawingsCount === 1
                ? "Upcoming Drawing"
                : "Upcoming Drawings"}{" "}
              <span className="size-6 rounded-full text-primary bg-blue-3 font-semibold">
                {delayedDrawingsCount}
              </span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="delayed-constructions" className="min-h-[50vh]">
            {data?.projects.length === 0 ? (
              <NoDataToShow className="mt-24" />
            ) : (
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {data?.projects?.map((project) => (
                  <ProjectCard key={project.projectId} project={project} />
                ))}
              </div>
            )}
          </TabsContent>
          <TabsContent
            value="upcoming-drawings"
            className=" space-y-3 min-h-[50vh]"
          >
            {/* <div className="flex items-start text-neutrals-G600 text-xs px-3.5 gap-x-4">
              <div className="basis-1/2">MILESTONE</div>
              <div className="basis-1/2">EXPECTED UPLOAD DATE</div>
            </div> */}

            <div className="space-y-1">
              <DrawingsList />
            </div>
          </TabsContent>

          <TabsContent
            value="upcoming-payments"
            className=" space-y-3 min-h-[50vh]"
          >
            <UpcomingPaymentsCard data={dashboardData?.upcomingPayments} />
          </TabsContent>

          <TabsContent
            value="budget-exceeded-projects"
            className=" space-y-3 min-h-[50vh]"
          >
            {dashboardData?.budgetExceededProjects?.length === 0 ? (
              <NoDataToShow className="mt-24" />
            ) : (
              <BudgetExceededContent
                data={dashboardData?.budgetExceededProjects}
              />
            )}
          </TabsContent>
        </Tabs>
      </div>
    </div>
  );
};

export default withTeamGuard(DashboardPage);
