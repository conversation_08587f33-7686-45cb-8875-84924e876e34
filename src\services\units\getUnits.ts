import { useQuery } from "@tanstack/react-query";
import api from "@/lib/api-client";

export type Unit = {
  _id: string;
  name: string;
};

const getUnits = async (): Promise<Unit[]> => {
  const response = await api.get("/common/units");
  return response.data;
};

const useGetUnits = () => {
  return useQuery({
    queryKey: ["units"],
    queryFn: getUnits,
  });
};

export default useGetUnits;
