"use client";
import PageHeader from "@/components/layout/pageHeader/PageHeader";
import AddNewItem from "@/components/teamVault/AddNewItem";
import ContentSection from "@/components/teamVault/ContentSection";
import { useState } from "react";

function Page() {
  const [refetchItems, setRefetchItems] = useState(false);
  const [selectedFolder, setSelectedFolder] = useState<string>("root");
  const handleSelectedFolder = (folderId: string) => {
    setSelectedFolder(folderId);
  };
  const handleRefetchItems = () => {
    setRefetchItems((prev) => !prev);
  };
  return (
    <div className="flex flex-col h-screen">
      <PageHeader>
        <div className="flex flex-col">
          <PageHeader.Heading>Team Vault</PageHeader.Heading>
          <PageHeader.Description>
            Access shared files and documents across your organization. All
            files are securely stored and permission-controlled.
          </PageHeader.Description>
        </div>
        <AddNewItem
          handleRefetchItems={handleRefetchItems}
          selectedFolder={selectedFolder}
        />
      </PageHeader>
      <div className="flex-1 overflow-y-auto p-4 mt-5">
        <ContentSection
          selectedFolder={selectedFolder}
          handleSelectedFolder={handleSelectedFolder}
          refetchItems={refetchItems}
        />
      </div>
    </div>
  );
}

export default Page;
