import * as React from "react";

const Time = ({ className }: { className?: string }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="17"
    height="17"
    fill="none"
    viewBox="0 0 17 17"
    className={className}
  >
    <g
      stroke="currentColor"
      strokeLinecap="round"
      strokeLinejoin="round"
      clipPath="url(#clip0_2489_2852)"
    >
      <path d="M8.25 15.89a7.39 7.39 0 1 0 0-14.78 7.39 7.39 0 0 0 0 14.78"></path>
      <path
        strokeWidth="0.5"
        d="M8.25 9.016a.516.516 0 1 0 0-1.032.516.516 0 0 0 0 1.032"
      ></path>
      <path d="M8.766 8.5h6.875m-7.754.363-4.863 4.863"></path>
    </g>
    <defs>
      <clipPath id="clip0_2489_2852">
        <path fill="#fff" d="M0 .25h16.5v16.5H0z"></path>
      </clipPath>
    </defs>
  </svg>
);

export default Time;
