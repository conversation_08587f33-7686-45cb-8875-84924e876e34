import React from "react";

const DailyUpdateIcon: React.FC<{ className?: string }> = ({ className }) => (
  <svg
    width="218px"
    height="218px"
    viewBox="0 0 48.00 48.00"
    id="a"
    xmlns="http://www.w3.org/2000/svg"
    fill="none"
    strokeWidth={3.6}
    transform="matrix(1, 0, 0, 1, 0, 0)rotate(0)"
    className={className}
  >
    <g id="SVGRepo_bgCarrier" strokeWidth={0} />
    <g
      id="SVGRepo_tracerCarrier"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <g id="SVGRepo_iconCarrier">
      <defs>
        {/* <style>
          {
            ".f{fill:none;stroke:#A3A5B2;stroke-linecap:round;stroke-linejoin:round;}"
          }
        </style> */}
      </defs>
      <path
        id="b"
        stroke="currentColor"
        d="M13.9138,22.6411l10.6346,9.8405L40.891,15.7128"
      />
      <path
        id="c"
        stroke="currentColor"
        d="M41.8921,31.2658c-2.1603,9.3573-11.1657,15.4697-20.6595,14.0226-9.4938-1.4471-16.2692-9.9649-15.5436-19.5409,.7256-9.576,8.7072-16.9756,18.3106-16.9756"
      />
      <path
        id="d"
        className="f"
        d="M24.1806,2.5001l6.7473,6.3151-6.9282,6.7443"
      />
      <path id="e" className="f" d="M23.9996,8.7719l6.9282,.0434" />
    </g>
  </svg>
);

export default DailyUpdateIcon;
