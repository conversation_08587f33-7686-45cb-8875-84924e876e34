import React, { useEffect } from "react";
import { useInView } from "react-intersection-observer";

import ProjectCard from "@/components/dashboard/ProjectCard";
import NoDataToShow from "@/components/ui/noDataToShow";
import Loader from "@/components/ui/loader";
import ErrorText from "@/components/ui/errortext";
import useGetProjects from "@/services/project/getProjects";
import { Status } from "@/types/Project";

const ITEMS_PER_PAGE = 30;

type SelectValue = Status | "all" | "";

interface ProjectsListProps {
  searchTerm: string;
  select: SelectValue;
}

const ProjectsList: React.FC<ProjectsListProps> = React.memo(
  ({ searchTerm, select }) => {
    const { ref, inView } = useInView();

    const {
      data: projects,
      isLoading,
      isError,
      fetchNextPage,
      hasNextPage,
      isFetchingNextPage,
    } = useGetProjects({
      searchTerm,
      select,
      itemsPerPage: ITEMS_PER_PAGE,
    });

    useEffect(() => {
      if (inView && hasNextPage && !isFetchingNextPage) {
        fetchNextPage();
      }
    }, [inView, fetchNextPage, hasNextPage, isFetchingNextPage]);

    if (isLoading) return <Loader />;
    if (isError) return <ErrorText entity="projects" />;

    if (!projects || projects.length === 0) {
      return (
        <div className="justify-center align-center flex">
          <NoDataToShow />
        </div>
      );
    }

    return (
      <>
        <div className="grid grid-cols-2 gap-3">
          {projects.map((project) => (
            <ProjectCard key={project._id} {...project} />
          ))}
        </div>
        <div ref={ref} className="w-full h-20 flex items-center justify-center">
          {isFetchingNextPage && <Loader size={10} />}
        </div>
      </>
    );
  },
);

ProjectsList.displayName = "ProjectsList";

export default ProjectsList;
