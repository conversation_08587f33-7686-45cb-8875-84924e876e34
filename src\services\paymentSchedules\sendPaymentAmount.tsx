"use client";

import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";
// import { toast } from "sonner";

type SendPaymentRequestInput = {
  scheduleId: string;
  amount: string;
  percentage: string;
};

export const useSendPaymentRequest = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: SendPaymentRequestInput) => {
      const res = await api.put("/payment-schedule/sendRequest", data);
      return res.data;
    },
    onSuccess: (_data, variables) => {
      queryClient.invalidateQueries({
        queryKey: ["paymentSchedules"],
      });

      queryClient.invalidateQueries({
        queryKey: ["paymentSchedule", variables.scheduleId],
      });
    },
    // onError: (error) => {
    //   toast.error(error.message);
    //   console.log(error,"errro from pay")
    // },
  });
};
