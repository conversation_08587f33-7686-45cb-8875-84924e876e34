"use client";

import { usePara<PERSON>, useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

import Search from "@/components/icons/Search";
import { Input } from "@/components/ui/input";
import DesignStageCard from "@/components/designStage/DesignStageCard";
import useGetDesignStages from "@/services/designStage/getDesignStages";
import Loader from "@/components/ui/loader";
import ErrorText from "@/components/ui/errortext";
import NoDataToShow from "@/components/ui/noDataToShow";
import AddStage from "@/components/designStage/AddStage";
import { CreatedMessage } from "@/types/Socket";
import { socket } from "@/lib/socket";
import { useSocket } from "@/hooks/useSocket";
import { useChatStore } from "@/store/useChatStore";
import BudgetDetails from "@/components/designStage/BudgetDetails";
import { useAuth } from "@/app/contexts/AuthContext";

const DesignStages = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const searchTerm = searchParams.get("search") || "";

  const { id } = useParams() as { id: string };

  const [search, setSearch] = useState(searchTerm);

  const { connectSocket, disconnectSocket } = useSocket();

  const messages = useChatStore((state) => state.messages);
  const updateMessages = useChatStore((state) => state.updateMessages);

  const { data, isPending, isError } = useGetDesignStages({
    projectId: id,
    search: searchTerm,
  });

  console.log(data, "projectId");

  const handleSearch = () => {
    router.push(`?search=${search}`);
  };

  useEffect(() => {
    const onNewMessage = (newMessage: CreatedMessage) => {
      console.log("newMessage", newMessage);
      const groupId = newMessage.groupId;

      if (!groupId) {
        return console.error("No groupId found");
      }

      const prevMessagesInGroupId = messages?.[groupId];

      if (!prevMessagesInGroupId) {
        updateMessages({
          ...messages,
          [groupId]: [newMessage],
        });
        return;
      }

      updateMessages({
        ...messages,
        [groupId]: [...prevMessagesInGroupId, newMessage],
      });
    };

    socket.on("newMessage", onNewMessage);
    return () => {
      socket.off("newMessage");
    };
  }, [messages, updateMessages]);

  useEffect(() => {
    connectSocket();

    return () => {
      disconnectSocket();
    };
  });

  const { isTeamMember: isTeam, isAdmin } = useAuth();

  if (isTeam === null) return null;

  if (isPending) return <Loader />;
  if (isError) return <ErrorText entity="design stages" />;

  console.log("messages from store:", messages);
  return (
    <div className="flex flex-col gap-y-3">
      <div className="flex justify-between items-center py-3.5 border-b border-neutrals-G40">
        <div className="space-y-1 text-neutrals-G900">
          <h4 className="font-semibold">Design Stages</h4>
          <p className="text-xs text-neutrals-G300">
            You have created Manage all the design stages over here.
          </p>
        </div>
        {(!isTeam || isAdmin) && <AddStage projectId={id} />}
      </div>
      <div className="flex-1 overflow-y-auto scrollbar-hide">
        <div className="w-full flex justify-between">
          <form
            onSubmit={(e) => {
              e.preventDefault();
              handleSearch();
            }}
            className="flex w-fit bg-neutrals-G40 rounded-lg overflow-hidden mb-4"
          >
            <Input
              type="search"
              placeholder="Search by name or ID"
              className="w-1/4 min-w-[297px] h-[2.25rem] border-none bg-inherit placeholder:text-neutrals-G400 placeholder:text-sm placeholder:font-normal"
              value={search}
              onChange={(e) => setSearch(e.currentTarget.value)}
            />
            <button className="pr-3 py-2">
              <Search />
            </button>
          </form>

          {(!isTeam || isAdmin) && <BudgetDetails projectId={id} />}
        </div>
        {data.length === 0 ? (
          <NoDataToShow />
        ) : (
          <div className="space-y-[18px]">
            {data.map((stage) => (
              <DesignStageCard
                key={stage._id}
                stage={stage}
                projectId={id}
                isTeam={isTeam}
                isAdmin={isAdmin}
              />
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default DesignStages;
