import { useQuery } from "@tanstack/react-query";

import api from "@/lib/api-client";
import { QueryConfig } from "@/lib/react-query";
import { getItemsFromTeamVaultResponse } from "@/types/TeamVault";

const getItemsFromTeamVault = async (
  folderId: string,
): Promise<getItemsFromTeamVaultResponse> => {
  const { data } = await api({
    url: `/vault/folder/${folderId}/contents`,
    method: "GET",
  });
  return data;
};

const useGetItemsFromTeamVault = (
  args: string,
  queryKeys: string[],
  queryConfig?: QueryConfig<any>,
) => {
  return useQuery({
    queryKey: queryKeys,
    queryFn: () => getItemsFromTeamVault(args),
    ...queryConfig,
  });
};

export default useGetItemsFromTeamVault;
