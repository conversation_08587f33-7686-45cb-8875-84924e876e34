import React from "react";
import { Map<PERSON><PERSON>, Star } from "lucide-react";
import { Contractor } from "@/types/Contractor";

type ContractorCardProps = {
  contractor: Contractor;
};

const ContractorCard: React.FC<ContractorCardProps> = ({ contractor }) => {
  // Static data for fields not available in API
  const staticRating = 4.2;
  const staticLocation = "Kochi, India";
  const staticProjectTypes = ["#ID 010111", "Evaluation", "Commercial"];

  return (
    <div className="cursor-pointer bg-white rounded-xl flex flex-col justify-start overflow-hidden border border-neutrals-G40">
      <div className="bg-primary-blue-B30 px-6 pt-5 pb-[23px] flex justify-between items-end gap-3">
        <div className="space-y-1">
          <h3 className="text-neutrals-G900 text-xl font-semibold">
            {contractor.contractorName}
          </h3>
          <div className="flex gap-1 text-neutrals-G600 text-xs">
            <div className="p-1 flex rounded border border-neutrals-G50 bg-neutrals-G30">
              <MapPin className="size-3.5 mr-1" />
              {staticLocation}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-1 mb-1">
          <span className="text-neutrals-G900 text-sm font-medium">
            {staticRating}
          </span>
          <Star className="size-4 fill-yellow-400 text-yellow-400" />
        </div>
      </div>
      <div className="px-6 pt-2 pb-5">
        <div className="space-y-2">
          <p className="text-[0.625rem] text-neutrals-G600">Project types</p>
          <div className="flex flex-wrap gap-1">
            {staticProjectTypes.map((type, index) => (
              <div
                key={index}
                className="p-1 rounded border border-neutrals-G50 bg-neutrals-G30 text-xs text-neutrals-G600"
              >
                {type}
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContractorCard;
