import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { MeetingConfiguration } from "@/types/Meetings";

interface UpdateMeetingConfigurationProps {
  calApiKeys: string;
  eventLink: string;
  onSuccess?: () => void;
}

const updateMeetingConfiguration = async ({
  calApiKeys,
  eventLink,
}: UpdateMeetingConfigurationProps): Promise<MeetingConfiguration> => {
  const response = await api.put("/meetings/meeting-configuration", {
    calApiKeys,
    eventLink,
  });
  return response.data;
};

const useUpdateMeetingConfiguration = ({
  onSuccess,
}: { onSuccess?: () => void } = {}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateMeetingConfiguration,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["meeting-configuration"] });
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message ||
          "Failed to update meeting configuration",
      );
    },
  });
};

export default useUpdateMeetingConfiguration;
