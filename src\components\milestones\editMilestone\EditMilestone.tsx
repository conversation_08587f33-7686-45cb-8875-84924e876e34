import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { toast } from "sonner";
import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import MilestoneFormFields from "../formFields/Milestone";
import { Form } from "@/components/ui/form";
import { milestoneSchema } from "@/schema/milestone";
import useUpdateMilestone from "@/services/milestone/updateMilestone";
import { useParams } from "next/navigation";
import { useEffect, useState } from "react";
import { Milestone } from "@/types/Milestone";
import DeleteMilestone from "../deleteMilestone/DeleteMilestone";
import Settings from "@/components/icons/Settings";

type EditMilestoneProps = { milestone: Milestone };

const EditMilestone = ({ milestone }: EditMilestoneProps) => {
  const { id } = useParams() as { id: string };
  const [isOpen, setIsOpen] = useState(false);

  const calculateEndDate = (startDate?: string, duration?: number) => {
    if (!startDate || duration === undefined) return undefined;
    return new Date(
      new Date(startDate).getTime() + duration * 24 * 60 * 60 * 1000,
    );
  };

  const getDefaultValues = () => {
    const defaultValues: Partial<z.infer<typeof milestoneSchema>> = {
      duration: milestone.duration ?? -1,
      isCustom: milestone.isCustom ?? false,
    };

    if (milestone.isCustom) {
      defaultValues.milestoneName = milestone.milestoneName ?? "";
    } else {
      defaultValues.milestoneTemplateId = milestone.milestoneTemplateId?._id;
    }

    if (milestone.startDate) {
      defaultValues.startDate = new Date(milestone.startDate);
      const endDate = calculateEndDate(milestone.startDate, milestone.duration);
      if (endDate) {
        defaultValues.endDate = endDate;
      }
    }

    return defaultValues;
  };

  const form = useForm<z.infer<typeof milestoneSchema>>({
    resolver: zodResolver(milestoneSchema),
    defaultValues: getDefaultValues(),
  });

  const handleUpdateMilestoneSuccess = () => {
    setIsOpen(false);
    toast.success("Milestone updated successfully");
  };

  const { mutate: updateMilestone, isPending: isPendingUpdateMilestone } =
    useUpdateMilestone(handleUpdateMilestoneSuccess);

  const onSubmit = (data: z.infer<typeof milestoneSchema>) => {
    updateMilestone({
      projectId: id,
      milestoneId: milestone._id,
      startDate: data.startDate?.toLocaleDateString("en-CA"),
      duration: data.duration ?? undefined,
    });
  };

  useEffect(() => {
    form.reset(getDefaultValues());
  }, [milestone, form]);

  return (
    <Dialog
      open={isOpen}
      onOpenChange={(open) => {
        if (!open) {
          form.reset();
        }
        setIsOpen(open);
      }}
    >
      <DialogTrigger asChild>
        <Button
          variant="ghost"
          className="gap-x-1.5 px-4 text-[#49515B] font-medium hover:bg-inherit hover:text-[#49515B] p-0 [&>svg]:size-5"
        >
          Milestone Settings <Settings />
        </Button>
      </DialogTrigger>
      <DialogContent isCloseIconVisible className="gap-y-[30px]">
        <DialogTitle>Milestone Settings</DialogTitle>

        <Form {...form}>
          <form
            onSubmit={form.handleSubmit(onSubmit)}
            className="flex flex-col gap-y-8"
          >
            <MilestoneFormFields isEdit form={form} />
            <div className="flex gap-x-4 justify-between">
              <DeleteMilestone
                milestone={milestone}
                onSuccess={() => {
                  setIsOpen(false);
                }}
              />
              <Button
                loading={isPendingUpdateMilestone}
                disabled={!form.formState.isDirty}
                className="w-[65px] px-4"
              >
                Save
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
};

export default EditMilestone;
