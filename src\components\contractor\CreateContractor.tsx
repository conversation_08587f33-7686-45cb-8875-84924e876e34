import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON>T<PERSON>ger,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormMessage,
  FormItem,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import useCreateContractor from "@/services/contractor/createContractor";

const createContractorSchema = z.object({
  name: z.string().min(1, ""),
  email: z.string().min(1, "").email("Please enter a valid email"),
  organisationName: z.string().min(1, ""),
});

type CreateContractorFormData = z.infer<typeof createContractorSchema>;

export function CreateContractor() {
  const [open, setOpen] = useState(false);

  const form = useForm<CreateContractorFormData>({
    resolver: zod<PERSON><PERSON><PERSON><PERSON>(createContractorSchema),
    defaultValues: {
      name: "",
      email: "",
      organisationName: "",
    },
  });

  const { mutate: createContractor, isPending } = useCreateContractor();

  const onSubmit = (data: CreateContractorFormData) => {
    createContractor(data, {
      onSuccess: () => {
        setOpen(false);
        form.reset();
      },
    });
  };

  const handleCancel = () => {
    setOpen(false);
    form.reset();
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="w-[13rem] h-12">Create Contractor</Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle className="text-center text-2xl mb-8">
            Create Contractor
          </DialogTitle>
        </DialogHeader>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input placeholder="Enter name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input placeholder="Enter email" type="email" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="organisationName"
              render={({ field }) => (
                <FormItem>
                  <FormControl>
                    <Input placeholder="Enter organisation name" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end gap-4 pt-6">
              <Button
                type="button"
                variant="outline"
                className="w-1/2"
                onClick={handleCancel}
              >
                Cancel
              </Button>
              <Button type="submit" loading={isPending} className="w-1/2">
                Create contractor
              </Button>
            </div>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
