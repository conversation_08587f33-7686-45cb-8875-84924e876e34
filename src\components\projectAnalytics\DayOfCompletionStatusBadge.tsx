import { ComponentProps } from "react";
import { differenceInDays } from "date-fns";

import { Badge } from "../ui/badge";

type DayOfCompletionProps = ComponentProps<typeof Badge> & {
  projectedEndDate: string;
  expectedEndDate: string;
};

const getDifferenceInDays = (
  expectedEndDate: string,
  projectedEndDate: string,
) => {
  return differenceInDays(expectedEndDate, projectedEndDate);
};

export const checkStatus = (day: number) => {
  if (day === 0) return "On time";
  if (day < 0) return "behind";
  return "upfront";
};

export const getStatusResult = (day: number, status: string) => {
  const dayString = Math.abs(day) === 1 ? "Day" : "Days";

  if (status === "On time") return "On time";

  if (status === "behind") return `${Math.abs(day)} ${dayString} behind`;

  return `${day} ${dayString} upfront`;
};

const DayOfCompletionStatusBadge = ({
  projectedEndDate,
  expectedEndDate,
  ...props
}: DayOfCompletionProps) => {
  const difference = getDifferenceInDays(expectedEndDate, projectedEndDate);
  const status = checkStatus(difference);
  const statusResult = getStatusResult(difference, status);

  return (
    <Badge
      variant={
        status === "On time" || status === "upfront" ? "default" : "destructive"
      }
      size="sm"
      {...props}
    >
      {statusResult}
    </Badge>
  );
};

export default DayOfCompletionStatusBadge;
