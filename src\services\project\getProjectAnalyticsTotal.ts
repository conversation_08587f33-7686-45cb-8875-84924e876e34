import { useQuery } from "@tanstack/react-query";
import api from "@/lib/api-client";
import { ProjectAnalyticsTotal } from "@/types/Project";

const getProjectAnalyticsTotal = async (
  id: string,
): Promise<ProjectAnalyticsTotal> => {
  const url = `/analytics/project-analytics-architect-total/${id}`;

  const response = await api.get(url);
  return response.data;
};

const useGetProjectAnalyticsTotal = (id: string) => {
  return useQuery({
    queryKey: ["project-analytics-total", id],
    queryFn: () => getProjectAnalyticsTotal(id),
    enabled: !!id,
  });
};

export default useGetProjectAnalyticsTotal;
