// TODO: remove use client later
"use client";

import Tick from "../icons/Tick";
import Tick3 from "../icons/Tick3";
import { Button } from "../ui/button";
import {
  Timeline,
  TimelineDot,
  TimelineHeading,
  TimelineItem,
  TimelineLine,
} from "../ui/timeline";
import PaymentRequestForm from "./PaymentRequestForm";
import { PaymentSchedule } from "@/services/paymentSchedules/getPaymentSchedules";
import { useVerifyPayment } from "@/services/paymentSchedules/verifyPaymentSchedules";

type PaymentStatus =
  | "upload-invoice"
  | "invoice-uploaded"
  | "verify-payment"
  | "payment-completed";

const PaymentStatusCard = ({
  schedule,
  totalBudget,
}: {
  schedule: PaymentSchedule;
  totalBudget: number;
}) => {
  const { mutate: verifyPayment, isPending } = useVerifyPayment();
  console.log(schedule, "data8");

  const getPaymentStatus = (
    status: PaymentSchedule["paymentStatus"],
  ): PaymentStatus => {
    if (status.verifyPayment) return "payment-completed";

    if (
      status.invoiceUploaded &&
      status.verifyClientPayment &&
      status.verifyPayment
    ) {
      return "verify-payment";
    }
    if (status.invoiceUploaded && status.verifyClientPayment) {
      return "verify-payment";
    }

    return "upload-invoice";
  };

  const paymentStatus: PaymentStatus = getPaymentStatus(
    schedule?.paymentStatus,
  );

  return (
    <div className="border border-neutrals-G40 rounded-[8px]">
      <div className="pt-5 pl-6 pr-8 pb-8 flex justify-between gap-x-3 lg:gap-x-[78px]">
        <h5 className="font-semibold text-neutrals-G600 text-xl">
          Payment Status
        </h5>
        <Timeline className="flex-1 mt-[5px]">
          <TimelineItem>
            <TimelineHeading
              status={paymentStatus !== "upload-invoice" ? "done" : undefined}
            >
              Invoice upload
            </TimelineHeading>
            <TimelineDot status={"done"} />
            <TimelineLine
              status={
                [
                  "verify-client-payment",
                  "verify-payment",
                  "payment-completed",
                ].includes(paymentStatus)
                  ? "done"
                  : undefined
              }
              side="right"
            />
          </TimelineItem>
          <TimelineItem className="flex-1">
            <TimelineLine
              status={
                [
                  "verify-client-payment",
                  "verify-payment",
                  "payment-completed",
                ].includes(paymentStatus)
                  ? "done"
                  : undefined
              }
            />
            <TimelineHeading
              status={
                [
                  "verify-client-payment",
                  "verify-payment",
                  "payment-completed",
                ].includes(paymentStatus)
                  ? "done"
                  : undefined
              }
            >
              Verify client payment
            </TimelineHeading>
            <TimelineDot
              status={
                [
                  "verify-client-payment",
                  "verify-payment",
                  "payment-completed",
                ].includes(paymentStatus)
                  ? "done"
                  : undefined
              }
            />
            <TimelineLine
              side="right"
              status={
                ["payment-completed"].includes(paymentStatus)
                  ? "done"
                  : undefined
              }
            />
          </TimelineItem>
          <TimelineItem>
            <TimelineLine
              status={
                paymentStatus === "payment-completed" ? "done" : undefined
              }
            />
            <TimelineHeading
              status={
                paymentStatus === "payment-completed" ? "done" : undefined
              }
            >
              Paid
            </TimelineHeading>
            <TimelineDot
              status={
                paymentStatus === "payment-completed" ? "done" : undefined
              }
            />
          </TimelineItem>
        </Timeline>
      </div>
      <hr className="h-[1px] bg-neutrals-G40 mx-7" />
      <div className="pt-8 pl-6 pr-[37px] pb-9 flex justify-between items-center gap-x-12">
        {paymentStatus === "upload-invoice" ||
        paymentStatus === "invoice-uploaded" ? (
          <>
            <div className="basis-[24%] space-y-2 text-neutrals-G600">
              <h6 className="font-semibold">Invoice upload</h6>
              <p className="text-sm">
                Upload the invoice and send it to the client for approval and
                payment processing.
              </p>
            </div>
            <PaymentRequestForm
              className="basis-3/5"
              scheduleId={schedule?._id}
              defaultAmount={schedule?.amount}
              defaultPercentage={schedule?.percentage}
              totalBudget={totalBudget}
              invoice={schedule?.invoice}
            />
          </>
        ) : paymentStatus === "verify-payment" ? (
          <>
            <div className="flex gap-x-2 items-start">
              <Tick3 />
              <div className="space-y-2 text-neutrals-G600">
                <h6 className="font-semibold">Verify Client Payment</h6>
                <p className="text-sm">
                  The client has marked the payment as paid. Please verify the
                  payment to confirm completion.
                </p>
              </div>
            </div>
            <Button
              className="px-6"
              onClick={() => verifyPayment(schedule._id)}
              disabled={isPending}
            >
              {isPending ? "verifying..." : "Verify payment"}
            </Button>
          </>
        ) : (
          <>
            <div className="flex gap-x-2 items-start">
              <Tick className="size-[54px]" />
              <div className="space-y-2 text-neutrals-G600">
                <h6 className="font-semibold">Payment completed</h6>
                <p className="text-sm">
                  Client payment has been successfully completed and confirmed.
                </p>
              </div>
            </div>
            <div className="h-9 flex justify-between items-center text-neutrals-G600 gap-x-6 text-sm px-4 py-1.5 rounded-lg border border-neutrals-G40">
              Amount received:{" "}
              <span className="text-neutrals-G800 font-medium">
                Rs {schedule?.amount} /-
              </span>
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default PaymentStatusCard;
