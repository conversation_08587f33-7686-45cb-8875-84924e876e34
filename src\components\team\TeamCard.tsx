import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import ProfileIcon from "../icons/Profile";
import Message from "../icons/Message";
import Phone from "../icons/Phone";
import Worklog from "./Worklog";
import TeamCardDropdownMenu from "./TeamCardDropdownMenu";
import { TeamMember } from "@/types/TeamMember";

type TeamCardProps = {
  data: TeamMember;
};

const TeamCard = ({ data }: TeamCardProps) => {
  return (
    <div className="rounded-xl border border-neutrals-G40 bg-white overflow-hidden">
      <div className="p-4 relative bg-primary-blue-B30 flex gap-x-4 items-start">
        <Avatar className="size-[87px]">
          <AvatarImage
            src={data.profileImage || undefined}
            alt={`profile picture of ${data.name}`}
          />
          <AvatarFallback>
            <ProfileIcon className="size-3/4" />
          </AvatarFallback>
        </Avatar>
        <div className="space-y-2">
          <h5 className="text-xl font-semibold text-neutrals-G900 line-clamp-1">
            {data.name}
          </h5>
          <div className="space-y-2 text-neutrals-G600 text-xs">
            <div className="flex gap-x-1 items-start [&>svg]:shrink-0">
              <Message />
              <p className="break-all">{data.email}</p>
            </div>
            <div className="flex gap-x-1">
              <Phone />
              <p className="line-clamp-1 break-all">{data.phone}</p>
            </div>
          </div>
        </div>
        <TeamCardDropdownMenu data={data} />
      </div>
      <div className="p-4 flex gap-x-2 justify-between items-end">
        <div className="space-y-2">
          <p className="text-[0.625rem] text-neutrals-G600">Role</p>
          <h6 className="font-medium text-sm text-neutrals-G800">
            {data.role}
          </h6>
        </div>
        <div className="space-y-2">
          <p className="text-[0.625rem] text-neutrals-G600">Salary</p>
          <h6 className="font-medium text-sm text-neutrals-G800">
            ₨ {data.salary}
          </h6>
        </div>
        <Worklog member={data} />
      </div>
    </div>
  );
};

export default TeamCard;
