import { z } from "zod";

import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { designTeamSchema } from "@/schema/designTeam";

type AddDesignTeamMember = z.infer<typeof designTeamSchema>;

const addDesignTeamMember = async ({
  projectId,
  userId,
}: AddDesignTeamMember) => {
  const response = await api.post(
    `/design-team/add-design-team/${projectId}/${userId}`,
  );
  return response.data;
};

const useAddDesignTeamMember = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: addDesignTeamMember,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["design-team"] });
      onSuccess?.();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to add design team",
      );
    },
  });
};

export default useAddDesignTeamMember;
