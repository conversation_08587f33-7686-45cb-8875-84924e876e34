import { useQuery } from "@tanstack/react-query";
import api from "@/lib/api-client";

const getDesignStageAnalytics = async (projectId: string) => {
  const response = await api({
    url: `/design-stage/get-analytics/${projectId}`,
    method: "GET",
  });

  return response.data;
};

const useDesignStageAnalytics = (projectId: string, enabled = true) => {
  return useQuery({
    queryKey: ["design-stage-budget", projectId],
    queryFn: () => getDesignStageAnalytics(projectId),
    enabled: !!projectId && enabled,
  });
};

export default useDesignStageAnalytics;
