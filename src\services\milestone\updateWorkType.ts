import { toast } from "sonner";

import api from "@/lib/api-client";
import { useMutation, useQueryClient } from "@tanstack/react-query";

export type UpdateWorkType = {
  projectId: string;
  worktypeId: string;
  // worktypeTemplateId: string;
  startDate: string | undefined;
  duration: number | undefined;
  totalQuantity: number | undefined;
  unit: string | undefined;
  totalQuantityOptionalUnit?: number;
  optionalUnit?: string;
  // worktypeOrder: number | undefined;
};

const updateWorkType = async ({
  projectId,
  worktypeId,
  ...body
}: UpdateWorkType) => {
  const response = await api({
    url: `/worktypes/${projectId}/${worktypeId}`,
    method: "PUT",
    data: body,
  });
  return response.data;
};

const useUpdateWorkType = (onSuccess?: () => void) => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: updateWorkType,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["worktypes"] });
      if (onSuccess) onSuccess();
    },
    onError: (error: any) => {
      toast.error(
        error?.response?.data?.message || "Failed to update work type",
      );
    },
  });
};

export default useUpdateWorkType;
