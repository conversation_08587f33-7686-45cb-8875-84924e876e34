import { useInfiniteQuery } from "@tanstack/react-query";
import api from "@/lib/api-client";
import { Notification } from "@/types/Notifications";

type UseGetNotificationsParams = {
  limit?: number;
  enabled?: boolean;
};

type UseGetNotificationsReturn = {
  data: Notification[];
  isPending: boolean;
  isFetchingNextPage: boolean;
  hasNextPage: boolean;
  isError: Error | null;
  fetchNextPage: () => Promise<any>;
  refetch: () => Promise<any>;
};

const useGetNotifications = ({
  limit = 10,
  enabled = true,
}: UseGetNotificationsParams = {}): UseGetNotificationsReturn => {
  const query = useInfiniteQuery({
    queryKey: ["notifications", { limit }],
    queryFn: async ({ pageParam = 1 }) => {
      try {
        const params = new URLSearchParams({
          page: pageParam.toString(),
          limit: limit.toString(),
        });

        const response = await api.get<Notification[]>(
          `/notifications?${params.toString()}`,
        );

        return {
          data: response.data,
          page: pageParam,
          limit,
          totalCount: response.data.length,
        };
      } catch (error) {
        throw error;
      }
    },
    getNextPageParam: (lastPage) => {
      if (!lastPage || lastPage.data.length < limit) return undefined;
      return lastPage.page + 1;
    },
    initialPageParam: 1,
    enabled,
  });

  const data = query.data?.pages.flatMap((page) => page.data) ?? [];

  return {
    data,
    isPending: query.isPending,
    isFetchingNextPage: query.isFetchingNextPage,
    hasNextPage: query.hasNextPage,
    isError: query.error,
    fetchNextPage: query.fetchNextPage,
    refetch: query.refetch,
  };
};

export default useGetNotifications;
