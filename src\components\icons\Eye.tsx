import { cn } from "@/lib/utils";

const Eye = ({ className }: { className?: string }) => {
  return (
    <svg
      className={cn(
        "relative outline shrink-0 outline-[#F8FCFA] outline-[0.5px] size-3 rounded-full bg-primary-blue-B900 stroke-white",
        className,
      )}
      xmlns="http://www.w3.org/2000/svg"
      width="11"
      height="12"
      viewBox="0 0 11 12"
      fill="none"
    >
      <path
        d="M9.87368 5.56262C10.013 5.75787 10.0827 5.85595 10.0827 6.00033C10.0827 6.14516 10.013 6.24278 9.87368 6.43803C9.2476 7.3162 7.64847 9.20866 5.49935 9.20866C3.34977 9.20866 1.7511 7.31574 1.12502 6.43803C0.985682 6.24278 0.916016 6.1447 0.916016 6.00033C0.916016 5.85549 0.985682 5.75787 1.12502 5.56262C1.7511 4.68445 3.35022 2.79199 5.49935 2.79199C7.64893 2.79199 9.2476 4.68491 9.87368 5.56262Z"
        strokeWidth="0.696429"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
      <path
        d="M6.875 6C6.875 5.63533 6.73013 5.28559 6.47227 5.02773C6.21441 4.76987 5.86467 4.625 5.5 4.625C5.13533 4.625 4.78559 4.76987 4.52773 5.02773C4.26987 5.28559 4.125 5.63533 4.125 6C4.125 6.36467 4.26987 6.71441 4.52773 6.97227C4.78559 7.23013 5.13533 7.375 5.5 7.375C5.86467 7.375 6.21441 7.23013 6.47227 6.97227C6.73013 6.71441 6.875 6.36467 6.875 6Z"
        strokeWidth="0.696429"
        stroke-linecap="round"
        stroke-linejoin="round"
      />
    </svg>
  );
};

export default Eye;
